package com.meerkat.crdt.report.service;

import com.meerkat.crdt.report.enums.ReportSyncStateEnum;
import com.meerkat.crdt.report.param.ReportSync;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ReportSyncService.java
 * @Description TODO
 * @createTime 2022-02-16 17:44:00
 */
public interface ReportSyncService {

    /**
     * 同步体检报告
     * @param reportSyncParams
     */
    void reportSync(List<ReportSync> reportSyncParams);

    /**
     * 获取机构报告同步数据
     * @param orgaId
     * @param syncStateEnumList
     * @return
     */
    List<ReportSync> listReportSync(Long orgaId, List<ReportSyncStateEnum> syncStateEnumList);

    /**
     * 更新同步状态
     * @param ids
     * @param stateEnum
     */
    void updateSyncState(List<Long> ids, ReportSyncStateEnum stateEnum);
}
