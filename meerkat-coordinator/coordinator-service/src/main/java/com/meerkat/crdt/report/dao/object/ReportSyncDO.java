package com.meerkat.crdt.report.dao.object;

import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName ReportSyncDO.java
 * @Description TODO
 * @createTime 2022-02-16 19:55:00
 */
public class ReportSyncDO {

    private Long id;

    /**
     * 机构id
     */
    private Long organizationId;

    /**
     * 体检报告号
     */
    private String reportNo;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 体检日期
     */
    private String examTime;

    /**
     * 报告日期
     */
    private String reportTime;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 报告内容
     */
    private String context;

    /**
     * 是否已经处理: 0-未处理， 1- 已处理
     */
    private Integer state;

    /**
     * 是否删除: 0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getExamTime() {
        return examTime;
    }

    public void setExamTime(String examTime) {
        this.examTime = examTime;
    }

    public String getReportTime() {
        return reportTime;
    }

    public void setReportTime(String reportTime) {
        this.reportTime = reportTime;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
}
