package com.meerkat.crdt.capacity.service.impl;

import com.meerkat.base.enums.OrgaBizTypeEnum;
import com.meerkat.capacity.model.OrgaCapacity;
import com.meerkat.capacity.service.CapacityService;
import com.meerkat.crdt.capacity.param.CapacitySyncParams;
import com.meerkat.crdt.capacity.service.CapacitySyncService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/21 11:28
 */
public class CapacitySyncServiceImpl implements CapacitySyncService {

    @Resource
    private CapacityService capacityService;;

    @Override
    public  List<OrgaCapacity> capacitySync(CapacitySyncParams capacitySyncParams) {
        return (List<OrgaCapacity>) capacityService.listCapacityByOrgaId(capacitySyncParams.getOrgaId(),
                capacitySyncParams.getStartDate(), capacitySyncParams.getEndDate(), OrgaBizTypeEnum.EXAM.getCode());
    }
}
