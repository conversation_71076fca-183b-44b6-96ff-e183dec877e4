<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.crdt.report.mapper.ReportSyncMapper">

    <resultMap id="BaseResultMap" type="com.meerkat.crdt.report.dao.object.ReportSyncDO" >
        <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="report_no" property="reportNo" />
        <result column="id_card" property="idCard" />
        <result column="mobile" property="mobile" />
        <result column="exam_time" property="examTime" />
        <result column="report_time" property="reportTime" />
        <result column="company_name" property="companyName" />
        <result column="context" property="context" />
        <result column="state" property="state" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <sql id="Insert_Column">
        organization_id,
        report_no,
        id_card,
        mobile,
        exam_time,
        report_time,
        company_name,
        context,
        `state`
    </sql>

    <sql id="Base_Column_All">
        id,
        <include refid="Insert_Column"/>,
        is_deleted,
        gmt_created,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.meerkat.crdt.report.dao.object.ReportSyncDO">
        INSERT INTO tb_coordinator_report_sync
        VALUES ( <include refid="Insert_Column"/>
        )VALUES(
        #{organizationId},
        #{reportNo},
        #{idCard},
        #{mobile},
        #{examTime},
        #{reportTime},
        #{companyName},
        #{context},
        #{state}
        )
    </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO tb_coordinator_report_sync(
        <include refid="Insert_Column"/>
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.organizationId},
            #{item.reportNo},
            #{item.idCard},
            #{item.mobile},
            #{item.examTime},
            #{item.reportTime},
            #{item.companyName},
            #{item.context},
            #{item.state}
            )
        </foreach>
    </insert>

    <select id="batchUpdate">
        UPDATE tb_coordinator_report_sync
           SET `state` = #{state}
         WHERE id IN
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByOrgaIdAndState" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_All"/>
        FROM tb_coordinator_report_sync
        WHERE organization_id = #{orgaId}
        <if test="null != list and list.size()  > 0">
            AND `state` IN
            <foreach item="item" index="index" collection="list"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
       AND is_deleted = 0
       LIMIT 20
    </select>

</mapper>