package com.meerkat.crdt.item.service.impl;

import com.google.common.collect.Lists;
import com.meerkat.common.pinyin.PinYinUtil;
import com.meerkat.crdt.item.enums.ItemOperationEnum;
import com.meerkat.crdt.item.param.ItemSyncParam;
import com.meerkat.crdt.item.service.ItemSyncService;
import com.meerkat.shop.goods.enums.GoodsGenderEnum;
import com.meerkat.shop.goods.enums.GoodsMarriageStatusEnum;
import com.meerkat.shop.item.enums.ItemChangeTypeEnum;
import com.meerkat.shop.item.enums.ItemTypeEnum;
import com.meerkat.shop.item.model.Item;
import com.meerkat.shop.item.model.ItemChangeLog;
import com.meerkat.shop.item.service.ItemChangeLogService;
import com.meerkat.shop.item.service.ItemService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName ItemSyncServiceImpl.java
 * @Description TODO
 * @createTime 2022-02-15 15:23:00
 */
@Service
public class ItemSyncServiceImpl implements ItemSyncService {

    private static final Logger logger = LoggerFactory.getLogger(ItemSyncServiceImpl.class);

    @Resource
    private ItemService itemService;

    @Resource
    private ItemChangeLogService  itemChangeLogService;


    @Override
    public List<ItemSyncParam> listOrgaItem(Long orgaId) {
        logger.info("start list orga items， orgaId: {}", orgaId);
        List<Item> items = itemService.listItemByType(orgaId, Lists.newArrayList(ItemTypeEnum.EXAM_ITEM));

        if (CollectionUtils.isEmpty(items)) {
            return Lists.newArrayList();
        }

        List<ItemSyncParam> hisItemParams = Lists.newArrayList();
        items.stream().forEach(item -> {
            ItemSyncParam itemSyncParam = new ItemSyncParam();
            itemSyncParam.setName(item.getName());
            itemSyncParam.setInnerItemCode(item.getInnerItemCode());
            itemSyncParam.setPrice(item.getPrice());
            itemSyncParam.setDiscountable(item.getDiscountable());
            itemSyncParam.setGender(item.getGender());
            hisItemParams.add(itemSyncParam);

        });
        logger.info("end list orga items， orgaId: {}, item size: {}", orgaId, hisItemParams.size());
        return hisItemParams;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncChangeItem(Long orgaId, List<ItemSyncParam> itemSyncParams) {

        if (CollectionUtils.isEmpty(itemSyncParams)) {
            return;
        }
        logger.info("orgaId: {}, sync his item， his item size:{} ", orgaId, itemSyncParams.size());

        for (ItemSyncParam itemSyncParam: itemSyncParams) {
            if (StringUtils.isEmpty(itemSyncParam.getInnerItemCode())) {
                logger.warn("find his itemId is empty, item name : {}", itemSyncParam.getName());
            }

            // 平台不存在且院内需要删除，则直接跳过
            List<Item> items = itemService.listByOrgaIdAndItemCodes(orgaId, Lists.newArrayList(itemSyncParam.getInnerItemCode()));
            Item item = null;
            if (CollectionUtils.isNotEmpty(items)) {
                item = items.get(0);
            }

            Integer operationType = itemSyncParam.getOperationType();
            if (ItemOperationEnum.DELETE.getCode().equals(operationType)) {
                if (item == null) {
                    continue;
                }
                deletedItem(orgaId, item);
            } else if (ItemOperationEnum.ADD.getCode().equals(operationType)) {
                addItem(orgaId, itemSyncParam);
            } else if (ItemOperationEnum. MODIFY.getCode().equals(operationType)) {
                modifyItem(orgaId, item, itemSyncParam);
            } else {
                logger.warn("unknown operation type, hisItemId: {}", itemSyncParam.getInnerItemCode());
            }
        }

        logger.info("sync item end. orgaId: {}, sync his item， his item size:{} ", orgaId, itemSyncParams.size());

    }

    private void addItem(Long orgaId, ItemSyncParam itemSyncParam) {
        Item item = new Item();
        item.setName(itemSyncParam.getName());
        item.setOrganizationId(orgaId);
        item.setPinyin(PinYinUtil.getFirstSpell(itemSyncParam.getName()));
        item.setGender(Objects.nonNull(itemSyncParam.getGender()) ? itemSyncParam.getGender() : GoodsGenderEnum.COMMON.getCode());
        item.setMarriageStatus(GoodsMarriageStatusEnum.COMMON.getCode());
        item.setPrice(itemSyncParam.getPrice());
        item.setDiscountable(itemSyncParam.getDiscountable());
        item.setType(ItemTypeEnum.EXAM_ITEM.getCode());
        item.setInnerItemCode(itemSyncParam.getInnerItemCode());
        itemService.addItem(item);
    }

    private void deletedItem(Long orgaId, Item item) {
        List<Long> itemIds = Lists.newArrayList();
        itemIds.add(item.getId());
        itemChangeLogService.delUncompletedByItemIds(itemIds);

        ItemChangeLog changeLog = new ItemChangeLog();
        changeLog.setOrganizationId(orgaId);
        changeLog.setItemId(item.getId());
        changeLog.setType(ItemChangeTypeEnum.DELETED.getCode());
        // -1表示系统
        changeLog.setOperatorId(-1L);
        changeLog.setOriginalVal("0");
        changeLog.setNewVal("1");
        itemChangeLogService.addItemChangeLog(changeLog);
    }

    private void modifyItem(Long orgaId, Item oldItem, ItemSyncParam itemSyncParam) {
        List<ItemChangeLog> changeLogs = Lists.newArrayList();
        // 价格变动
        if (! oldItem.getPrice().equals(itemSyncParam.getPrice())) {
            logger.info("orgaId: {}, itemId: {}, changePrice.", orgaId, oldItem.getId());
            ItemChangeLog changeLog = new ItemChangeLog();
            changeLog.setOrganizationId(orgaId);
            changeLog.setItemId(oldItem.getId());
            changeLog.setType(ItemChangeTypeEnum.PRICE.getCode());
            changeLog.setOriginalVal(oldItem.getPrice().toString());
            changeLog.setNewVal(itemSyncParam.getPrice().toString());
            // -1表示系统
            changeLog.setOperatorId(-1L);
            changeLogs.add(changeLog);
        }
        // 是否可打折
        if (! oldItem.getDiscountable().equals(itemSyncParam.getDiscountable())) {
            logger.info("orgaId: {}, itemId: {}, changeDiscount.", orgaId, oldItem.getId());
            ItemChangeLog changeLog = new ItemChangeLog();
            changeLog.setOrganizationId(orgaId);
            changeLog.setItemId(oldItem.getId());
            changeLog.setType(ItemChangeTypeEnum.DISCOUNTABLE.getCode());
            changeLog.setOriginalVal(oldItem.getDiscountable().toString());
            changeLog.setNewVal(itemSyncParam.getDiscountable().toString());
            // -1表示系统
            changeLog.setOperatorId(-1L);
            changeLogs.add(changeLog);

        }

        if (! oldItem.getName().equals(itemSyncParam.getName())) {
            logger.info("orgaId: {}, itemId: {}, changeName.", orgaId, oldItem.getId());
            Item newItem = new Item();
            BeanUtils.copyProperties(oldItem, newItem);
            newItem.setName(itemSyncParam.getName());
            newItem.setPinyin(PinYinUtil.getFirstSpell(newItem.getName()));
            itemService.updateItem(newItem);
        }

        if (CollectionUtils.isNotEmpty(changeLogs)) {
            List<Long> itemIds = changeLogs.stream().map(ItemChangeLog::getItemId).collect(Collectors.toList());
            itemChangeLogService.delUncompletedByItemIds(itemIds);
            itemChangeLogService.batchAddItemChangeLog(changeLogs);
        }

    }
}
