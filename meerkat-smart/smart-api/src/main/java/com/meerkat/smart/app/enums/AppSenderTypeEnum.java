package com.meerkat.smart.app.enums;

/**
 * <p>
 * 应用消息发送者
 * </p>
 *
 * <AUTHOR>
 * @since 2022/5/30 下午9:06
 */
public enum AppSenderTypeEnum {

    /**
     * 用户
     */
    YH(0),

    /**
     * 客服
     */
    KF(1),

    /**
     * 系统自动回复
     */
    XT(2);

    private final int code;

    AppSenderTypeEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
