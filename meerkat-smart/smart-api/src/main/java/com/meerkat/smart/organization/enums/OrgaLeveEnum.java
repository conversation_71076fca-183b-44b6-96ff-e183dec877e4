package com.meerkat.smart.organization.enums;

/**
 * <AUTHOR>
 * @ClassName OrgaLeveEnum.java
 * @Description TODO
 * @createTime 2021-11-10 18:04:00
 */
public enum OrgaLeveEnum {

    THIRD_LEVEL_CLASS_S(1, "三级特等"),

    THIRD_LEVEL_CLASS_A(2, "三甲"),

    THIRD_LEVEL_CLASS_B(3, "三乙"),

    THIRD_LEVEL_CLASS_C(4, "三丙"),

    SECOND_LEVEL_CLASS_A(5, "二甲"),

    SECOND_LEVEL_CLASS_B(6, "二乙"),

    SECOND_LEVEL_CLASS_C(7, "二丙"),

    FIRST_LEVEL_CLASS_A(8,"一甲"),

    FIRST_LEVEL_CLASS_B(9,"一乙"),

    FIRST_LEVEL_CLASS_C(10,"一丙"),

    ;
    private int code;

    private String name;

    private OrgaLeveEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
