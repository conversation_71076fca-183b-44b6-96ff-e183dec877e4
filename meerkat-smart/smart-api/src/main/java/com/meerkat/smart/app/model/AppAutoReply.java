package com.meerkat.smart.app.model;

import java.time.LocalDateTime;

/**
 * @description app_auto_reply
 * <AUTHOR>
 * @date 2022-05-26
 */
public class AppAutoReply{

    /**
    * id
    */
    private Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    /**
    * 应用配置id
    */
    private Long appId;

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    /**
     * 自动回复标题
     */
    private String title;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    /**
    * 关键词
    */
    private String key;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
    /**
    * 自动回复内容
    */
    private String replyContent;

    public String getReplyContent() {
        return replyContent;
    }

    public void setReplyContent(String replyContent) {
        this.replyContent = replyContent;
    }
    /**
    * 未找到关键词时的默认回复，0否1是
    */
    private Integer isDefault;

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }
    /**
    * 雪花算法生成删除id
    */
    private Long deleteId;

    public Long getDeleteId() {
        return deleteId;
    }

    public void setDeleteId(Long deleteId) {
        this.deleteId = deleteId;
    }
    /**
    * 创建时间
    */
    private LocalDateTime gmtCreated;

    public LocalDateTime getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(LocalDateTime gmtCreated) {
        this.gmtCreated = gmtCreated;
    }
    /**
    * 修改时间
    */
    private LocalDateTime gmtModified;

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }


    /**
     * 机构/渠道
     */
    private Long chorgaId;

    public Long getChorgaId() {
        return chorgaId;
    }

    public void setChorgaId(Long chorgaId) {
        this.chorgaId = chorgaId;
    }
    /**
     * 1机构 2渠道
     */
    private Integer chorgaType;

    public Integer getChorgaType() {
        return chorgaType;
    }

    public void setChorgaType(Integer chorgaType) {
        this.chorgaType = chorgaType;
    }
}