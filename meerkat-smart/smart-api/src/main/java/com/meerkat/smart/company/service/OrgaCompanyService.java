package com.meerkat.smart.company.service;

import com.meerkat.common.db.PageView;
import com.meerkat.smart.company.enums.GuestCompanyEnum;
import com.meerkat.smart.company.model.OrgaCompany;
import com.meerkat.smart.company.model.OrgaCompanyGoods;
import com.meerkat.smart.company.param.OrgaCompanyQuery;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName OrgaCompanyService.java
 * @Description TODO
 * @createTime 2021-10-20 14:56:00
 */
public interface OrgaCompanyService {

    /**
     *  查询散客单位
     * @param orgaId
     * @param companyEnum
     * @return
     */
    OrgaCompany getGuestCompany(Long orgaId, GuestCompanyEnum companyEnum);

    /**
     *  查询散客单位列表
     * @param orgaId
     * @param companyEnums
     * @return
     */
    List<OrgaCompany> listGuestCompany(Long orgaId, List<GuestCompanyEnum> companyEnums);

    /**
     *  根据单位ID查询单位
     * @param companyId
     * @return
     */
    OrgaCompany getOrgaCompany(Long companyId);

    /**
     * 添加机构单位
     * @param orgaCompany
     * @return
     */
    int addOrgaCompany(OrgaCompany orgaCompany);

    /**
     * 查询机构单位列表，模糊单位名字的空时返回机构所有单位
     * @param orgaCompanyQuery
     * @return
     */
    PageView<OrgaCompany> listOrgaCompany(OrgaCompanyQuery orgaCompanyQuery);

    /**
     *  查询机构单位，通过机构单位ID集合
     * @param ids
     * @return
     */
    List<OrgaCompany> listOrgaCompanyByIds(List<Long> ids);

    /**
     *  添加机构单位&内网单位关联关系
     * @param orgaCompanyId
     * @param innerCompanyId
     */
    void addOrgaInnerCompanyRel(Long orgaCompanyId, Long innerCompanyId);


    /**
     * 查询需要同步的单位
     * @param orgaId
     * @return
     */
    List<OrgaCompany> listNeedSyncCompany(Long orgaId);

    /**
     * 更新单位基础信息
     * @param orgaCompany
     */
    void updateOrgaCompanyBase(OrgaCompany orgaCompany);

    /**
     * 查询机构下的单位基础信息
     * @param orgaId
     * @param name
     * @return
     */
    List<OrgaCompany> listCompanyBase(Long orgaId, String name);

    /**
     * 初始化机构散客单位
     * @param orgaId
     * @return
     */
    int initOrgaGuestCompany(Long orgaId);

    /**
     * 添加机构单位商品
     * @param orgaCompanyGoods
     * @return
     */
    void addOrgaCompanyGoods(OrgaCompanyGoods orgaCompanyGoods);


    /**
     * 通过单位查询机构单位商品信息
     * @param companyId
     * @return
     */
    List<OrgaCompanyGoods> listOrgaCompanyGoodsByCompanyId(Long companyId);

    /**
     *  通过机构ID查询单位商品信息
     * @param orgaId
     * @return
     */
    List<OrgaCompanyGoods> listOrgaCompanyGoodsByOrgaId(Long orgaId);

    /**
     * 通过单位集合查询机构单位商品信息
     * @param companyIds
     * @return
     */
    List<OrgaCompanyGoods> listOrgaCompanyGoodsByCompanyIds(List<Long> companyIds);

    /**
     * 根据机构id和单位名称查询机构单位信息
     * @param organizationId 机构id
     * @param name  单位名称
     * @return
     */
    OrgaCompany getOrgaCompany(Long organizationId, String name);
}
