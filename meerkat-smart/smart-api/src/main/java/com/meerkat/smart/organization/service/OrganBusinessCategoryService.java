package com.meerkat.smart.organization.service;

import com.meerkat.smart.organization.model.OrganBusinessCategory;

import java.util.List;

public interface OrganBusinessCategoryService {

    Integer save(OrganBusinessCategory organBusinessCategory);

    Integer batchSave(List<OrganBusinessCategory> organBusinessCategoryList);

    Integer deleteByOrganId(Long organId);

    List<OrganBusinessCategory> listByOrganId(Long organId);

    List<OrganBusinessCategory> listByIds(List<Long> ids);

}
