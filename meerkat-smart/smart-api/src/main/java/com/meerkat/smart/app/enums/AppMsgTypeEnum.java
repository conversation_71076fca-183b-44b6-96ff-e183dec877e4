package com.meerkat.smart.app.enums;

/**
 * 应用消息类型
 * <AUTHOR>
 * @description
 * @date 2022/5/27 14:35
 */
public enum AppMsgTypeEnum {
    TEXT(0, "文本消息"),

    NEWS(1, "图文消息"),
    ;
    private final int code;

    private final String desc;

    AppMsgTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
