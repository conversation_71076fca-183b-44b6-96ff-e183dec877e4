<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.smart.company.mapper.OrgaCompanyBookMapper">

    <resultMap id="BaseResultMap" type="com.meerkat.smart.company.dao.object.OrgaCompanyBookDO" >
        <result column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="credential_no" property="credentialNo" />
        <result column="credential_type" property="credentialType" />
        <result column="acceptor_id" property="acceptorId" />
        <result column="acceptor_name" property="acceptorName" />
        <result column="goods_id" property="goodsId" />
        <result column="goods_name" property="goodsName" />
        <result column="operator_id" property="operatorId" />
        <result column="operator_name" property="operatorName" />
        <result column="status" property="status" />
        <result column="order_num" property="orderNum" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <sql id="Insert_Column">
        company_id,
        credential_no,
        credential_type,
        acceptor_id,
        acceptor_name,
        goods_id,
        goods_name,
        operator_id,
        operator_name,
        status,
        order_num
    </sql>

    <sql id="Base_Column_All">
        id,
        <include refid="Insert_Column"/>,
        is_deleted,
        gmt_created,
        gmt_modified
    </sql>

    <insert id="batchInsert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.meerkat.smart.company.dao.object.OrgaCompanyBookDO">
        INSERT INTO tb_organization_company_book(
        <include refid="Insert_Column"/>
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.companyId},
            #{item.credentialNo},
            #{item.credentialType},
            #{item.acceptorId},
            #{item.acceptorName},
            #{item.goodsId},
            #{item.goodsName},
            #{item.operatorId},
            #{item.operatorName},
            0,
            #{item.orderNum}
            )
        </foreach>
    </insert>

    <update id="update" parameterType="com.meerkat.smart.company.dao.object.OrgaCompanyBookDO">
        UPDATE tb_organization_company_book
        <set>
            <if test="null != companyId">company_id = #{companyId},</if>
            <if test="null != credentialNo and '' != credentialNo">credential_no = #{credentialNo},</if>
            <if test="null != credentialType">credential_type = #{credentialType},</if>
            <if test="null != acceptorId">acceptor_id = #{acceptorId},</if>
            <if test="null != acceptorName and '' != acceptorName">acceptor_name = #{acceptorName},</if>
            <if test="null != goodsId">goods_id = #{goodsId},</if>
            <if test="null != goodsName and '' != goodsName">goods_name = #{goodsName},</if>
            <if test="null != operatorId">operator_id = #{operatorId},</if>
            <if test="null != operatorName and '' != operatorName">operator_name = #{operatorName},</if>
            <if test="null != status">status = #{status},</if>
            <if test="null != orderNum and '' != orderNum">order_num = #{orderNum}</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
          FROM tb_organization_company_book
        WHERE id = #{id}
    </select>

    <select id="selectOrgaCompanyBook" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_organization_company_book
        WHERE company_id = #{companyId}
        <if test="null != acceptorName and '' != acceptorName">
          AND acceptor_name LIKE concat("%",#{acceptorName}, "%")
        </if>
        <if test="null != goodsName and '' != goodsName">
          AND goods_name LIKE concat("%",#{goodsName}, "%")
        </if>
          AND is_deleted = 0
        ORDER BY id DESC
    </select>

    <select id="selectByCredentialNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_organization_company_book
        WHERE company_id = #{companyId}
          AND credential_no = #{credentialNo}
          AND is_deleted = 0
    </select>

    <select id="selectByAcceptorId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_organization_company_book
        WHERE company_id = #{companyId}
          AND acceptor_id = #{acceptorId}
          AND is_deleted = 0
    </select>

</mapper>