package com.meerkat.smart.company.mapper;

import com.meerkat.smart.company.dao.object.ChannelCompanyDO;
import com.meerkat.smart.company.dao.object.InnerCompanyDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @ClassName ChannelCompanyMapper.java
 * @Description TODO
 * @createTime 2021-09-27 20:45:00
 */
@Mapper
public interface ChannelCompanyMapper {

    int insert(ChannelCompanyDO channelCompanyDO);

    int delete(Integer id);

    int update(ChannelCompanyDO channelCompanyDO);

}
