package com.meerkat.smart.organization.mapper;

import com.meerkat.smart.organization.dao.object.OrganizationDO;
import com.meerkat.smart.organization.model.Organization;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OrganizationMapper.java
 * @Description TODO
 * @createTime 2021-09-27 13:46:00
 */
@Mapper
public interface OrganizationMapper {

    int insert(OrganizationDO organizationDO);

    int delete(Long id);

    int update(OrganizationDO organizationDO);

    OrganizationDO selectById(@Param("id") Long id);

    List<OrganizationDO> selectByIds(@Param("ids") List<Long> ids, @Param("statusList") List<Integer> statusList);

    List<OrganizationDO> selectByAddressIds(@Param("addressIds") List<Integer> addressIds);

    List<OrganizationDO> selectByAddressAndIds(@Param("ids") List<Long> ids, @Param("addressIds") List<Integer> addressIds,
                                               @Param("type") Integer type, @Param("statusList") List<Integer> statusList,
                                               @Param("level") String level, @Param("longitude") String longitude,
                                               @Param("latitude") String latitude);

    int checkName(@Param("name") String name, @Param("exclude") Long id);

    /**
     * @param name
     * @return
     */
    List<OrganizationDO> selectByName(@Param("names") List<String> name);

    /**
     * 根据机构id列表查询
     * @return
     */
    List<OrganizationDO> listByIds(@Param("ids")List<Long> ids);

    /**
     * @param name
     * @return
     */
    List<OrganizationDO> select(@Param("name") String name, @Param("addressIds") List<Integer> addressIds, @Param("type") Integer type, @Param("level") String level,
                                @Param("sortField") String sortField, @Param("desc") Boolean desc, @Param("trusted") Integer trusted, @Param("wechat") Integer wechat,
                                @Param("bizType") String bizType,@Param("managerIds") List<Long> managerIds,@Param("businessName") String businessName);

    int switchStatus(@Param("orgaId") Long orgaId);

    List<OrganizationDO> selectAll();

    int updateAuthInfo(@Param("orgaId") Long orgaId, @Param("trusted") Integer trusted);

    List<OrganizationDO> selectByStatus(@Param("statusList") List<Integer> statusList);

}
