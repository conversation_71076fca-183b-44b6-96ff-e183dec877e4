package com.meerkat.smart.organization.mapper;

import com.meerkat.smart.organization.dao.object.BrandDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName BrandMapper.java
 * @Description TODO
 * @createTime 2021-09-17 11:02:00
 */
@Mapper
public interface BrandMapper {


    int insert(BrandDO brand);

    List<BrandDO> selectAll();

    int delete(int id);

    int update(BrandDO brand);

    BrandDO selectById(@Param("id")Integer id);

}
