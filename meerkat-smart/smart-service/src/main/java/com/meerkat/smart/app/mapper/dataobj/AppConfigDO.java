package com.meerkat.smart.app.mapper.dataobj;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description app_config
 * <AUTHOR>
 * @date 2022-05-26
 */
public class AppConfigDO{
    /**
    * id
    */
    private Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    /**
    * 机构/渠道
    */
    private Long chorgaId;

    public Long getChorgaId() {
        return chorgaId;
    }

    public void setChorgaId(Long chorgaId) {
        this.chorgaId = chorgaId;
    }
    /**
    * 1机构 2渠道
    */
    private Integer chorgaType;

    public Integer getChorgaType() {
        return chorgaType;
    }

    public void setChorgaType(Integer chorgaType) {
        this.chorgaType = chorgaType;
    }
    /**
    * 应用key
    */
    private String appKey;

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }
    /**
    * 应用名称
    */
    private String appName;

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }
    /**
    * 应用密钥
    */
    private String appSecret;

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }
    /**
    * 0:小程序 1:公众号
    */
    private Integer appType;

    public Integer getAppType() {
        return appType;
    }

    public void setAppType(Integer appType) {
        this.appType = appType;
    }

    /**
     * 服务器回调token
     */
    private String token;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    /**
     * 消息加密key
     */
    private String aesKey;

    public String getAesKey() {
        return aesKey;
    }

    public void setAesKey(String aesKey) {
        this.aesKey = aesKey;
    }
    /**
    * 0:微信 1:支付宝
    */
    private Integer platform;

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }
    /**
    * 雪花算法生成删除id
    */
    private Long deleteId;

    public Long getDeleteId() {
        return deleteId;
    }

    public void setDeleteId(Long deleteId) {
        this.deleteId = deleteId;
    }
    /**
    * 创建时间
    */
    private LocalDateTime gmtCreated;

    public LocalDateTime getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(LocalDateTime gmtCreated) {
        this.gmtCreated = gmtCreated;
    }
    /**
    * 修改时间
    */
    private LocalDateTime gmtModified;

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }
}