<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.smart.app.mapper.AppConfigMapper">

    <resultMap id="BaseResultMap" type="com.meerkat.smart.app.mapper.dataobj.AppConfigDO" >
        <result column="id" property="id" />
        <result column="chorga_id" property="chorgaId" />
        <result column="chorga_type" property="chorgaType" />
        <result column="app_key" property="appKey" />
        <result column="app_name" property="appName" />
        <result column="app_secret" property="appSecret" />
        <result column="app_type" property="appType" />
        <result column="platform" property="platform" />
        <result column="token" property="token"/>
        <result column="aes_key" property="aesKey"/>
        <result column="delete_id" property="deleteId" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        chorga_id,
        chorga_type,
        app_key,
        app_name,
        app_secret,
        app_type,
        platform,
        delete_id,
        gmt_created,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.meerkat.smart.app.mapper.dataobj.AppConfigDO">
        INSERT INTO tb_app_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != chorgaId">
                chorga_id,
            </if>
            <if test="null != chorgaType">
                chorga_type,
            </if>
            <if test="null != appKey and '' != appKey">
                app_key,
            </if>
            <if test="null != appName and '' != appName">
                app_name,
            </if>
            <if test="null != appSecret and '' != appSecret">
                app_secret,
            </if>
            <if test="null != appType">
                app_type,
            </if>
            <if test="null != platform">
                platform,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != chorgaId">
                #{chorgaId},
            </if>
            <if test="null != chorgaType">
                #{chorgaType},
            </if>
            <if test="null != appKey and '' != appKey">
                #{appKey},
            </if>
            <if test="null != appName and '' != appName">
                #{appName},
            </if>
            <if test="null != appSecret and '' != appSecret">
                #{appSecret},
            </if>
            <if test="null != appType">
                #{appType},
            </if>
            <if test="null != platform">
                #{platform},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.meerkat.smart.app.mapper.dataobj.AppConfigDO">
        UPDATE tb_app_config
        <set>
            <if test="null != chorgaId and '' != chorgaId">chorga_id = #{chorgaId},</if>
            <if test="null != chorgaType">chorga_type = #{chorgaType},</if>
            <if test="null != appKey and '' != appKey">app_key = #{appKey},</if>
            <if test="null != appName and '' != appName">app_name = #{appName},</if>
            <if test="null != appSecret and '' != appSecret">app_secret = #{appSecret},</if>
            <if test="null != appType">app_type = #{appType},</if>
            <if test="null != platform">platform = #{platform}</if>
        </set>
        WHERE id = #{id}
    </update>
    <select id="loadById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_app_config
        WHERE id = #{id}
        AND delete_id = 0;
    </select>
    <select id="loadByChorga" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_app_config
        WHERE chorga_id = #{chorgaId}
        AND chorga_type = #{chorgaType}
        AND delete_id = 0;
    </select>
    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_app_config
        WHERE chorga_id = #{chorgaId}
        AND chorga_type = #{chorgaType}
        AND platform = #{platform}
        AND app_key = #{appKey}
        AND delete_id = 0;
    </select>
    <select id="loadByQuery" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_app_config
        <where>
            <if test="chorgaId != null">
                and chorga_id = #{chorgaId}
            </if>
            <if test="chorgaType != null">
                and chorga_type = #{chorgaType}
            </if>
            <if test="appKey != null">
                and app_key = #{appKey}
            </if>
            <if test="platform != null">
                and platform = #{platform}
            </if>
            <if test="1 == 1">
                and delete_id = 0
            </if>
            <if test="appType != null">
                and app_type = #{appType}
            </if>
        </where>
    </select>

</mapper>