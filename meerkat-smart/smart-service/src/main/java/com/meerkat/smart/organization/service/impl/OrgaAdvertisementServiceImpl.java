package com.meerkat.smart.organization.service.impl;

import com.meerkat.common.utils.CopyUtil;
import com.meerkat.smart.organization.dao.object.OrgaAdvertisementDO;
import com.meerkat.smart.organization.enums.OrgaAdvertisementStatusEnum;
import com.meerkat.smart.organization.enums.OrgaAdvertisementTypeEnum;
import com.meerkat.smart.organization.mapper.OrgaAdvertisementMapper;
import com.meerkat.smart.organization.model.OrgaAdvertisement;
import com.meerkat.smart.organization.service.OrgaAdvertisementService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/1 16:06
 */
@Service
public class OrgaAdvertisementServiceImpl implements OrgaAdvertisementService {

    @Resource
    private OrgaAdvertisementMapper orgaAdvertisementMapper;

    @Override
    public List<OrgaAdvertisement> listByType(Long orgaId, Integer type, boolean valid) {
        List<OrgaAdvertisementDO> doList = orgaAdvertisementMapper.selectByOrgaIdAndType(orgaId, type);
        if (CollectionUtils.isEmpty(doList)) {
            return List.of();
        }

        if (valid) {
            //针对弹窗、banner广告
            doList = doList.stream().filter(this::valid).collect(Collectors.toList());
        }

        return CopyUtil.copyList(doList, OrgaAdvertisement.class);
    }

    @Override
    public int logicDel(Long orgaId, Long adId) {
        return orgaAdvertisementMapper.logicDel(orgaId, adId);
    }

    @Override
    public int batchLogicDel(Long orgaId, List<Long> adIdList) {
        return orgaAdvertisementMapper.batchLogicDel(orgaId, adIdList);
    }

    @Override
    public int add(OrgaAdvertisement advertisement) {
        return orgaAdvertisementMapper.insert(CopyUtil.copy(advertisement, OrgaAdvertisementDO.class));
    }

    @Override
    public int batchAdd(List<OrgaAdvertisement> advertisementList) {
        return orgaAdvertisementMapper.batchInsert(CopyUtil.copyList(advertisementList, OrgaAdvertisementDO.class));
    }

    @Override
    public int update(OrgaAdvertisement advertisement) {
        return orgaAdvertisementMapper.update(CopyUtil.copy(advertisement, OrgaAdvertisementDO.class));
    }

    @Override
    public int batchUpdate(List<OrgaAdvertisement> advertisementList) {
        return orgaAdvertisementMapper.batchUpdate(CopyUtil.copyList(advertisementList, OrgaAdvertisementDO.class));
    }

    @Override
    public boolean switchStatus(Long orgaId, Integer open) {
        List<OrgaAdvertisement> orgaAdvertisements = listByType(orgaId, OrgaAdvertisementTypeEnum.FIXED.getCode(), false);

        if (CollectionUtils.isEmpty(orgaAdvertisements)) {
            return false;
        }

        List<Long> idList = orgaAdvertisements.stream().map(OrgaAdvertisement::getId).collect(Collectors.toList());
        if (open == OrgaAdvertisementStatusEnum.ENABLE.getCode()) {
            //开启
            orgaAdvertisementMapper.switchStatus(OrgaAdvertisementStatusEnum.ENABLE.getCode(), idList);
        } else {
            //关闭
            orgaAdvertisementMapper.switchStatus(OrgaAdvertisementStatusEnum.DISABLE.getCode(), idList);
        }

        return true;
    }

    @Override
    public Integer sort(Long orgaId, List<Long> idList) {
        return orgaAdvertisementMapper.sort(orgaId, idList);
    }

    @Override
    public int maxSeqByType(Long orgaId, Integer type) {
        return orgaAdvertisementMapper.maxSeqByType(orgaId, type);
    }

    // ============================= private method =============================

    /**
     * @param ad
     * @return
     */
    private boolean valid(OrgaAdvertisementDO ad) {
        //是否启用
        if (ad.getStatus() == OrgaAdvertisementStatusEnum.DISABLE.getCode()) {
            //过期
            return false;
        }

        if (OrgaAdvertisementTypeEnum.FIXED.getCode() == ad.getAdType()) {
            //固定广告直接返回
            return true;
        }

        if (ad.getActiveStartTime().isAfter(LocalDateTime.now())) {
            //未开始
            return false;
        }

        if (Objects.isNull(ad.getActiveEndTime())) {
            //永久有效
            return true;
        }

        //过期
        return !ad.getActiveEndTime().isBefore(LocalDateTime.now());

        //有效
    }
}
