package com.meerkat.smart.company.dao.object;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName OrgaCompanyGoodsDO.java
 * @Description TODO
 * @createTime 2022-01-18 10:55:00
 */
public class OrgaCompanyGoodsDO implements Serializable {


    private static final long serialVersionUID = 4036280562738289564L;

    private Long id;

    /**
     * 机构id
     */
    private Long organizationId;

    /**
     * 机构单位id
     */
    private Long companyId;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 是否删除：0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
}
