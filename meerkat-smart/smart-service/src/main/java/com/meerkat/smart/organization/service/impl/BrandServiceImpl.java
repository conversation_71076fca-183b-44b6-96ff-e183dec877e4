package com.meerkat.smart.organization.service.impl;

import com.meerkat.smart.organization.service.BrandService;
import com.meerkat.smart.organization.dao.object.BrandDO;
import com.meerkat.smart.organization.mapper.BrandMapper;
import com.meerkat.smart.organization.model.Brand;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @ClassName BrandServiceImpl.java
 * @Description TODO
 * @createTime 2021-09-18 11:15:00
 */
@Service
public class BrandServiceImpl implements BrandService {

    @Autowired
    private BrandMapper brandMapper;

    @Override
    @Transactional
    public void addBrand(Brand brand) {
        BrandDO brandDO = new BrandDO();
        BeanUtils.copyProperties(brand, brandDO);
        brandMapper.insert(brandDO);
    }

    @Override
    public Brand getBrandById(Integer id) {
        BrandDO brandDO = brandMapper.selectById(id);
        Brand brand = new Brand();
        BeanUtils.copyProperties(brandDO, brand);
        return brand;
    }
}
