package com.meerkat.smart.company.mapper;

import com.meerkat.smart.company.dao.object.OrgaCompanyGoodsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OrgaCompanyGoodsMapper.java
 * @Description TODO
 * @createTime 2022-01-18 10:57:00
 */
@Mapper
public interface OrgaCompanyGoodsMapper {

    /**
     * 写入机构单位商品数据
     * @param orgaCompanyGoodsDO
     * @return
     */
    int insert(OrgaCompanyGoodsDO orgaCompanyGoodsDO);

    /**
     * 删除机构单位商品
     * @param id
     * @return
     */
    int delete(int id);

    /**
     * 根据单位ID查询单位套餐
     * @param companyId
     * @return
     */
    List<OrgaCompanyGoodsDO> selectByCompanyId(@Param("companyId") Long companyId);

    /**
     * 根据机构ID查询单位套餐
     * @param orgaId
     * @return
     */
    List<OrgaCompanyGoodsDO> selectByOrgaId(@Param("orgaId") Long orgaId);

    /**
     * 根据单位IDS查询单位套餐
     * @param companyIds
     * @return
     */
    List<OrgaCompanyGoodsDO> selectByCompanyIds(@Param("companyIds") List<Long> companyIds);

    List<OrgaCompanyGoodsDO> selectByCompanyIdAndGoodsId(@Param("companyId") Long companyId, @Param("goodsId") Long goodsId);
}
