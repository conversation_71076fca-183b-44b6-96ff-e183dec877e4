package com.meerkat.smart.channel.dao.object;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName ChannelOrgaRelationDO.java
 * @Description TODO
 * @createTime 2021-09-27 16:55:00
 */
public class ChannelOrgaRelationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 机构id
     */
    private Long organizationId;

    /**
     * 排序
     */
    private Integer sequence;

    /**
     * 状态：1-可用 2-不可用
     */
    private Integer status;

    /**
     * 渠道下机构是否显示
     */
    private Integer isShow;

    /**
     * 是否删除：0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getIsShow() {
        return isShow;
    }

    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }
}
