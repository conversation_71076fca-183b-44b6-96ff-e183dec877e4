<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.smart.organization.mapper.BusinessCategoryMapper">

    <resultMap id="BaseResultMap" type="com.meerkat.smart.organization.dao.object.BusinessCategoryDO">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="parent_id" property="parentId"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>


    <sql id="Insert_Column">
        `name`,
        parent_id
    </sql>

    <sql id="Base_Column_All">
        id,
        <include refid="Insert_Column"/>,
        is_deleted,
        gmt_created,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.meerkat.smart.organization.dao.object.BusinessCategoryDO">
        INSERT INTO tb_business_category(
        <include refid="Insert_Column"/>
        )
        VALUES (
        #{name},
        #{parentId}
        )
    </insert>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_All"/>
        FROM tb_business_category
        WHERE is_deleted =0
    </select>

    <delete id="delete">
        UPDATE tb_business_category
        SET is_deleted = 1
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.meerkat.smart.organization.dao.object.BusinessCategoryDO">
        UPDATE tb_brand
        <set>
            <if test="null != name and '' != name">name = #{name},</if>
            <if test="null != parentId and '' != parentId">parent_id = #{parentId},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_All"/>
        FROM tb_business_category
        WHERE id = #{id}
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_All"/>
        FROM tb_business_category
        WHERE id in
        (
        <foreach collection="ids" item="item" separator=",">
            #{item}
        </foreach>
        ) and is_deleted = 0
    </select>

</mapper>