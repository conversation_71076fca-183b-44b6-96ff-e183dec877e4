<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.smart.app.mapper.AppAutoReplyMapper">

    <resultMap id="BaseResultMap" type="com.meerkat.smart.app.mapper.dataobj.AppAutoReplyDO" >
        <result column="id" property="id" />
        <result column="chorga_id" property="chorgaId"/>
        <result column="chorga_type" property="chorgaType"/>
        <result column="app_id" property="appId" />
        <result column="key" property="key" />
        <result column="reply_content" property="replyContent" />
        <result column="is_default" property="isDefault" />
        <result column="delete_id" property="deleteId" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        chorga_id,
        chorga_type,
        app_id,
        title,
        `key`,
        reply_content,
        is_default,
        delete_id,
        gmt_created,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.meerkat.smart.app.mapper.dataobj.AppAutoReplyDO">
        INSERT INTO tb_app_auto_reply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != chorgaId">
                chorga_id,
            </if>
            <if test="null != chorgaType">
                chorga_type,
            </if>
            <if test="null != appId">
                app_id,
            </if>
            <if test="null != title and '' != title">
                title,
            </if>
            <if test="null != key and '' != key">
                `key`,
            </if>
            <if test="null != replyContent and '' != replyContent">
                reply_content,
            </if>
            <if test="null != isDefault">
                is_default,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != chorgaId">
                #{chorgaId},
            </if>
            <if test="null != chorgaType">
                #{chorgaType},
            </if>
            <if test="null != appId">
                #{appId},
            </if>
            <if test="null != title and '' != title">
                #{title},
            </if>
            <if test="null != key and '' != key">
                #{key},
            </if>
            <if test="null != replyContent and '' != replyContent">
                #{replyContent},
            </if>
            <if test="null != isDefault">
                #{isDefault},
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        UPDATE tb_app_auto_reply SET delete_id = #{deleteId}
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.meerkat.smart.app.mapper.dataobj.AppAutoReplyDO">
        UPDATE tb_app_auto_reply
        <set>
            title = #{title},`key` = #{key},reply_content = #{replyContent}
        </set>
        WHERE id = #{id}
    </update>

    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_app_auto_reply
        WHERE id = #{id}
        AND delete_id = 0
    </select>
    <select id="loadByAppId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_app_auto_reply
        WHERE app_id = #{appId}
        AND delete_id = 0
    </select>

</mapper>