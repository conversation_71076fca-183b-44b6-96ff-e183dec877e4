<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.smart.company.mapper.InnerCompanyMapper">

    <resultMap id="BaseResultMap" type="com.meerkat.smart.company.dao.object.InnerCompanyDO" >
        <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <sql id="Insert_Column">
        organization_id,
        `name`,
        code
    </sql>

    <sql id="Base_Column_All">
        id,
        <include refid="Insert_Column"/>,
        is_deleted,
        gmt_created,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.meerkat.smart.company.dao.object.InnerCompanyDO">
        INSERT INTO tb_inner_company(
        <include refid="Insert_Column"/>
        )VALUES (
        #{organizationId},
        #{name},
        #{code}
        )
    </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.meerkat.smart.company.dao.object.InnerCompanyDO">
        INSERT INTO tb_inner_company(
        <include refid="Insert_Column"/>
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.organizationId},
            #{item.name},
            #{item.code}
            )
        </foreach>
    </insert>

    <delete id="delete" >
        UPDATE tb_inner_company
        SET is_deleted = 1
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.meerkat.smart.company.dao.object.InnerCompanyDO">
        UPDATE tb_inner_company
        <set>
            <if test="null != organizationId and '' != organizationId">organization_id = #{organizationId},</if>
            <if test="null != name and '' != name">`name` = #{name},</if>
            <if test="null != code and '' != code">code = #{code}</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_inner_company
        WHERE id = #{id}
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_inner_company
        WHERE is_deleted = 0
          AND id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByCodes" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_inner_company
        WHERE is_deleted = 0
          AND organization_id = #{orgaId}
          AND code IN
        <foreach item="item" index="index" collection="codes" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_inner_company
        WHERE is_deleted = 0
        AND organization_id = #{orgaId}
        AND code = #{code}
    </select>

    <select id="selectByOrgaIdAndFuzzyName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_inner_company
        WHERE organization_id = #{orgaId}
        AND is_deleted = 0
        <if test="null != fuzzyName and '' != fuzzyName">
            AND `name` LIKE concat('%',#{fuzzyName},'%')
        </if>
    </select>


</mapper>