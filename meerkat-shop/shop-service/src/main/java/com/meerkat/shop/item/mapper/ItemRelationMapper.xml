<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.shop.item.mapper.ItemRelationMapper">

    <resultMap id="BaseResultMap" type="com.meerkat.shop.item.dao.object.ItemRelationDO" >
        <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="item_id" property="itemId" />
        <result column="related_item_id" property="relatedItemId" />
        <result column="type" property="type" />
        <result column="group_id" property="groupId" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <sql id="Insert_Column">
        organization_id,
        item_id,
        related_item_id,
        `type`,
        group_id
    </sql>

    <sql id="Base_Column_All">
        id,
        <include refid="Insert_Column"/>,
        is_deleted,
        gmt_created,
        gmt_modified
    </sql>


    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.meerkat.shop.item.dao.object.ItemRelationDO">
        INSERT INTO tb_item_relation(
        <include refid="Insert_Column"/>
        )VALUES (
        #{organizationId},
        #{itemId},
        #{relatedItemId},
        #{type},
        #{groupId}
        )
    </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="java.util.Map">
        INSERT INTO tb_item_relation(
        <include refid="Insert_Column"/>
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.organizationId},
            #{item.itemId},
            #{item.relatedItemId},
            #{item.type},
            #{item.groupId}
            )
        </foreach>
    </insert>

    <delete id="delete" >
        UPDATE tb_item_relation
        SET is_deleted = 1
        WHERE id = #{id}
    </delete>

    <update id="deleteByItemId" parameterType="java.util.Map">
        UPDATE tb_item_relation
        SET is_deleted = 1
        WHERE item_id = #{itemId} AND organization_id = #{orgaId}
    </update>

    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_All"/>
        FROM tb_item_relation
        <where>
            organization_id = #{orgaId}
            <if test="types != null and types.size()>0">
                AND `type` IN
                <foreach item="item" index="index" collection="types"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="groupIds != null and groupIds.size()>0">
                and  group_id  in
                <foreach item="groupId" index="index" collection="groupIds" open="(" separator="," close=")">
                    #{groupId}
                </foreach>
            </if>
            AND is_deleted = 0
        </where>
    </select>

    <select id="selectByItemIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_item_relation
        WHERE item_id IN
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        AND is_deleted = 0
    </select>

    <select id="selectByItemIdAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
          FROM tb_item_relation
         WHERE item_id =  #{itemId}
           AND `type` = #{type}
           AND is_deleted = 0
    </select>

    <select id="selectByRelItemIdAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_item_relation
        WHERE related_item_id =  #{relItemId}
        AND `type` = #{type}
        AND is_deleted = 0
    </select>

    <select id="selectByItemId" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_item_relation
        WHERE item_id = #{itemId}
        AND is_deleted = 0
    </select>

    <select id="selectByRelatedItemId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_item_relation
        WHERE organization_id = #{orgaId}
        <if test="relatedItemIdList != null and relatedItemIdList.size() > 0">
            AND related_item_id in (
                <foreach collection="relatedItemIdList" item="item" separator=",">
                    #{item}
                </foreach>
            )
        </if>
        <if test="type != null">
            AND type = #{type}
        </if>
        AND is_deleted = 0
    </select>

    <delete id="deleteItemRelation" >
        update tb_item_relation set is_deleted = 1
        where organization_id = #{orgaId}
          and item_id = #{itemId}
          and related_item_id = #{relatedItemId}
          and type = #{type}
    </delete>
    <delete id="deleteByIds">
        UPDATE tb_item_relation
        SET is_deleted = 1
        WHERE
        id IN
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectItemRelation" resultMap="BaseResultMap" >
        SELECT <include refid="Base_Column_All" />
        FROM tb_item_relation
        where organization_id = #{orgaId}
        <if test="null != itemId and '' != itemId">
            and item_id = #{itemId}
        </if>
        <if test="null != relatedItemId and '' != relatedItemId">
            and related_item_id = #{relatedItemId}
        </if>
        <if test="null != type and '' != type">
            and type = #{type}
        </if>
        AND is_deleted = 0
    </select>

    <select id="selectByOrgaId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_item_relation
        WHERE is_deleted = 0
        AND organization_id = #{orgaId}
    </select>

    <select id="selectByOrgIdAndGroupId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_item_relation
        WHERE is_deleted = 0
        AND organization_id = #{orgaId} and group_id = #{groupId}
    </select>
    <select id="selectGroupRelationByItemId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_All"/>
        FROM
        tb_item_relation
        WHERE
        group_id = (
        SELECT
        group_id
        FROM
        tb_item_relation
        WHERE
        item_id = #{itemId} AND type=6 AND is_deleted = 0 ) AND is_deleted = 0
    </select>

    <!-- 查询同一机构最大的组号,没有组信息的数据可能为空-->
    <select id="queryNextGroupId" resultType="integer">
        select  MAX(group_id)
        from
            tb_item_relation
        where
            organization_id = #{orgaId} AND is_deleted = 0
    </select>
    <select id="selectAllItemRelationByItemIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_All" />
        from
        tb_item_relation
        where
        is_deleted = 0
        and item_id in
        <foreach item="itemId" index="index" collection="itemIds" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        UNION
        select
        <include refid="Base_Column_All" />
        from
        tb_item_relation
        where
        is_deleted = 0
        and related_item_id in
        <foreach item="itemId" index="index" collection="itemIds" open="(" separator="," close=")">
            #{itemId}
        </foreach>

    </select>

    <select id="selectByRelItemIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_All" />
        FROM tb_item_relation
        WHERE is_deleted = 0
        <if test="list != null and list.size() > 0">
            AND related_item_id in (
            <foreach collection="list" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
</mapper>