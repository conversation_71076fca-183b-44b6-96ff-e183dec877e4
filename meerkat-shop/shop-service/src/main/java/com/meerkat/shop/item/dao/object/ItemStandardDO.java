package com.meerkat.shop.item.dao.object;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @ClassName ItemStandardDO
 * @description 标准项目字典表
 * @createTime 2022/5/6 14:42
 */
public class ItemStandardDO {

    /**
     * id
     */
    private Integer id;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目名称拼音
     */
    private String pinyin;

    /**
     * 性别: 0-女 1-男 2-通用
     */
    private Integer gender;

    /**
     * 婚姻状态: 1- 已婚 2-通用
     */
    private Integer marriageStatus;

    /**
     * 详情介绍
     */
    private String description;

    /**
     * 排序
     */
    private Integer sequence;

    /**
     * 关键词
     */
    private String keyWord;

    /**
     * 是否删除：0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getMarriageStatus() {
        return marriageStatus;
    }

    public void setMarriageStatus(Integer marriageStatus) {
        this.marriageStatus = marriageStatus;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public LocalDateTime getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(LocalDateTime gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }
}
