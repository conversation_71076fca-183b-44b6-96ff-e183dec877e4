package com.meerkat.shop.calculate;

import com.google.common.collect.Lists;
import com.meerkat.common.exception.BizException;
import com.meerkat.shop.goods.model.GoodsItem;
import com.meerkat.shop.item.enums.ItemDiscountableEnum;
import com.meerkat.shop.item.exception.ItemBizExEnum;
import com.meerkat.shop.item.model.Item;
import com.meerkat.shop.item.model.ItemPackageItem;
import com.meerkat.shop.item.service.ItemService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName CalculateManager.java
 * @Description TODO
 * @createTime 2022-03-14 15:50:00
 */
@Component
public class CalculateManager {

    @Resource
    private ItemService itemService;

    public List<ItemPackageItem> itemPackageCal(Long itemPkgId, Long price, List<ItemPackageItem> itemPkgItems) {

        if (CollectionUtils.isEmpty(itemPkgItems)) {
            return Lists.newArrayList();
        }
        List<Long> itemIds = itemPkgItems.stream().map(ItemPackageItem::getItemId).collect(Collectors.toList());
        List<Item> items = itemService.listItemByIds(itemIds);

        // 项目总价
        Long itemPrice = 0L;

        // 可折扣项目总价
        Long discountableItemPrice = 0L;
        for (Item item: items) {
            itemPrice += item.getPrice();
            if (item.getDiscountable() == ItemDiscountableEnum.CAN_DISCOUNT.getCode()) {
                discountableItemPrice += item.getPrice();
            }
        }

        // 项目总价与售价之差
        Long differencePrice = itemPrice - price;

        // 项目分可打折和不可打折
        Map<Integer, List<Item>> discountGroup = items.stream().collect(Collectors.groupingBy(Item::getDiscountable));
        List<Item> discountItems = discountGroup.get(ItemDiscountableEnum.CAN_DISCOUNT.getCode());
        List<Item> noDiscountItems = discountGroup.get(ItemDiscountableEnum.CAN_NOT_DISCOUNT.getCode());

        Map<Long, ItemPackageItem> itemPkgItemMap = itemPkgItems.stream()
                .collect(Collectors.toMap(ItemPackageItem::getItemId, ItemPackageItem -> ItemPackageItem));

        // 加项包项目算价结果
        List<ItemPackageItem> calculatePkgItems = Lists.newArrayList();

        // 不可打折项目写入
        if (CollectionUtils.isNotEmpty(noDiscountItems)) {
            for(Item item: noDiscountItems) {
                ItemPackageItem itemPkgItem = itemPkgItemMap.get(item.getId());
                ItemPackageItem targetItemPkgItem= new ItemPackageItem();
                targetItemPkgItem.setItemId(item.getId());
                targetItemPkgItem.setItemPkgId(itemPkgId);
                targetItemPkgItem.setIsNecessary(itemPkgItem.getIsNecessary() == null? 1: itemPkgItem.getIsNecessary());
                targetItemPkgItem.setEnableSelect(itemPkgItem.getEnableSelect());
                targetItemPkgItem.setPrice(item.getPrice());
                calculatePkgItems.add(targetItemPkgItem);
            }
        }

        // 打折项目
        if (CollectionUtils.isNotEmpty(discountItems)) {
            int size = discountItems.size();
            Long shareTotalPrice = 0L;
            for (Item item: discountItems) {
                ItemPackageItem itemPkgItem = itemPkgItemMap.get(item.getId());
                ItemPackageItem targetItemPkgItem= new ItemPackageItem();
                targetItemPkgItem.setItemId(item.getId());
                targetItemPkgItem.setItemPkgId(itemPkgId);
                targetItemPkgItem.setIsNecessary(itemPkgItem.getIsNecessary() == null? 1: itemPkgItem.getIsNecessary());
                targetItemPkgItem.setEnableSelect(itemPkgItem.getEnableSelect());
                if (size == 1) {
                    targetItemPkgItem.setPrice(item.getPrice() - (differencePrice- shareTotalPrice));
                } else {
                    Long sharePrice = new BigDecimal(item.getPrice())
                            .divide(new BigDecimal(discountableItemPrice), 6, RoundingMode.UP)
                            .multiply(new BigDecimal(differencePrice)).longValue();
                    targetItemPkgItem.setPrice(item.getPrice() - sharePrice);
                    shareTotalPrice += sharePrice;
                }
                calculatePkgItems.add(targetItemPkgItem);
                size--;
            }
        }
        return calculatePkgItems;

    }

    public List<GoodsItem> goodsCal(Long goodsId, Long price, List<GoodsItem> goodsItems) {

        if (CollectionUtils.isEmpty(goodsItems)) {
            return Lists.newArrayList();
        }

        List<Long> itemIds = goodsItems.stream().map(GoodsItem::getItemId).collect(Collectors.toList());
        List<Item> items = itemService.listItemByIds(itemIds);
        if (CollectionUtils.isEmpty(items)) {
            throw new BizException(ItemBizExEnum.ITEM_NOT_FOUND);
        }

        // 项目总价
        Long itemPrice = 0L;
        // 不可可折扣项目总价
        Long noDiscountableItemPrice = 0L;
        Long discountableItemPrice = 0L;
        List<Item> discountItems = Lists.newArrayList();
        List<Item> noDiscountItems = Lists.newArrayList();
        for (Item item: items) {
            itemPrice += item.getPrice();
            if (item.getDiscountable() == ItemDiscountableEnum.CAN_DISCOUNT.getCode() && item.getPrice() != 0) {
                discountableItemPrice += item.getPrice();
                discountItems.add(item);
            }else {
                noDiscountableItemPrice+= item.getPrice();
                noDiscountItems.add(item);
            }
        }
        if (price < noDiscountableItemPrice) {
            throw new BizException(ItemBizExEnum.ITEM_SHARE_PRICE_NOT_EQUAL);
        }

        boolean flag = true;
        // 套餐售价为0时，项目必须全为必选项目
        if(Objects.equals(0L, noDiscountableItemPrice) && Objects.equals(0L, price)) {
            for (GoodsItem goodsItem : goodsItems) {
                if (!(Objects.equals(1, goodsItem.getIsNecessary()) &&
                        Objects.equals(0, goodsItem.getEnableSelect())
                )) {
                    flag = false;
                    break;
                }
            }
        }
        if(!flag) {
            throw new BizException(ItemBizExEnum.ITEM_MUST_NECESSARY);
        }

        List<GoodsItem> calculateGoodsItems = Lists.newArrayList();
        Map<Long, GoodsItem> sourceGoodsItemMap = goodsItems.stream().collect(Collectors.toMap(GoodsItem::getItemId, GoodsItem -> GoodsItem));

        if (CollectionUtils.isNotEmpty(noDiscountItems)) {
            for(Item item: noDiscountItems) {
                GoodsItem sourceGoodsItem = sourceGoodsItemMap.get(item.getId());
                GoodsItem targetGoodsItem = new GoodsItem();
                targetGoodsItem.setItemId(item.getId());
                targetGoodsItem.setGoodsId(goodsId);
                targetGoodsItem.setIsNecessary(sourceGoodsItem.getIsNecessary() == null? 1: sourceGoodsItem.getIsNecessary());
                targetGoodsItem.setSequence(sourceGoodsItem.getSequence());
                targetGoodsItem.setPrice(item.getPrice());
                targetGoodsItem.setIsShow(sourceGoodsItem.getIsShow() == null? 1: sourceGoodsItem.getIsShow());
                targetGoodsItem.setIsImportant(sourceGoodsItem.getIsImportant() == null ? 0: sourceGoodsItem.getIsImportant());
                targetGoodsItem.setEnableSelect(sourceGoodsItem.getEnableSelect());
                calculateGoodsItems.add(targetGoodsItem);
            }
        }
        //可打折项目售价
        long discountItemSalePrice = price - noDiscountableItemPrice;

        int finishSharePrice = 0;
        int currentTotalMoney = 0;
        if (CollectionUtils.isNotEmpty(discountItems)) {
            for (int i = 0; i < discountItems.size(); i++) {
                long shareSalePrice;
                Item item = discountItems.get(i);
                GoodsItem sourceGoodsItem = sourceGoodsItemMap.get(item.getId());
                GoodsItem targetGoodsItem = new GoodsItem();
                targetGoodsItem.setItemId(item.getId());
                targetGoodsItem.setGoodsId(goodsId);
                targetGoodsItem.setIsNecessary(sourceGoodsItem.getIsNecessary() == null ? 1 : sourceGoodsItem.getIsNecessary());
                targetGoodsItem.setSequence(sourceGoodsItem.getSequence());
                if (currentTotalMoney < discountItemSalePrice) {
                    //打折项目价格
                     shareSalePrice = new BigDecimal(item.getPrice())
                            .divide(new BigDecimal(discountableItemPrice), 7, RoundingMode.UP)
                            .multiply(new BigDecimal(discountItemSalePrice)).longValue();
                    //单项分摊售价的价格
                    currentTotalMoney += shareSalePrice;
                    if (currentTotalMoney > discountItemSalePrice) {
                        shareSalePrice = shareSalePrice + (discountItemSalePrice - currentTotalMoney);
                    }
                }else {
                    shareSalePrice = 0;
                }
                //最后一项的价格为:分摊总价-已分摊价格之和
                if (i == discountItems.size() - 1) {
                    targetGoodsItem.setPrice(discountItemSalePrice - finishSharePrice);
                } else {
                    targetGoodsItem.setPrice(shareSalePrice);
                }
                targetGoodsItem.setIsShow(sourceGoodsItem.getIsShow() == null ? 1 : sourceGoodsItem.getIsShow());
                targetGoodsItem.setIsImportant(sourceGoodsItem.getIsImportant() == null ? 0 : sourceGoodsItem.getIsImportant());
                targetGoodsItem.setEnableSelect(sourceGoodsItem.getEnableSelect());
                calculateGoodsItems.add(targetGoodsItem);
                finishSharePrice += shareSalePrice;
            }
        }

        Long calculatePrice = 0L;
        for (GoodsItem goodsItem: calculateGoodsItems) {
            calculatePrice += goodsItem.getPrice();
        }
        if (! calculatePrice.equals(price)) {
            throw new BizException(ItemBizExEnum.ITEM_SHARE_PRICE_NOT_EQUAL);
        }

        return calculateGoodsItems;
    }

}
