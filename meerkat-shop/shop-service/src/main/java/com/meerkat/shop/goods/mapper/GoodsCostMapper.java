package com.meerkat.shop.goods.mapper;

import com.meerkat.shop.goods.dao.object.GoodsCostDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface GoodsCostMapper {

    /**
     * 获取有效成本
     * @return
     */
    GoodsCostDO selectEffectiveCost(@Param("goodsId") Long goodsId, @Param("effDate") LocalDate effDate);

    int insert(GoodsCostDO goodsCostDO);

    int logicDel(@Param("id") Long id);

    List<GoodsCostDO> selectListByGoodsIdList(@Param("goodsIdList") List<Long> goodsIdList);
}
