package com.meerkat.shop.item.mapper;

import com.meerkat.shop.item.dao.object.ItemPackageDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ItemPackageMapper.java
 * @Description TODO
 * @createTime 2022-03-01 11:46:00
 */
@Mapper
public interface ItemPackageMapper {

    /**
     * 根据ID 查询
     * @param id
     * @return
     */
    ItemPackageDO queryById(Long id);

    /**
     * 写入加项包数据
     * @param itemPackageDO
     * @return
     */
    int insert(ItemPackageDO itemPackageDO);

    /**
     * 删除
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 更新加项包
     * @param itemPackageDO
     * @return
     */
    int update(ItemPackageDO itemPackageDO);


    /**
     * 通过id集合批量查询数据
     * @param ids 主键集合
     * @return
     */
    List<ItemPackageDO> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 通过类型和名称获取机构加项包
     * @param name
     * @param types
     * @param orgaId
     * @return
     */
    List<ItemPackageDO> selectByTypeWithName(@Param("orgaId") Long orgaId, @Param("name") String name,
                                             @Param("types") List<Integer> types);

    /**
     * 根据条件查询
     * @param itemPackageDO
     * @return
     */
    List<ItemPackageDO> selectByCondition(ItemPackageDO itemPackageDO);

    /**
     * 根据加项包id批量查询加项包
     * @param itemPkgIds
     * @return
     */
    List<ItemPackageDO> selectByItemPkgIds(@Param("list") List<Long> itemPkgIds);


    /**
     * 根据机构id查询加项包
     * @param orgaId 机构id
     * @return
     */
    List<ItemPackageDO> selectByOrgaId(@Param("orgaId") Long orgaId);
}
