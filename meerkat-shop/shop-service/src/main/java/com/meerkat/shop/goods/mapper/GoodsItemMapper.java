package com.meerkat.shop.goods.mapper;

import com.meerkat.shop.goods.dao.object.GoodsItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName GoodsItemMapper.java
 * @Description TODO
 * @createTime 2021-09-28 17:07:00
 */
@Mapper
public interface GoodsItemMapper {

    /**
     * 写入商品项目
     * @param goodsItemDO
     * @return
     */
    int insert(GoodsItemDO goodsItemDO);

    /**
     * 批量写入商品项目
     * @param goodsItemDOs
     * @return
     */
    int batchInsert(List<GoodsItemDO> goodsItemDOs);

    /**
     * 更新商品项目信息
     * @param goodsItemDO
     * @return
     */
    int update(GoodsItemDO goodsItemDO);

    /**
     * 通过商品ID逻辑删除商品项目
     * @param goodsId
     * @return
     */
    int logicDeleteByGoodsId(Long goodsId);

    /**
     * 通过商品ID查询商品项目
     * @param goodsId
     * @return
     */
    List<GoodsItemDO> selectByGoodsId(@Param("goodsId") Long goodsId);

    /**
     * 通过商品ID集合查询商品项目
     * @param goodsIds
     * @return
     */
    List<GoodsItemDO> selectByGoodsIds(@Param("goodsIds") List<Long> goodsIds);

}
