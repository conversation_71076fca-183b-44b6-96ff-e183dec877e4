package com.meerkat.shop.goods.service.impl;

import com.google.common.collect.Lists;
import com.meerkat.shop.goods.dao.object.GoodsRecommendDO;
import com.meerkat.shop.goods.enums.GoodsTypeEnum;
import com.meerkat.shop.goods.mapper.GoodsRecommendMapper;
import com.meerkat.shop.goods.model.GoodsRecommend;
import com.meerkat.shop.goods.service.GoodsRecommendService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName GoodsRecommendServiceImpl.java
 * @Description TODO
 * @createTime 2021-10-19 15:13:00
 */

@Service
public class GoodsRecommendServiceImpl implements GoodsRecommendService {

    @Autowired
    private GoodsRecommendMapper recommendMapper;

    @Override
    public List<GoodsRecommend> listGoodsRecommend(List<Long> orgaIds, List<GoodsTypeEnum> goodsTypeEnums) {
        if(CollectionUtils.isEmpty(orgaIds)){
            return Lists.newArrayList();
        }
        List<Integer> goodsTypeList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(goodsTypeEnums)){
            goodsTypeEnums.stream().forEach( goodsTypeEnum ->{
                goodsTypeList.add(goodsTypeEnum.getCode());
            });
        }
        List<GoodsRecommendDO> goodsRecoDOS = recommendMapper.selectByOrgaIds(orgaIds, goodsTypeList);

        return do2Model(goodsRecoDOS);
    }

    @Override
    public List<GoodsRecommend> listByGoodsIdList(List<Long> goodsIdList) {
        return do2Model(recommendMapper.selectByGoodsIdList(goodsIdList));
    }


    private List<GoodsRecommend> do2Model(List<GoodsRecommendDO> goodsRecoDOS){
        if (CollectionUtils.isEmpty(goodsRecoDOS)){
            return Lists.newArrayList();
        }

        List<GoodsRecommend> goodsRecommends = Lists.newArrayList();
        goodsRecoDOS.stream().forEach(goodsRecoDO->{
            GoodsRecommend goodsRecommend = new GoodsRecommend();
            BeanUtils.copyProperties(goodsRecoDO, goodsRecommend);
            goodsRecommends.add(goodsRecommend);
        });

        return goodsRecommends;
    }
}
