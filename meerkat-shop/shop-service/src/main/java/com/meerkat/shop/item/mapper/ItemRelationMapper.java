package com.meerkat.shop.item.mapper;

import com.meerkat.shop.item.dao.object.ItemRelationDO;
import com.meerkat.shop.item.model.ItemRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ItemRelationMapper.java
 * @Description TODO
 * @createTime 2022-01-10 15:08:00
 */
@Mapper
public interface ItemRelationMapper {

    /**
     *  写入项目关系
     * @param itemRelationDO
     * @return
     */
    int insert(ItemRelationDO itemRelationDO);

    /**
     * 批量写入项目关系
     * @param itemRelationDOS
     * @return
     */
    int batchInsert(@Param("list") List<ItemRelationDO> itemRelationDOS);

    /**
     * 删除项目关系
     * @param id
     * @return
     */
    int delete(int id);

    /**
     * 批量删除项目关系
     * @param ids
     * @return
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 通过item id删除
     * @param itemId
     * @param orgaId
     * @return
     */
    int deleteByItemId(@Param("itemId")Long itemId, @Param("orgaId")Long orgaId);

    /**
     * 根据机构ID和关系类型，分组查询项目关系
     * @param orgaId
     * @param types
     * @param groupIds
     * @return
     */
    List<ItemRelationDO> selectByCondition(@Param("orgaId") Long orgaId,
                                           @Param("types") List<Integer> types,
                                           @Param("groupIds") List<Integer> groupIds);

    /**
     * 通过项目ID集合查询项目关系
     * @param itemIds
     * @return
     */
    List<ItemRelationDO> selectByItemIds(@Param("list") List<Long> itemIds);

    List<ItemRelationDO> selectAllItemRelationByItemIds(@Param("itemIds") List<Long> itemIds);

    /**
     * 根据项目ID&关系类型查询
     * @param itemId
     * @param type
     * @return
     */
    List<ItemRelationDO> selectByItemIdAndType(@Param("itemId")Long  itemId,
                                               @Param("type") Integer type);

    /**
     * 获取组项目关系信息
     * @param itemId
     * @return
     */
    List<ItemRelationDO> selectGroupRelationByItemId(@Param("itemId") Long itemId);

    /**
     * 根据关联项目ID&关系类型查询
     * @param relItemId
     * @param type
     * @return
     */
    List<ItemRelationDO> selectByRelItemIdAndType(@Param("relItemId")Long relItemId,
                                                  @Param("type") Integer type);

    /**
     * 获取关系 通过 item id
     * @param itemId
     * @return
     */
    List<ItemRelationDO> selectByItemId(@Param("itemId") Long itemId);

    /**
     *
     * @param orgaId
     * @param relatedItemIdList
     * @param type
     * @return
     */
    List<ItemRelationDO> selectByRelatedItemId(@Param("orgaId")Long orgaId, @Param("relatedItemIdList")List<Long> relatedItemIdList, @Param("type")Integer type);

    /**
     * 删除关系
     * @param orgaId
     * @param itemId
     * @param relatedItemId
     * @param type
     * @return
     */
    int deleteItemRelation(@Param("orgaId")Long orgaId, @Param("itemId")Long itemId,@Param("relatedItemId")Long relatedItemId,
                           @Param("type")Integer type);

    /**
     * 查询ItemRelationDO
     * @param orgaId
     * @param itemId
     * @param relatedItemId
     * @param type
     * @return
     */
    List<ItemRelationDO> selectItemRelation(@Param("orgaId")Long orgaId, @Param("itemId")Long itemId,@Param("relatedItemId")Long relatedItemId,
                                            @Param("type")Integer type);


    /**
     * 通过机构Id查询数据
     * @param orgaId 机构id
     * @return
     */
    List<ItemRelationDO> selectByOrgaId(@Param("orgaId") Long orgaId);


    /**
     * 通过机构Id查询数据
     * @param orgaId 机构id
     * @param groupId 机构id
     * @return
     */
    List<ItemRelationDO> selectByOrgIdAndGroupId(@Param("orgaId") Long orgaId,
                                                 @Param("groupId") Integer groupId);


    /**
     * 获取机构中组最大的gorupId
     * @param orgaId
     * @return
     */
    Integer queryNextGroupId(@Param("orgaId") Long orgaId);

    List<ItemRelationDO> selectByRelItemIds(@Param("list") List<Long> relItemIds);
}
