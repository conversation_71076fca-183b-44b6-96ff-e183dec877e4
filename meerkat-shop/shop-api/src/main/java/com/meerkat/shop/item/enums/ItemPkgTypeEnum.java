package com.meerkat.shop.item.enums;

/**
 * <AUTHOR>
 * @ClassName ItemPkgTypeEnum.java
 * @Description TODO
 * @createTime 2022-03-23 13:48:00
 */
public enum ItemPkgTypeEnum {

    COMMON(1, "通用"),

    RISK(2, "风险"),

    SPECIAL(3, "专项"),

    ;

    final private int code;

    final private String message;

    ItemPkgTypeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
