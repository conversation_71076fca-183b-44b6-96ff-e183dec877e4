package com.meerkat.shop.goods.enums;

/**
 * <AUTHOR>
 * @EnumName GoodsSurveyEnum
 * @description
 * @createTime 2022/4/18 15:22
 */
public enum GoodsSurveyEnum {

    /**
     * 不是问卷商品
     */
    FALSE(0, "不是"),

    /**
     * 是问卷商品
     */
    TURE(1, "是"),
    ;

    private int code;

    private String name;

    GoodsSurveyEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
