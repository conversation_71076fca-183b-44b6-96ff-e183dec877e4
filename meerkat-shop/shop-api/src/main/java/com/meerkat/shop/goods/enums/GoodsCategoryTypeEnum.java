package com.meerkat.shop.goods.enums;

/**
 * <AUTHOR>
 * @ClassName GoodsCategoryTypeEnum.java
 * @Description TODO
 * @createTime 2022-01-11 14:07:00
 */
public enum GoodsCategoryTypeEnum {

    BASE_CATEGORY(1, "后台类目"),

    FRONT_CATEGORY(2, "前台类目"),

    ;

    private int code;

    private String name;

    private GoodsCategoryTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static GoodsCategoryTypeEnum getByCode(int code) {
        for (GoodsCategoryTypeEnum goodsCategoryTypeEnum : GoodsCategoryTypeEnum.values()) {
            if (goodsCategoryTypeEnum.getCode() == code) {
                return goodsCategoryTypeEnum;
            }
        }
        return null;
    }
}
