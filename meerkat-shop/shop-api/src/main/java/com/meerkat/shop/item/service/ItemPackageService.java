package com.meerkat.shop.item.service;

import com.meerkat.common.db.PageView;
import com.meerkat.evaluation.risk.model.diseaserisk.SpecialInspectionPackageRisk;
import com.meerkat.shop.goods.enums.GoodsGenderEnum;
import com.meerkat.shop.goods.enums.GoodsMarriageStatusEnum;
import com.meerkat.shop.item.model.ItemPackage;
import com.meerkat.shop.item.param.ItemPkgQueryParam;

import java.util.List;

/**
 * @className: com.meerkat.shop.item.service
 * @author: yaotongxin
 * @date: 2022/3/1 3:47 PM
 * @description:
 */
public interface ItemPackageService {

    /**
     * 通过加项包ID获取加项包
     * @param pkgIds
     * @param needItem
     * @return
     */
    List<ItemPackage> listItemPkgByPkgIds(List<Long> pkgIds,boolean needItem);

     /** 查询加项包
     * @param itemPackageId
     * @return
     */
    ItemPackage getItemPackageById(Long itemPackageId);

    /** 查询加项包,包含明细
     * @param itemPackageId
     * @return
     */
    ItemPackage getFullItemPackageById(Long itemPackageId);


    /**
     * 获取加项包列表
     * @param itemPkgQueryParam
     * @return
     */
    PageView<ItemPackage> list(ItemPkgQueryParam itemPkgQueryParam);


    /**
     * 删除减项包
     * @param itemPackageId
     * @return
     */
    Integer deleteById(Long itemPackageId);

    /**
     * 保存修改
     * @param itemPackage
     * @return
     */
    Integer save(ItemPackage itemPackage);


    /**
     * 通过机构id查询所有加项包信息
     * @param orgaId
     * @return
     */
    List<ItemPackage> listItemPackageByOrgaId(Long orgaId);


    /**
     * 生成空的专项检查并关联疾病风险
     * @param itemPackage
     */
    void relationDiseaseRisk(ItemPackage itemPackage, List<SpecialInspectionPackageRisk> specialInspectionPackageRiskList);

    /**
     * 通过婚姻状态、婚否过滤id
     *
     * @param itemPackageIdList
     * @param acceptorGender
     * @param marriageStatusEnum
     * @param chorgaId
     * @param chorgaType
     * @return
     */
    List<Long> filterByGenderAndMarriage(List<Long> itemPackageIdList, GoodsGenderEnum acceptorGender, GoodsMarriageStatusEnum marriageStatusEnum, Long chorgaId, Integer chorgaType);
}
