package com.meerkat.shop.tag.service;

import com.meerkat.shop.tag.model.TagCategory;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TagCategoryService.java
 * @Description TODO
 * @createTime 2021-10-18 13:40:00
 */
public interface TagCategoryService {

    /**
     * 获取标签类目信息
     * @param ids
     * @return
     */
    List<TagCategory> listTagCategory(List<Integer> ids);

    /**
     *  获取所有标签类目详情信息
     * @return
     */
    List<TagCategory> listTagCategoryDetail();

    List<TagCategory> listAll();

    /**
     * 根据类目类型获取标签类目信息
     * @param type
     * @return
     */
    List<TagCategory> listTagCategoryByType(Integer type);

    /**
     * 新增
     * @param tagCategory
     * @return
     */
    int add(TagCategory tagCategory);
}
