package com.meerkat.base.model;

import com.meerkat.base.enums.OrgaBizTypeEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName SystemParam.java
 * @Description TODO
 * @createTime 2021-10-25 17:09:00
 */
public class SystemParam implements Serializable {

    private static final long serialVersionUID = -8235994483131170441L;

    private Long id;

    /**
     * 机构id
     */
    private Long organizationId;

    /**
     * 参数键
     */
    private String paramKey;

    /**
     * 参数值
     */
    private String paramValue;

    /**
     * 参数说明
     */
    private String description;

    /**
     * 机构业务类型
     */
    private OrgaBizTypeEnum orgaBizTypeEnum;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public String getParamKey() {
        return paramKey;
    }

    public void setParamKey(String paramKey) {
        this.paramKey = paramKey;
    }

    public String getParamValue() {
        return paramValue;
    }

    public void setParamValue(String paramValue) {
        this.paramValue = paramValue;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public OrgaBizTypeEnum getOrgaBizTypeEnum() {
        return orgaBizTypeEnum;
    }

    public void setOrgaBizTypeEnum(OrgaBizTypeEnum orgaBizTypeEnum) {
        this.orgaBizTypeEnum = orgaBizTypeEnum;
    }
}
