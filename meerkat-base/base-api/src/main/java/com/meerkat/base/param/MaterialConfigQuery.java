package com.meerkat.base.param;

import java.io.Serializable;
import java.lang.Integer;

/**
 * <p>
 * 素材配置表,查询条件
 * </p>
 *
 * <AUTHOR>
 * @since 2022/08/31 11:51:24
 */
public class MaterialConfigQuery implements Serializable{

    private static final long serialVersionUID = 1564823141716062208L;
    /**
     * 对应页面展示位置1，后期需要会添加tb_location表存储前端页面元素位置表
     */
    private Integer locationId;
    /**
     * tb_address的id
     */
    private Integer addressId;
    /**
     * 获取对应页面展示位置1，后期需要会添加tb_location表存储前端页面元素位置表
     */
    public Integer getLocationId() {
        return locationId;
    }
    /**
     * 写入对应页面展示位置1，后期需要会添加tb_location表存储前端页面元素位置表
     */
    public void setLocationId(Integer locationId) {
        this.locationId = locationId;
    }
    /**
     * 获取tb_address的id
     */
    public Integer getAddressId() {
        return addressId;
    }
    /**
     * 写入tb_address的id
     */
    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }
}