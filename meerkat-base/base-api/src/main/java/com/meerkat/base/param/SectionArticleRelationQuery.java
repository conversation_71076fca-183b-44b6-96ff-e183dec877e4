package com.meerkat.base.param;

import java.io.Serializable;
import java.lang.Integer;

/**
 * <p>
 * 板块文章关联表,查询条件
 * </p>
 *
 * <AUTHOR>
 * @since 2022/08/31 11:48:46
 */
public class SectionArticleRelationQuery implements Serializable{

    private static final long serialVersionUID = 1564822476570624000L;
    /**
     * 文章id
     */
    private Integer sectionId;
    /**
     * 文章id
     */
    private Long articleId;
    /**
     * 获取文章id
     */
    public Integer getSectionId() {
        return sectionId;
    }
    /**
     * 写入文章id
     */
    public void setSectionId(Integer sectionId) {
        this.sectionId = sectionId;
    }
    /**
     * 获取文章id
     */
    public Long getArticleId() {
        return articleId;
    }
    /**
     * 写入文章id
     */
    public void setArticleId(Long articleId) {
        this.articleId = articleId;
    }
}