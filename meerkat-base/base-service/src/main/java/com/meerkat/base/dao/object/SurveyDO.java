package com.meerkat.base.dao.object;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 问卷
 * <AUTHOR>
 */
public class SurveyDO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 问卷标题
     */
    private String title;

    /**
     * 图片url
     */
    private String imageUrl;

    /**
     * 描述
     */
    private String describe;

    /**
     * 问卷类型:1普通问卷、2分值问卷、3条件问卷
     */
    private Integer type;

    /**
     * 适用场景：1-通用类型 2-疫情问卷 3-满意度问卷 4-健康问卷
     */
    private Integer scene;

    /**
     * 目标分值
     */
    private Integer targetScore;

    private Integer status;

    private Integer isDeleted;
    private LocalDateTime gmtCreated;
    private LocalDateTime gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public LocalDateTime getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(LocalDateTime gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getScene() {
        return scene;
    }

    public void setScene(Integer scene) {
        this.scene = scene;
    }

    public Integer getTargetScore() {
        return targetScore;
    }

    public void setTargetScore(Integer targetScore) {
        this.targetScore = targetScore;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "SurveyDO{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", describe='" + describe + '\'' +
                ", type=" + type +
                ", scene=" + scene +
                ", targetScore=" + targetScore +
                ", status=" + status +
                ", isDeleted=" + isDeleted +
                ", gmtCreated=" + gmtCreated +
                ", gmtModified=" + gmtModified +
                '}';
    }
}
