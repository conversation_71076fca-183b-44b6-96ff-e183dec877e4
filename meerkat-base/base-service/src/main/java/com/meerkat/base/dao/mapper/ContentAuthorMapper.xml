<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.base.dao.mapper.ContentAuthorMapper">

    <resultMap id="BaseResultMap" type="com.meerkat.base.dao.object.ContentAuthorDO">
        <result column="id" property="id"/>
        <result column="nickname" property="nickname"/>
        <result column="face" property="face"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <sql id="Base_Column_List">
        T1.id,
        T1.nickname,
        T1.face,
        T1.is_deleted,
        T1.gmt_created,
        T1.gmt_modified
    </sql>

    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM tb_content_author T1 WHERE T1.id IN (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="selectOneById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM tb_content_author T1 WHERE id = #{id} 
    </select>

    <!--条件查询多条-->
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM tb_content_author T1
        <where>
        </where>
    </select>

    <!--新增一条-->
    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.meerkat.base.dao.object.ContentAuthorDO">
        INSERT INTO tb_content_author
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != nickname and '' != nickname">
                nickname,
            </if>
            <if test="null != face and '' != face">
                face,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != nickname and '' != nickname">
                #{nickname},
            </if>
            <if test="null != face and '' != face">
                #{face},
            </if>
        </trim>
    </insert>

    <!--根据主键id更新-->
    <update id="updateById">
        update tb_content_author
        <set>
            <trim suffixOverrides=",">
            <if test="null != nickname and '' != nickname">
                nickname = #{nickname},
            </if>
            <if test="null != face and '' != face">
                face = #{face},
            </if>
            <if test="null != isDeleted">
                is_deleted = #{isDeleted},
            </if>
            </trim>
        </set>
        <where>
            id = #{id}
        </where>
    </update>
</mapper>