<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.base.mapper.SurveyMapper">

    <resultMap id="BaseResultMap" type="com.meerkat.base.dao.object.SurveyDO">
        <result column="id" property="id" />
        <result column="title" property="title" />
        <result column="image_url" property="imageUrl" />
        <result column="describe" property="describe" />
        <result column="type" property="type" />
        <result column="scene" property="scene" />
        <result column="target_score" property="targetScore" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <sql id="all_column">
        id, title, image_url, `describe`, type, scene, target_score, status, is_deleted, gmt_created, gmt_modified
    </sql>

    <sql id="insert_column">
        title, image_url, `describe`, type, scene, target_score, status
    </sql>

    <insert id="insert" parameterType="com.meerkat.base.dao.object.SurveyDO" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_survey(<include refid="insert_column" />)
        value(#{title}, #{imageUrl}, #{describe}, #{type}, #{scene}, #{targetScore}, #{status})
    </insert>

    <select id="selectById" parameterType="java.util.Map" resultMap="BaseResultMap">
        select <include refid="all_column" />
        from tb_survey
        where id = #{id} and is_deleted = 0
    </select>

    <select id="selectByIds" parameterType="java.util.Map" resultMap="BaseResultMap">
        select <include refid="all_column" />
        from tb_survey
        where id in
        (
            <foreach collection="list" item="item" separator=",">
                #{item}
            </foreach>
        ) and is_deleted = 0
    </select>

    <update id="logicDel" parameterType="java.util.Map">
        update tb_survey set is_deleted = 1
        where id = #{id}
    </update>

    <update id="update" parameterType="com.meerkat.base.dao.object.SurveyDO">
        update tb_survey
            set title=#{title}, image_url=#{imageUrl}, `describe`=#{describe}, type=#{type}, scene=#{scene}, target_score=#{targetScore}
        where id = #{id}
    </update>

    <update id="switchStatus" parameterType="java.util.Map">
        update tb_survey set status = (status = 0) where id = #{id}
    </update>
</mapper>