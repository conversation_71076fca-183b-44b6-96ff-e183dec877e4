package com.meerkat.base.service.impl;

import cn.hutool.json.JSONUtil;
import com.meerkat.base.dao.object.UserSurveyRecordDO;
import com.meerkat.base.dao.mapper.UserSurveyRecordMapper2;
import com.meerkat.base.model.SurveyAnswerRecord;
import com.meerkat.base.model.UserSurveyRecord;
import com.meerkat.base.service.UserSurveyRecordService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service("userSurveyRecordServiceImpl2")
public class UserSurveyRecordServiceImpl implements UserSurveyRecordService {

    @Resource
    private UserSurveyRecordMapper2 userSurveyRecordMapper2;



    @Override
    public int addUserSurveyRecord(UserSurveyRecord record) {
        completion(record);
        UserSurveyRecordDO doObj = model2do(record);
        int count = userSurveyRecordMapper2.insert(doObj);
        record.setId(doObj.getId());
        return count;
    }

    @Override
    public boolean judge(Long orgaId, Long surveyId, Long acceptorId, Integer hour) {
        LocalDateTime theLatest = userSurveyRecordMapper2.selectLatestDate(orgaId, surveyId, acceptorId);
        if (theLatest == null) {
            return true;
        }

        theLatest = theLatest.plusHours(hour);
        return theLatest.isBefore(LocalDateTime.now());
    }


    // ================== util method ==================

    /**
     * model 2 do
     */
    private UserSurveyRecordDO model2do(UserSurveyRecord record) {
        UserSurveyRecordDO doObj = new UserSurveyRecordDO();
        BeanUtils.copyProperties(record, doObj);
        List<SurveyAnswerRecord> surveyAnswerRecords = record.getSurveyAnswerRecords();
        doObj.setAnswerRecord(JSONUtil.toJsonStr(surveyAnswerRecords));
        //计算总成绩
        int score = 0;
        for (SurveyAnswerRecord surveyAnswerRecord : surveyAnswerRecords) {
            List<SurveyAnswerRecord.Answer> answers = surveyAnswerRecord.getAnswers();
            score += answers.stream().mapToInt(t -> t.getScore() == null ? 0 : t.getScore()).sum();
        }
        doObj.setScore(score);
        return doObj;
    }

    /**
     * 补充数据
     * @param record
     */
    private void completion(UserSurveyRecord record) {
    }
}
