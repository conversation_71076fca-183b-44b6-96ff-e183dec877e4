package com.meerkat.base.dao.object;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/9/8 14:55
 */
public class CommonTagMarkRelationDO {

    private Long id;

    /**
     * 标签id
     */
    private Long commonTagId;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 业务id
     */
    private Long bizId;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 业务操作来源
     */
    private String optSource;

    /**
     * 逻辑删除标志，0：未删除、1：删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCommonTagId() {
        return commonTagId;
    }

    public void setCommonTagId(Long commonTagId) {
        this.commonTagId = commonTagId;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOptSource() {
        return optSource;
    }

    public void setOptSource(String optSource) {
        this.optSource = optSource;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public LocalDateTime getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(LocalDateTime gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

}
