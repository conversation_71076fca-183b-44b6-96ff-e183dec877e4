package com.meerkat.order.pulishevents.listener;

import com.meerkat.order.model.Order;
import com.meerkat.order.pulishevents.OrderChangeDatePublishEvent;
import com.meerkat.order.pulishevents.PublishMessageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 订单改期发送MQ
 * @author: pantaoling
 * @date: 2022/1/13
 */
@Component
public class OrderChangeDateMessageListener extends AbstractMessagePublishEventListener<OrderChangeDatePublishEvent> {

    private static final Logger LOG = LoggerFactory.getLogger(OrderChangeDateMessageListener.class);

    @Autowired
    private PublishMessageHelper publishMessageHelper;

    @Override
    public void onApplicationEvent(OrderChangeDatePublishEvent event) {
        Order order = event.getOrder();
        try {
            //发送异步消息
            publishMessageHelper.publish(event);
        } catch (Exception e) {
            LOG.error("发送改期消息失败,orderNum={}", order.getOrderNum(), e);
        }
    }
}
