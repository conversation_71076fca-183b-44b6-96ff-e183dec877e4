package com.meerkat.order.pulishevents.listener;

import com.meerkat.order.enums.OrderSourceEnum;
import com.meerkat.order.model.Order;
import com.meerkat.order.pulishevents.OrderCancelPublishEvent;
import com.meerkat.user.service.AcceptorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @description: 订单取消事件监听器
 * @author: pantaoling
 * @date: 2022/1/13
 */
@Component
public class OrderCancelListener extends AbstractOrderApplicationListener<OrderCancelPublishEvent> {
    private static final Logger LOG = LoggerFactory.getLogger(OrderAppointListener.class);

    @Autowired
    private AcceptorService acceptorService;

    @Override
    public void onApplicationEvent(OrderCancelPublishEvent event) {
        Order order = event.getOrder();

        try {
            //2、CRM代预约情况下，更新履约人为未操作
            if (Objects.equals(order.getSourceType(), OrderSourceEnum.CRM.getCode()) && order.getAcceptorSnapshot() != null) {
                acceptorService.updateAppointment(order.getAcceptorSnapshot().getAcceptorId(), Boolean.FALSE);
            }
        } catch (Exception e) {
            LOG.error("监听订单取消事件，将履约人更新为未操作失败,orderNum={},acceptorSnapshot={}", order.getOrderNum(), order.getAcceptorSnapshot());
        }
    }
}
