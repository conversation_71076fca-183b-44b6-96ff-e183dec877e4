package com.meerkat.order.pulishevents.listener;

import cn.hutool.json.JSONUtil;
import com.meerkat.distribution.platform.dto.param.DistributionOrderUpdateParamDTO;
import com.meerkat.distribution.platform.enums.DistributionOrderStatusEnum;
import com.meerkat.distribution.platform.model.DistributionOrder;
import com.meerkat.distribution.platform.service.DistributionOrderService;
import com.meerkat.order.model.Order;
import com.meerkat.order.pulishevents.OrderClosePublishEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 订单关单分销监听事件
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2022/5/23
 */
@Component
public class OrderCloseDistributionListener extends AbstractOrderApplicationListener<OrderClosePublishEvent> {

    private static final Logger logger = LoggerFactory.getLogger(OrderCloseDistributionListener.class);

    @Autowired
    private DistributionOrderService distributionOrderService;

    @Override
    public void onApplicationEvent(OrderClosePublishEvent event) {
        Order order = event.getOrder();
        logger.info("订单关单完成后置更新分销单状态事件，参数:{}", JSONUtil.toJsonStr(order));

        try {
            DistributionOrder distributionOrder = distributionOrderService.getByOrderNum(order.getOrderNum());
            if (distributionOrder == null) {
                logger.info("没有分销单，订单号：{}", order.getOrderNum());
                return;
            }
            // 构建更新对象
            DistributionOrderUpdateParamDTO distributionOrderUpdateParamDTO = new DistributionOrderUpdateParamDTO(
                    distributionOrder.getId(),
                    null,
                    DistributionOrderStatusEnum.CANCEL.getCode()
            );

            // 更新状态为已取消
            distributionOrderService.updateDistributionOrder(distributionOrderUpdateParamDTO);
        } catch (Exception e) {
            logger.error("分销单取消失败, orderNum={}", order.getOrderNum(), e);
        }
    }
}
