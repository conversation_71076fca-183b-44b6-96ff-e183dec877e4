package com.meerkat.order.service.processor;

import com.meerkat.order.dto.OrderContext;
import com.meerkat.order.dto.OrderResult;
import com.meerkat.order.dto.OrderStateContext;
import com.meerkat.order.validator.Checkable;

/**
 * @description: 订单状态处理器
 * @author: pantaoling
 * @date: 2021/9/29
 */
public interface OrderStateProcessor<T extends OrderContext> {

    /**
     * 处理逻辑
     * @param context
     * @return
     * @throws Exception
     */
    OrderResult action(OrderStateContext<T> context) throws Exception;

    Checkable<T> getCheckable(OrderStateContext<T> context);
}
