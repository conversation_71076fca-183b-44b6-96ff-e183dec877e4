package com.meerkat.order.validator.condition;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import cn.hutool.extra.spring.SpringUtil;
import com.meerkat.order.model.Order;
import com.meerkat.smart.organization.model.OrgaExamSettings;
import com.meerkat.smart.organization.service.OrgaSettingsService;

import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 改期时间据预约时间
 * 距离预约时间≤3个工作日，不允许改期；
 * </p>
 *
 * <AUTHOR>
 * @date 2021/12/10 17:18
 */
public class AppointDateCondition implements OrderChangeDateCondition {

    private static final String SIX = "星期六";
    private static final String SEVEN = "星期日";

    @Override
    public boolean check(Order order) {
        OrgaSettingsService orgaSettingsService = SpringUtil.getBean(OrgaSettingsService.class);
        OrgaExamSettings orgaExamSettings = orgaSettingsService.getOrgaExamSettings(order.getOrganizationId());
        int limitDay;
        if (Objects.isNull(orgaExamSettings)){
            return true;
        }else {
            limitDay = orgaExamSettings.getChangeLimitDays() == null ? 3 : orgaExamSettings.getChangeLimitDays();
        }
        Date examDate = order.getExamDate();
        if (examDate == null) {
            return true;
        }
        DateTime dateTime = DateUtil.date();
        for (int i = limitDay; i > 0; i--) {
            dateTime = DateUtil.offsetDay(dateTime, 1);
            Week week = DateUtil.dayOfWeekEnum(dateTime);
            //判断日期跳过周六周日
            if (SIX.equals(week.toChinese())) {
                i++;
            }
            if (SEVEN.equals(week.toChinese())) {
                i++;
            }
        }
        return dateTime.before(examDate);
    }
}
