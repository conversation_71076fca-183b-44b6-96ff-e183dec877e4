package com.meerkat.order.service.helper;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.order.model.snapshot.ChildOrder;
import com.meerkat.order.model.snapshot.goods.BaseInfo;
import com.meerkat.order.model.snapshot.goods.GoodsUsageLimitRule;
import com.meerkat.order.param.GoodsParam;
import com.meerkat.shop.goods.enums.GoodsBizTypeEnum;
import com.meerkat.shop.goods.model.*;
import com.meerkat.shop.goods.service.GoodsService;
import com.meerkat.shop.item.model.Item;
import com.meerkat.shop.item.model.ItemPackage;
import com.meerkat.shop.item.model.ItemPackageItem;
import com.meerkat.shop.item.service.ItemPackageItemService;
import com.meerkat.shop.item.service.ItemPackageService;
import com.meerkat.shop.item.service.ItemService;
import com.meerkat.shop.tag.model.Tag;
import com.meerkat.smart.supplier.model.Supplier;
import com.meerkat.smart.supplier.service.SupplierService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 加项包、套餐、组合商品到底什么区别？？？？？
 * @author: pantaoling
 * @date: 2022/5/25
 */
@Component
public class OrderGoodsDetailHelper {
//
//    @Autowired
//    private GoodsService goodsService;
//
//
//    @Autowired
//    private SupplierService supplierService;
//
//    @Autowired
//    private ItemService itemService;
//
//    @Autowired
//    private ItemPackageService itemPackageService;
//
//    @Autowired
//    private ItemPackageItemService itemPackageItemService;
//
//    /**
//     * 创建商品快照V2
//     *
//     * @param goodsParamList
//     * @return
//     */
//    public List<ChildOrder> createGoodsSnapshotV2(List<GoodsParam> goodsParamList) {
//        List<ChildOrder> goodsSnapshotList = new ArrayList<>();
//        if (CollectionUtils.isEmpty(goodsParamList)) {
//            return goodsSnapshotList;
//        }
//        //1、循环获取商品
//        for (GoodsParam param : goodsParamList) {
//            Goods goods = goodsService.getGoods(param.getGoodsId());
//            //2、构造商品快照
//            ChildOrder snapshot = buildGoodsSnapshotV2(goods,
//                    param.getNum(),
//                    param.getAddItemIdList(),
//                    param.getRemoveItemIdList());
//            //构建加项包快照
//            if (!CollectionUtils.isEmpty(param.getAddtionalPackageList())) {
//                setAddPackageList(snapshot, param.getAddtionalPackageList());
//            }
//            goodsSnapshotList.add(snapshot);
//        }
//        return goodsSnapshotList;
//    }
//
//    /**
//     * 通过商品构建商品快照，此方法只含有商品本身，没有加项，减项信息，商品的数量默认为1
//     * @param goodsList
//     * @return
//     */
//    public List<ChildOrder> createGoodsSnapshotV2ByGoods(List<Goods> goodsList) {
//        if (CollectionUtils.isEmpty(goodsList)) {
//            return Lists.newArrayList();
//        }
//        List<ChildOrder> childOrder = Lists.newArrayList();
//        for (Goods goods : goodsList) {
//            childOrder.add(buildGoodsSnapshotV2(goods, 1, null, null));
//        }
//        return childOrder;
//    }
//
//    /**
//     * 构建商品快照
//     * @param goods
//     * @param purchaseQuantity
//     * @param addItemIdList
//     * @param removeItemIdList
//     * @return
//     */
//    private ChildOrder buildGoodsSnapshotV2(Goods goods,
//                                            Integer purchaseQuantity,
//                                            List<Long> addItemIdList,
//                                            List<Long> removeItemIdList) {
//        ChildOrder childOrder = new ChildOrder();
//        this.setGoods(childOrder, goods, purchaseQuantity, addItemIdList, removeItemIdList);
//        return childOrder;
//    }
//
//    private void setAddPackageList(ChildOrder childOrder, List<GoodsParam.AddtionalPackageParam> addtionalPackageList) {
//        childOrder.setAddPackageList(buildAddtionalPackageSnapshot(addtionalPackageList));
//    }
//
//    private List<ChildOrder> buildAddtionalPackageSnapshot(List<GoodsParam.AddtionalPackageParam> addtionalPackageParamList) {
//        if (CollectionUtils.isEmpty(addtionalPackageParamList)) {
//            return null;
//        }
//        List<ChildOrder> addtionalPackageSnapshotList = new ArrayList<>();
//        for (GoodsParam.AddtionalPackageParam addtionalPackageParam : addtionalPackageParamList) {
//            Long packageId = addtionalPackageParam.getId();
//            ItemPackage itemPackage = itemPackageService.getItemPackageById(packageId);
//            if (itemPackage == null) {
//                continue;
//            }
//            ChildOrder addtionalPackageSnapshot = new ChildOrder();
//            BeanUtils.copyProperties(itemPackage, addtionalPackageSnapshot);
//
//            //加项包项目快照
//            List<ItemPackageItem> itemPackageItemList = itemPackage.getItemPackageItems();
//            addtionalPackageSnapshot.setSingleItemSnapshots(convertPackageItem2Snapshot(itemPackageItemList));
//
//            //加项包添加项目
//            if (CollectionUtils.isNotEmpty(addtionalPackageParam.getAddPackageItemIdList())) {
//                List<ItemPackageItem> itemPackageItemByAddList = itemPackageItemService.getByPkgIdAndItemIds(packageId, addtionalPackageParam.getAddPackageItemIdList());
//                addtionalPackageSnapshot.setAddSingleItemsSnapshots(convertPackageItem2Snapshot(itemPackageItemByAddList));
//            }
//
//            //加项包删除项目
//            if (CollectionUtils.isNotEmpty(addtionalPackageParam.getRemovePackageItemIdList())) {
//                List<ItemPackageItem> itemPackageItemByRemoveList = itemPackageItemService.getByPkgIdAndItemIds(packageId, addtionalPackageParam.getRemovePackageItemIdList());
//                addtionalPackageSnapshot.setRemoveSingleItemsSnapshots(convertPackageItem2Snapshot(itemPackageItemByRemoveList));
//            }
//
//            addtionalPackageSnapshotList.add(addtionalPackageSnapshot);
//        }
//
//        return addtionalPackageSnapshotList;
//    }
//
//    private List<ChildOrder> convertPackageItem2Snapshot(List<ItemPackageItem> itemPackageItemList) {
//
//        if (CollectionUtils.isEmpty(itemPackageItemList)) {
//            return null;
//        }
//        List<ChildOrder> itemSnapshotList = new ArrayList<>();
//        for (ItemPackageItem packageItem : itemPackageItemList) {
//            ChildOrder packageItemSnapshot = new ChildOrder();
//            Item item = packageItem.getItem();
//            if (item == null) {
//                continue;
//            }
//            BeanUtils.copyProperties(item, packageItemSnapshot);
//            //此处为从item拷贝过来的价格，先置空，取加项包项目价格
//            packageItemSnapshot.setTotalPrice(packageItem.getPrice());
//            packageItemSnapshot.setId(packageItem.getId());
//            packageItemSnapshot.setTotalPayAmount(packageItem.getPrice());
//            packageItemSnapshot.setGoodsUsageLimitRule(new GoodsUsageLimitRule(item.getGender(), item.getMarriageStatus()));
//            packageItemSnapshot.setBaseInfo(new BaseInfo(packageItem.getId(), item.getName(), item.getOrganizationId(),
//                    item.getPinyin(), item.getPrice(), item.getPrice(), packageItem.getPrice(), item.getBriefIntro(),
//                    item.getDescription(), item.getType(), null, item.getInnerItemCode()));
//            packageItemSnapshot.setTotalPrice(packageItem.getPrice());
//            packageItemSnapshot.setGmtCreated(packageItem.getGmtCreated());
//            packageItemSnapshot.setGmtModified(packageItem.getGmtModified());
//            itemSnapshotList.add(packageItemSnapshot);
//        }
//        return itemSnapshotList;
//    }
//
//
//    public void setGoods(ChildOrder childOrder, Goods goods, Integer purchaseQuantity, List<Long> addDomainIds,
//                         List<Long> removeDomainIds) {
//        childOrder.setId(goods.getId());
//        childOrder.setPurchaseQuantity(purchaseQuantity == null ? 1 : purchaseQuantity);
//        childOrder.setImages(buildImages(goods));
//        childOrder.setSingleItemSnapshots(buildItemsByGoods(goods));
//        childOrder.setRemoveSingleItemsSnapshots(buildItemsByItemIds(removeDomainIds));
//        childOrder.setAddSingleItemsSnapshots(buildItemsByItemIds(addDomainIds));
//        childOrder.setSupplierSnapshot(buildSupplierSnapshots(goods));
//        childOrder.setTagsSnapshots(buildTags(goods));
//        childOrder.setBaseInfo(buildBaseInfo(goods));
//        childOrder.setTotalPrice(childOrder.calculateTotalPrice());
//        childOrder.setTotalPayAmount(childOrder.calculateTotalNeedToPayAmount());
//        childOrder.setSubOrderNum(childOrder.getSubOrderNum());
//    }
//
//    /**
//     *  商品基础信息
//     * @param goods
//     * @return
//     */
//    private BaseInfo buildBaseInfo(Goods goods) {
//        BaseInfo baseInfo = CopyUtil.copy(goods, BaseInfo.class);
//        baseInfo.setOutCode(goods.getHisGoodsId());
//        return baseInfo;
//    }
//
//    /**
//     * 商品单项/单品快照
//     *
//     * @param goods
//     * @return
//     */
//    public List<ChildOrder> buildItemsByGoods(Goods goods) {
//        List<ChildOrder> itemsSnapshots = Lists.newArrayList();
//        if (goods.getIsCombine()) {
//            List<CombineGoods> combineGoodsList = goods.getCombineGoodsList();
//            for (CombineGoods combineGoods : combineGoodsList) {
//                //todo: 商品
//                ChildOrder childOrder = buildGoodsSnapshotV2(null, combineGoods.getNum(),
//                        null,
//                        null);
//                itemsSnapshots.add(childOrder);
//            }
//        }
//        List<GoodsItem> items = goods.getGoodsItems();
//        if (CollectionUtils.isEmpty(items)) {
//            return itemsSnapshots;
//        }
//        for (GoodsItem goodsItem : items) {
//            itemsSnapshots.add(buildItemsByGoodsItem(goodsItem));
//        }
//        return itemsSnapshots;
//    }
//
//    /**
//     * 商品单项快照
//     *
//     * @param goodsItem
//     * @return
//     */
//    public ChildOrder buildItemsByGoodsItem(GoodsItem goodsItem) {
//        if (goodsItem == null) {
//            return null;
//        }
//        Item item = goodsItem.getItem();
//        return getGoodsSnapshotV2ByItem(item, goodsItem.getPrice());
//
//    }
//
//    private List<ChildOrder> buildItemsByItemIds(List<Long> itemIds) {
//        List<ChildOrder> itemsSnapshots = Lists.newArrayList();
//        if (CollectionUtils.isEmpty(itemIds)) {
//            return itemsSnapshots;
//        }
//        List<Item> itemList = itemService.listItemByIds(itemIds);
//        for (Item item : itemList) {
//            ChildOrder childOrder = getGoodsSnapshotV2ByItem(item, item.getPrice());
//            itemsSnapshots.add(childOrder);
//        }
//        return itemsSnapshots;
//
//    }
//
//    private ChildOrder getGoodsSnapshotV2ByItem(Item item, Long salePrice) {
//        ChildOrder childOrder = new ChildOrder();
//        childOrder.setId(item.getId());
//        childOrder.setTotalPrice(salePrice);
//        childOrder.setTotalPayAmount(salePrice);
//        BaseInfo baseInfo = new BaseInfo(item.getId(), item.getName(), item.getOrganizationId(), item.getPinyin(),
//                item.getPrice(), item.getPrice(), salePrice, item.getBriefIntro(), item.getDescription(), item.getType(),
//                GoodsBizTypeEnum.SERVICE.getCode(), item.getInnerItemCode());
//        childOrder.setBaseInfo(baseInfo);
//        childOrder.setPurchaseQuantity(1);
//        childOrder.setGoodsUsageLimitRule(new GoodsUsageLimitRule(item.getGender(), item.getMarriageStatus()));
//        return childOrder;
//    }
//
//
//    /**
//     * 图片快照
//     *
//     * @param goods
//     * @return
//     */
//    public List<ChildOrder.GoodsImageSnapshot> buildImages(Goods goods) {
//        List<ChildOrder.GoodsImageSnapshot> imagesSnapshots = Lists.newArrayList();
//        List<GoodsImage> items = goods.getGoodsImages();
//        if (CollectionUtil.isEmpty(items)) {
//            return new ArrayList<>();
//        }
//
//        for (GoodsImage item : items) {
//            ChildOrder.GoodsImageSnapshot snapshot = new ChildOrder.GoodsImageSnapshot();
//            BeanUtils.copyProperties(item, snapshot);
//            imagesSnapshots.add(snapshot);
//        }
//
//        return imagesSnapshots;
//    }
//
//    /**
//     * 标签快照
//     *
//     * @param goods
//     * @return
//     */
//    public List<ChildOrder.GoodsTagsSnapshot> buildTags(Goods goods) {
//        List<ChildOrder.GoodsTagsSnapshot> tagsSnapshots = Lists.newArrayList();
//        List<GoodsTag> tags = goods.getGoodsTags();
//        if (CollectionUtils.isEmpty(tags)) {
//            return tagsSnapshots;
//        }
//        for (GoodsTag goodsTag : tags) {
//            ChildOrder.GoodsTagsSnapshot snapshot = new ChildOrder.GoodsTagsSnapshot();
//            Tag tag = goodsTag.getTag();
//            BeanUtils.copyProperties(tag, snapshot);
//            tagsSnapshots.add(snapshot);
//        }
//
//        return tagsSnapshots;
//    }
//
//
//    public ChildOrder.SupplierSnapshot buildSupplierSnapshots(Goods goods) {
//        Supplier supplierById = supplierService.getSupplierById(goods.getSupplierId());
//        if (supplierById == null) {
//            return null;
//        }
//        ChildOrder.SupplierSnapshot supplierSnapshot = new ChildOrder.SupplierSnapshot();
//        BeanUtils.copyProperties(supplierById, supplierSnapshot);
//        return supplierSnapshot;
//    }


}
