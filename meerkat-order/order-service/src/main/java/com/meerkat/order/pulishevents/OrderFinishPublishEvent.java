package com.meerkat.order.pulishevents;


import com.meerkat.order.enums.MQConstants;
import com.meerkat.order.model.Order;

/**
 * 订单完成事件
 */
public class OrderFinishPublishEvent extends AbstractOrderEvent {
    private static final long serialVersionUID = -4015874408234674433L;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     * @param order
     */
    public OrderFinishPublishEvent(Object source, Order order) {
        super(source, order);
    }

    @Override
    public String routingKey() {
        return MQConstants.RoutingKey.MIDDLE_PLATFORM_ORDER_FINISH;
    }
}
