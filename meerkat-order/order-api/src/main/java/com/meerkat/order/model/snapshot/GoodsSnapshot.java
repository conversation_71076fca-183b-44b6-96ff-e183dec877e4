package com.meerkat.order.model.snapshot;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description: 商品快照
 * @author: pantaoling
 * @date: 2021/10/12
 */
public class GoodsSnapshot implements Serializable {

    private static final long serialVersionUID = 4841885889989399518L;
    private Long id;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 机构id
     */
    private Long organizationId;

    /**
     * 图片logo
     */
    private String icon;

    /**
     * 商品名称拼音
     */
    private String pinyin;

    /**
     * 性别: 0-女 1-男 2-通用
     */
    private Integer gender;

    /**
     * 婚姻状态: 1- 已婚 2-通用
     */
    private Integer marriageStatus;

    /**
     * 折扣
     */
    private String discount;

    /**
     * 初始价格
     */
    private Long initPrice;

    /**
     * 展示价格
     */
    private Long displayPrice;

    /**
     * 售价
     */
    private Long price;

    /**
     * 供货价
     */
    private Long supplyPrice;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 简介
     */
    private String briefIntro;

    /**
     * 详情介绍
     */
    private String description;

    /**
     * 排序
     */
    private Integer sequence;

    /**
     * 商品类型:1-套餐 2-加项包 3-HPV 4-上门核酸
     */
    private Integer type;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 上架配置:1-上架 0-下架
     */
    private Integer saleable;

    /**
     * 状态: 1-可用 2-异常
     */
    private Integer status;


    /**
     * 城市ID
     */
    private Long addressId;

    /**
     * 城市
     */
    private String city;


    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 加项包快照
     */
    private List<AddtionalPackageSnapshot> addtionalPackageList;

    private String hisGoodsId;

    private List<GoodsImageSnapshot> images;

    /**
     * 商品项目快照
     */
    private List<GoodsItemsSnapshot> itemsSnapshots;

    /**
     * 套餐加项快照
     */
    private List<GoodsItemsSnapshot> addItemsSnapshots;

    /**
     * 套餐删除快照
     */
    private List<GoodsItemsSnapshot> removeItemsSnapshots;

    private List<GoodsTagsSnapshot> tagsSnapshots;

    private SupplierSnapshot supplierSnapshot;

    private Integer purchaseQuantity;

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public SupplierSnapshot getSupplierSnapshot() {
        return supplierSnapshot;
    }

    public void setSupplierSnapshot(SupplierSnapshot supplierSnapshot) {
        this.supplierSnapshot = supplierSnapshot;
    }

    public Integer getType() {
        return type;
    }

    public Integer getSaleable() {
        return saleable;
    }

    public Integer getStatus() {
        return status;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getMarriageStatus() {
        return marriageStatus;
    }

    public void setMarriageStatus(Integer marriageStatus) {
        this.marriageStatus = marriageStatus;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setSaleable(Integer saleable) {
        this.saleable = saleable;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDiscount() {
        return discount;
    }

    public void setDiscount(String discount) {
        this.discount = discount;
    }

    public Long getInitPrice() {
        return initPrice;
    }

    public void setInitPrice(Long initPrice) {
        this.initPrice = initPrice;
    }

    public Long getDisplayPrice() {
        return displayPrice;
    }

    public void setDisplayPrice(Long displayPrice) {
        this.displayPrice = displayPrice;
    }

    public Long getPrice() {
        return price;
    }

    public void setPrice(Long price) {
        this.price = price;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getBriefIntro() {
        return briefIntro;
    }

    public void setBriefIntro(String briefIntro) {
        this.briefIntro = briefIntro;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public List<GoodsImageSnapshot> getImages() {
        return images;
    }

    public void setImages(List<GoodsImageSnapshot> images) {
        this.images = images;
    }

    public List<GoodsItemsSnapshot> getItemsSnapshots() {
        return itemsSnapshots;
    }

    public void setItemsSnapshots(List<GoodsItemsSnapshot> itemsSnapshots) {
        this.itemsSnapshots = itemsSnapshots;
    }

    public List<GoodsTagsSnapshot> getTagsSnapshots() {
        return tagsSnapshots;
    }

    public void setTagsSnapshots(List<GoodsTagsSnapshot> tagsSnapshots) {
        this.tagsSnapshots = tagsSnapshots;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Long getSupplyPrice() {
        return supplyPrice;
    }

    public void setSupplyPrice(Long supplyPrice) {
        this.supplyPrice = supplyPrice;
    }

    public List<GoodsItemsSnapshot> getAddItemsSnapshots() {
        return addItemsSnapshots;
    }

    public void setAddItemsSnapshots(List<GoodsItemsSnapshot> addItemsSnapshots) {
        this.addItemsSnapshots = addItemsSnapshots;
    }

    public List<GoodsItemsSnapshot> getRemoveItemsSnapshots() {
        return removeItemsSnapshots;
    }

    public void setRemoveItemsSnapshots(List<GoodsItemsSnapshot> removeItemsSnapshots) {
        this.removeItemsSnapshots = removeItemsSnapshots;
    }

    public List<AddtionalPackageSnapshot> getAddtionalPackageList() {
        return addtionalPackageList;
    }

    public void setAddtionalPackageList(List<AddtionalPackageSnapshot> addtionalPackageList) {
        this.addtionalPackageList = addtionalPackageList;
    }

    public String getHisGoodsId() {
        return hisGoodsId;
    }

    public void setHisGoodsId(String hisGoodsId) {
        this.hisGoodsId = hisGoodsId;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public static class GoodsImageSnapshot {

        /**
         * 商品id
         */
        private Long goodsId;

        /**
         * 图片类型
         */
        private Integer type;


        /**
         * 图片url
         */
        private String imageUrl;

        /**
         * 顺序
         */
        private Integer sequence;

        public Long getGoodsId() {
            return goodsId;
        }

        public void setGoodsId(Long goodsId) {
            this.goodsId = goodsId;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public Integer getSequence() {
            return sequence;
        }

        public void setSequence(Integer sequence) {
            this.sequence = sequence;
        }
    }

    public static class GoodsItemsSnapshot {
        private Long id;

        /**
         * 项目名称
         */
        private String name;

        /**
         * 机构id
         */
        private Long organizationId;

        /**
         * 项目名称拼音
         */
        private String pinyin;

        /**
         * 性别: 0-女 1-男 2-通用
         */
        private Integer gender;

        /**
         * 婚姻状态: 1- 已婚 2-通用
         */
        private Integer marriageStatus;

        /**
         * 售价
         */
        private Long price;

        /**
         * 是否可打折: 0-不可打折 1-可打折
         */
        private Integer discountable;

        /**
         * 项目类型:1-通用项目 2-体检中心项目
         */
        private Integer type;

        /**
         * 内网项目编码
         */
        private String innerItemCode;

        /**
         * 创建时间
         */
        private Date gmtCreate;

        /**
         * 修改时间
         */
        private Date gmtModified;

        /**
         * 项目在商品中的关系 1：商品内项目 2：套餐内删除项  3：新增项
         */
        private Integer typeInGoods;


        public Integer getGender() {
            return gender;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Long getOrganizationId() {
            return organizationId;
        }

        public void setOrganizationId(Long organizationId) {
            this.organizationId = organizationId;
        }

        public String getPinyin() {
            return pinyin;
        }

        public void setPinyin(String pinyin) {
            this.pinyin = pinyin;
        }

        public Integer getMarriageStatus() {
            return marriageStatus;
        }

        public void setMarriageStatus(Integer marriageStatus) {
            this.marriageStatus = marriageStatus;
        }

        public Long getPrice() {
            return price;
        }

        public void setPrice(Long price) {
            this.price = price;
        }

        public void setGender(Integer gender) {
            this.gender = gender;
        }

        public Integer getDiscountable() {
            return discountable;
        }

        public void setDiscountable(Integer discountable) {
            this.discountable = discountable;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getInnerItemCode() {
            return innerItemCode;
        }

        public void setInnerItemCode(String innerItemCode) {
            this.innerItemCode = innerItemCode;
        }

        public Date getGmtCreate() {
            return gmtCreate;
        }

        public void setGmtCreate(Date gmtCreate) {
            this.gmtCreate = gmtCreate;
        }

        public Date getGmtModified() {
            return gmtModified;
        }

        public void setGmtModified(Date gmtModified) {
            this.gmtModified = gmtModified;
        }

        public Integer getTypeInGoods() {
            return typeInGoods;
        }

        public void setTypeInGoods(Integer typeInGoods) {
            this.typeInGoods = typeInGoods;
        }

    }

    public static class GoodsTagsSnapshot {
        private Long id;

        /**
         * 标签分类名称
         */
        private String name;

        /**
         * 项目名称拼音
         */
        private String pinyin;

        /**
         * 标签分类id
         */
        private Integer categoryId;

        /**
         * 排序
         */
        private Integer sequence;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPinyin() {
            return pinyin;
        }

        public void setPinyin(String pinyin) {
            this.pinyin = pinyin;
        }

        public Integer getCategoryId() {
            return categoryId;
        }

        public void setCategoryId(Integer categoryId) {
            this.categoryId = categoryId;
        }

        public Integer getSequence() {
            return sequence;
        }

        public void setSequence(Integer sequence) {
            this.sequence = sequence;
        }
    }


    public static class SupplierSnapshot {
        /**
         * 供应商名称
         */
        private String name;

        /**
         * 供应商编码
         */
        private String code;

        /**
         * 供应商名称拼音
         */
        private String pinyin;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getPinyin() {
            return pinyin;
        }

        public void setPinyin(String pinyin) {
            this.pinyin = pinyin;
        }
    }

    /**
     *
     */
    public static class AddtionalPackageSnapshot {
        private Long id;

        /**
         * 名称
         */
        private String name;

        /**
         * 名称拼音
         */
        private String pinyin;

        /**
         * 加项包类型: 1-通用 2-风险
         */
        private Integer type;

        /**
         * 机构id
         */
        private Long organizationId;

        /**
         * 性别: 0-女 1-男 2-通用
         */
        private Integer gender;

        /**
         * 婚姻状态: 1- 已婚 2-通用
         */
        private Integer marriageStatus;

        /**
         * 加项包图标
         */
        private String icon;

        /**
         * 描述
         */
        private String description;

        /**
         * 初始价格
         */
        private Long initPrice;

        /**
         * 展示价格
         */
        private Long displayPrice;

        /**
         * 售价
         */
        private Long price;

        /**
         * 是否显示: 1-显示， 0-不显示
         */
        private Integer isShow;

        /**
         * 状态: 1-可用 2-异常
         */
        private Integer status;

        /**
         * 排序sequence
         */
        private Integer sequence;

        /**
         * 加项包项目
         */
        private List<PackageItemSnapshot> packageItemSnapshotList;

        /**
         * 减项集合
         */
        private List<PackageItemSnapshot> removePackageItemList;

        /**
         * 加项集合
         */
        private List<PackageItemSnapshot> addPackageItemList;

        /**
         * 创建时间
         */
        private Date gmtCreated;

        /**
         * 修改时间
         */
        private Date gmtModified;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPinyin() {
            return pinyin;
        }

        public void setPinyin(String pinyin) {
            this.pinyin = pinyin;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public Long getOrganizationId() {
            return organizationId;
        }

        public void setOrganizationId(Long organizationId) {
            this.organizationId = organizationId;
        }

        public Integer getGender() {
            return gender;
        }

        public void setGender(Integer gender) {
            this.gender = gender;
        }

        public Integer getMarriageStatus() {
            return marriageStatus;
        }

        public void setMarriageStatus(Integer marriageStatus) {
            this.marriageStatus = marriageStatus;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public Long getInitPrice() {
            return initPrice;
        }

        public void setInitPrice(Long initPrice) {
            this.initPrice = initPrice;
        }

        public Long getDisplayPrice() {
            return displayPrice;
        }

        public void setDisplayPrice(Long displayPrice) {
            this.displayPrice = displayPrice;
        }

        public Long getPrice() {
            return price;
        }

        public void setPrice(Long price) {
            this.price = price;
        }

        public Integer getIsShow() {
            return isShow;
        }

        public void setIsShow(Integer isShow) {
            this.isShow = isShow;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public Integer getSequence() {
            return sequence;
        }

        public void setSequence(Integer sequence) {
            this.sequence = sequence;
        }

        public List<PackageItemSnapshot> getPackageItemSnapshotList() {
            return packageItemSnapshotList;
        }

        public void setPackageItemSnapshotList(List<PackageItemSnapshot> packageItemSnapshotList) {
            this.packageItemSnapshotList = packageItemSnapshotList;
        }

        public List<PackageItemSnapshot> getRemovePackageItemList() {
            return removePackageItemList;
        }

        public void setRemovePackageItemList(List<PackageItemSnapshot> removePackageItemList) {
            this.removePackageItemList = removePackageItemList;
        }

        public List<PackageItemSnapshot> getAddPackageItemList() {
            return addPackageItemList;
        }

        public void setAddPackageItemList(List<PackageItemSnapshot> addPackageItemList) {
            this.addPackageItemList = addPackageItemList;
        }

        public Date getGmtCreated() {
            return gmtCreated;
        }

        public void setGmtCreated(Date gmtCreated) {
            this.gmtCreated = gmtCreated;
        }

        public Date getGmtModified() {
            return gmtModified;
        }

        public void setGmtModified(Date gmtModified) {
            this.gmtModified = gmtModified;
        }
    }

    /**
     * 加项包项目快照
     */
    public static class PackageItemSnapshot {

        private Long id;

        /**
         * 加项包id
         */
        private Long itemPkgId;

        /**
         * 项目id
         */
        private Long itemId;

        /**
         * 是否必选: 1-必须 0-非必选
         */
        private Integer isNecessary;

        /**
         * 售价
         */
        private Long price;

        /**
         * 项目名称
         */
        private String name;

        /**
         * 机构id
         */
        private Long organizationId;

        /**
         * 项目名称拼音
         */
        private String pinyin;

        /**
         * 性别: 0-女 1-男 2-通用
         */
        private Integer gender;

        /**
         * 婚姻状态: 1- 已婚 2-通用
         */
        private Integer marriageStatus;


        /**
         * 项目类型:1-通用项目 2-体检中心项目
         */
        private Integer type;

        /**
         * 内网项目编码
         */
        private String innerItemCode;


        /**
         * 项目在商品中的关系 1：商品内项目 2：套餐内删除项  3：新增项
         */
        private Integer typeInGoods;

        /**
         * 创建时间
         */
        private Date gmtCreated;

        /**
         * 修改时间
         */
        private Date gmtModified;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Long getItemPkgId() {
            return itemPkgId;
        }

        public void setItemPkgId(Long itemPkgId) {
            this.itemPkgId = itemPkgId;
        }

        public Long getItemId() {
            return itemId;
        }

        public void setItemId(Long itemId) {
            this.itemId = itemId;
        }

        public Integer getIsNecessary() {
            return isNecessary;
        }

        public void setIsNecessary(Integer isNecessary) {
            this.isNecessary = isNecessary;
        }

        public Long getPrice() {
            return price;
        }

        public void setPrice(Long price) {
            this.price = price;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Long getOrganizationId() {
            return organizationId;
        }

        public void setOrganizationId(Long organizationId) {
            this.organizationId = organizationId;
        }

        public String getPinyin() {
            return pinyin;
        }

        public void setPinyin(String pinyin) {
            this.pinyin = pinyin;
        }

        public Integer getGender() {
            return gender;
        }

        public void setGender(Integer gender) {
            this.gender = gender;
        }

        public Integer getMarriageStatus() {
            return marriageStatus;
        }

        public void setMarriageStatus(Integer marriageStatus) {
            this.marriageStatus = marriageStatus;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getInnerItemCode() {
            return innerItemCode;
        }

        public void setInnerItemCode(String innerItemCode) {
            this.innerItemCode = innerItemCode;
        }

        public Integer getTypeInGoods() {
            return typeInGoods;
        }

        public void setTypeInGoods(Integer typeInGoods) {
            this.typeInGoods = typeInGoods;
        }

        public Date getGmtCreated() {
            return gmtCreated;
        }

        public void setGmtCreated(Date gmtCreated) {
            this.gmtCreated = gmtCreated;
        }

        public Date getGmtModified() {
            return gmtModified;
        }

        public void setGmtModified(Date gmtModified) {
            this.gmtModified = gmtModified;
        }
    }

    public Integer getPurchaseQuantity() {
        //todo:默认购买数量 本来应该需要初始化的，疫苗商品也需要有数量
        return purchaseQuantity == null ? 1 : purchaseQuantity;
    }

    public void setPurchaseQuantity(Integer purchaseQuantity) {
        this.purchaseQuantity = purchaseQuantity;
    }
}
