package com.meerkat.order.event;

import com.meerkat.order.enums.OrderEventEnum;

/**
 * @description: 订单核销事件
 * @author: pantaoling
 * @date: 2021/11/6
 */
public class OrderCheckEvent implements OrderStateEvent{

    /**
     * 系统
     */
    private Integer system;

    /**
     * 订单编号
     */
    private String orderNum;

    /**
     * 操作人
     */
    private Long operator;

    @Override
    public Long getOperator() {
        return operator;
    }

    public void setOperator(Long operator) {
        this.operator = operator;
    }

    @Override
    public Integer getEventType() {
        return OrderEventEnum.CHECK.getCode();
    }

    @Override
    public Integer getSystem() {
        return this.system;
    }

    @Override
    public String getOrderNum() {
        return this.orderNum;
    }

    public void setSystem(Integer system) {
        this.system = system;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }
}
