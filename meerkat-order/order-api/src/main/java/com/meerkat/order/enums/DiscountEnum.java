package com.meerkat.order.enums;

/**
 * @description:
 * @author: pantaoling
 * @date: 2022/8/9
 */
public enum DiscountEnum {


    KNOCK("knock", 2, "立减",0),
    EXAM_CARD("exam_card", 1, "体检卡抵扣",1),
    COUPON("coupon", 0, "优惠券优惠",0);

    private final String name;
    private final Integer code;
    private final String desc;
    /**
     * 抵扣方式 0=优惠抵扣 1=支付抵扣
     */
    private final Integer discountType;

    DiscountEnum(String name, Integer code, String desc, Integer discountType) {
        this.name = name;
        this.code = code;
        this.desc = desc;
        this.discountType = discountType;
    }

    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getDiscountType() {
        return discountType;
    }
}
