package com.meerkat.order.event;

import com.meerkat.order.enums.OrderEventEnum;
import com.meerkat.order.param.ExamDateParam;

/**
 * @description: 订单改期事件
 * @author: pantaoling
 * @date: 2021/11/4
 */
public class OrderChangeDateEvent implements OrderStateEvent {

    /**
     * 订单编号
     */
    private String orderNum;

    /**
     * 系统
     */
    private Integer system;

    /**
     * 服务事件参数
     */
    private ExamDateParam examDateParam;

    /**
     * 操作人
     */
    private Long operator;

    /**
     * 结果
     */
    private boolean result;

    public boolean isResult() {
        return result;
    }

    public void setResult(boolean result) {
        this.result = result;
    }

    @Override
    public Long getOperator() {
        return this.operator;
    }

    public void setOperator(Long operator) {
        this.operator = operator;
    }

    public ExamDateParam getExamDateParam() {
        return examDateParam;
    }

    public void setExamDateParam(ExamDateParam examDateParam) {
        this.examDateParam = examDateParam;
    }

    @Override
    public Integer getEventType() {
        return OrderEventEnum.CHANGE_DATE.getCode();
    }

    @Override
    public Integer getSystem() {
        return this.system;
    }

    @Override
    public String getOrderNum() {
        return this.orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public void setSystem(Integer system) {
        this.system = system;
    }
}
