package com.meerkat.order.param;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 体检时间参数
 * @author: pantaoling
 * @date: 2021/11/3
 */
public class ExamDateParam implements Serializable {
    private static final long serialVersionUID = -1215875630573326851L;
    /**
     * 容量id
     */
    @Deprecated
    private Long capacityId;

    /**
     * 体检日期
     */
    private Date examDate;

    /**
     * 时间段
     */
    private String period;

    public Long getCapacityId() {
        return capacityId;
    }

    public void setCapacityId(Long capacityId) {
        this.capacityId = capacityId;
    }

    public Date getExamDate() {
        return examDate;
    }

    public void setExamDate(Date examDate) {
        this.examDate = examDate;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }
}
