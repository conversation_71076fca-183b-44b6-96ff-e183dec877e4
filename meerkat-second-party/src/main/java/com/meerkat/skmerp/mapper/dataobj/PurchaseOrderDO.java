package com.meerkat.skmerp.mapper.dataobj;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2022/6/7 11:29
 */
public class PurchaseOrderDO {

    /**
     * 申请编号
     */
    private String applyBillCode;

    /**
     * 事由
     */
    private String reimburseName;

    /**
     * 申请人
     */
    private String staffname;

    /**
     * 申请时间
     */
    private String applyDate;

    /**
     * 供应商编码
     */
    private String tradingPartnerBizCode;

    /**
     * 供应商名称
     */
    private String tradingPartnerName;

    /**
     * 入库单号
     */
    private String rkdh;

    /**
     * 是否预付款
     */
    private String sfyfk;

    /**
     * 付款方式
     */
    private String paymentName;

    /**
     * 账期
     */
    private Integer xindts;

    /**
     * 授信额度
     */
    private BigDecimal xinded;

    /**
     * 是否确认下游客户
     */
    private String sfqrxykh;

    /**
     * 下游客户名称
     */
    private String xykhmc;

    /**
     * 下游客户回款金额
     */
    private BigDecimal xykhhkje;

    /**
     * 本次付款金额
     */
    private BigDecimal bcfkje;

    /**
     * 付款条件
     */
    private String fktj;

    /**
     * 应付余额
     */
    private BigDecimal yfye;

    /**
     * 到票金额
     */
    private BigDecimal dpje;

    /**
     * 本次付款超授信额度
     */
    private BigDecimal bcfkcsxed;

    /**
     * 本次付款超期天数
     */
    private String bcfkcqts;

    /**
     * 收款账户名
     */
    private String pyeeAcctName;

    /**
     * 收款账户
     */
    private String pyeeAcctNumber;

    public String getApplyBillCode() {
        return applyBillCode;
    }

    public void setApplyBillCode(String applyBillCode) {
        this.applyBillCode = applyBillCode;
    }

    public String getReimburseName() {
        return reimburseName;
    }

    public void setReimburseName(String reimburseName) {
        this.reimburseName = reimburseName;
    }

    public String getStaffname() {
        return staffname;
    }

    public void setStaffname(String staffname) {
        this.staffname = staffname;
    }

    public String getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(String applyDate) {
        this.applyDate = applyDate;
    }

    public String getTradingPartnerBizCode() {
        return tradingPartnerBizCode;
    }

    public void setTradingPartnerBizCode(String tradingPartnerBizCode) {
        this.tradingPartnerBizCode = tradingPartnerBizCode;
    }

    public String getTradingPartnerName() {
        return tradingPartnerName;
    }

    public void setTradingPartnerName(String tradingPartnerName) {
        this.tradingPartnerName = tradingPartnerName;
    }

    public String getRkdh() {
        return rkdh;
    }

    public void setRkdh(String rkdh) {
        this.rkdh = rkdh;
    }

    public String getSfyfk() {
        return sfyfk;
    }

    public void setSfyfk(String sfyfk) {
        this.sfyfk = sfyfk;
    }

    public String getPaymentName() {
        return paymentName;
    }

    public void setPaymentName(String paymentName) {
        this.paymentName = paymentName;
    }

    public Integer getXindts() {
        return xindts;
    }

    public void setXindts(Integer xindts) {
        this.xindts = xindts;
    }

    public BigDecimal getXinded() {
        return xinded;
    }

    public void setXinded(BigDecimal xinded) {
        this.xinded = xinded;
    }

    public String getSfqrxykh() {
        return sfqrxykh;
    }

    public void setSfqrxykh(String sfqrxykh) {
        this.sfqrxykh = sfqrxykh;
    }

    public String getXykhmc() {
        return xykhmc;
    }

    public void setXykhmc(String xykhmc) {
        this.xykhmc = xykhmc;
    }

    public BigDecimal getXykhhkje() {
        return xykhhkje;
    }

    public void setXykhhkje(BigDecimal xykhhkje) {
        this.xykhhkje = xykhhkje;
    }

    public BigDecimal getBcfkje() {
        return bcfkje;
    }

    public void setBcfkje(BigDecimal bcfkje) {
        this.bcfkje = bcfkje;
    }

    public String getFktj() {
        return fktj;
    }

    public void setFktj(String fktj) {
        this.fktj = fktj;
    }

    public BigDecimal getYfye() {
        return yfye;
    }

    public void setYfye(BigDecimal yfye) {
        this.yfye = yfye;
    }

    public BigDecimal getDpje() {
        return dpje;
    }

    public void setDpje(BigDecimal dpje) {
        this.dpje = dpje;
    }

    public BigDecimal getBcfkcsxed() {
        return bcfkcsxed;
    }

    public void setBcfkcsxed(BigDecimal bcfkcsxed) {
        this.bcfkcsxed = bcfkcsxed;
    }

    public String getBcfkcqts() {
        return bcfkcqts;
    }

    public void setBcfkcqts(String bcfkcqts) {
        this.bcfkcqts = bcfkcqts;
    }

    public String getPyeeAcctName() {
        return pyeeAcctName;
    }

    public void setPyeeAcctName(String pyeeAcctName) {
        this.pyeeAcctName = pyeeAcctName;
    }

    public String getPyeeAcctNumber() {
        return pyeeAcctNumber;
    }

    public void setPyeeAcctNumber(String pyeeAcctNumber) {
        this.pyeeAcctNumber = pyeeAcctNumber;
    }
}
