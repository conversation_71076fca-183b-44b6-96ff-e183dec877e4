package com.meerkat.skmerp.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/1 16:02
 */
public class SkmerpPurchaseRecord implements Serializable {

    private static final long serialVersionUID = -8893135690739118027L;

    /**
     * 单据编码
     */
    private String formCode;

    /**
     * 记录唯一标识
     */
    private String recordSn;

    /**
     * 单据日期
     */
    private LocalDateTime formDate;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 部门
     */
    private String department;

    /**
     * 商品编码
     */
    private String goodsCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 单位
     */
    private String unit;

    /**
     * 采购数量
     */
    private Integer purchaseQuantity;

    /**
     * 无税金额
     */
    private BigDecimal amountNoTax;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 含税金额
     */
    private BigDecimal amountIncludingTax;

    /**
     * 含税单价
     */
    private BigDecimal priceIncludingTax;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    public String getFormCode() {
        return formCode;
    }

    public SkmerpPurchaseRecord setFormCode(String formCode) {
        this.formCode = formCode;
        return this;
    }

    public String getRecordSn() {
        return recordSn;
    }

    public SkmerpPurchaseRecord setRecordSn(String recordSn) {
        this.recordSn = recordSn;
        return this;
    }

    public LocalDateTime getFormDate() {
        return formDate;
    }

    public SkmerpPurchaseRecord setFormDate(LocalDateTime formDate) {
        this.formDate = formDate;
        return this;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public SkmerpPurchaseRecord setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
        return this;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public SkmerpPurchaseRecord setSupplierName(String supplierName) {
        this.supplierName = supplierName;
        return this;
    }

    public String getDepartment() {
        return department;
    }

    public SkmerpPurchaseRecord setDepartment(String department) {
        this.department = department;
        return this;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public SkmerpPurchaseRecord setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
        return this;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public SkmerpPurchaseRecord setGoodsName(String goodsName) {
        this.goodsName = goodsName;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public SkmerpPurchaseRecord setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public SkmerpPurchaseRecord setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
        return this;
    }

    public String getUnit() {
        return unit;
    }

    public SkmerpPurchaseRecord setUnit(String unit) {
        this.unit = unit;
        return this;
    }

    public Integer getPurchaseQuantity() {
        return purchaseQuantity;
    }

    public SkmerpPurchaseRecord setPurchaseQuantity(Integer purchaseQuantity) {
        this.purchaseQuantity = purchaseQuantity;
        return this;
    }

    public BigDecimal getAmountNoTax() {
        return amountNoTax;
    }

    public SkmerpPurchaseRecord setAmountNoTax(BigDecimal amountNoTax) {
        this.amountNoTax = amountNoTax;
        return this;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public SkmerpPurchaseRecord setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
        return this;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public SkmerpPurchaseRecord setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
        return this;
    }

    public BigDecimal getAmountIncludingTax() {
        return amountIncludingTax;
    }

    public SkmerpPurchaseRecord setAmountIncludingTax(BigDecimal amountIncludingTax) {
        this.amountIncludingTax = amountIncludingTax;
        return this;
    }

    public BigDecimal getPriceIncludingTax() {
        return priceIncludingTax;
    }

    public SkmerpPurchaseRecord setPriceIncludingTax(BigDecimal priceIncludingTax) {
        this.priceIncludingTax = priceIncludingTax;
        return this;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public SkmerpPurchaseRecord setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
        return this;
    }
}
