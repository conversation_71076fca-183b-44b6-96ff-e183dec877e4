package com.meerkat.shengchen.dto.param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/30 22:09
 */
public class ShengchenPurchaseRecordQueryDTO {

    /**
     * 商品id
     */
    private List<String> goodsCodeList;

    /**
     * 开始单据日期
     */
    private LocalDateTime startFormTime;

    /**
     * 结束单据日期
     */
    private LocalDateTime endFormTime;

    public ShengchenPurchaseRecordQueryDTO() {}

    public ShengchenPurchaseRecordQueryDTO(LocalDateTime startFormTime, LocalDateTime endFormTime) {
        this.startFormTime = startFormTime;
        this.endFormTime = endFormTime;
    }

    public ShengchenPurchaseRecordQueryDTO(List<String> goodsCodeList) {
        this.goodsCodeList = goodsCodeList;
    }

    public List<String> getGoodsCodeList() {
        return goodsCodeList;
    }

    public ShengchenPurchaseRecordQueryDTO setGoodsCodeList(List<String> goodsCodeList) {
        this.goodsCodeList = goodsCodeList;
        return this;
    }

    public LocalDateTime getStartFormTime() {
        return startFormTime;
    }

    public ShengchenPurchaseRecordQueryDTO setStartFormTime(LocalDateTime startFormTime) {
        this.startFormTime = startFormTime;
        return this;
    }

    public LocalDateTime getEndFormTime() {
        return endFormTime;
    }

    public ShengchenPurchaseRecordQueryDTO setEndFormTime(LocalDateTime endFormTime) {
        this.endFormTime = endFormTime;
        return this;
    }
}
