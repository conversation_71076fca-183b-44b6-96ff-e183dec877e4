package com.meerkat.shengchen.mapper.dataobj;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/25 17:10
 */
public class ShengchenSalesRecordDO {

    /**
     * id
     */
    private String id;

    /**
     * 单据日期
     */
    private Date djrq;

    /**
     * 单据编码
     */
    private String djbm;

    /**
     * 业务类型
     */
    private String ywlx;

    /**
     * 客户编码
     */
    private String khbm;

    /**
     * 客户名称
     */
    private String khmc;

    /**
     * 开单员
     */
    private String czy;

    /**
     * 通用名
     */
    private String tym;

    /**
     * 规格
     */
    private String gg;

    /**
     * 生产厂家
     */
    private String sccj;

    /**
     * 产地
     */
    private String cd;

    /**
     * 大包装
     */
    private Integer dbz;

    /**
     * 件数
     */
    private BigDecimal js;

    /**
     * 部门名称
     */
    private String bmmc;

    /**
     * 单位
     */
    private String dw;

    /**
     * 商品税率
     */
    private BigDecimal spsl;

    /**
     * 销售数量
     */
    private BigDecimal xssl;

    /**
     * 销售单价
     */
    private BigDecimal xsdj;

    /**
     * 含税金额
     */
    private BigDecimal hsje;

    /**
     * 无税金额
     */
    private BigDecimal wsje;

    /**
     * 税额
     */
    private BigDecimal se;

    /**
     * 客商部门编码
     */
    private String ksbmbm;

    /**
     * 制单人
     */
    private String zdr;

    /**
     * 商品编码
     */
    private String spbm;

    /**
     * 批号
     */
    private String ph;

    /**
     * 生产日期
     */
    private Date scrq;

    /**
     * 有效期至
     */
    private Date yxqz;

    /**
     * 有效期
     */
    private String yxqs;

    /**
     * 核算单元
     */
    private String hsdy;

    /**
     * 区划编码
     */
    private String qhbm;

    /**
     * 毛利
     */
    private BigDecimal ml;

    /**
     * 毛利率
     */
    private BigDecimal mll;

    /**
     * 最后供应商编码
     */
    private String zhgysbm;

    /**
     * 商品最后供应商名称
     */
    private String zhgysmc;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getDjrq() {
        return djrq;
    }

    public void setDjrq(Date djrq) {
        this.djrq = djrq;
    }

    public String getDjbm() {
        return djbm;
    }

    public void setDjbm(String djbm) {
        this.djbm = djbm;
    }

    public String getYwlx() {
        return ywlx;
    }

    public void setYwlx(String ywlx) {
        this.ywlx = ywlx;
    }

    public String getKhbm() {
        return khbm;
    }

    public void setKhbm(String khbm) {
        this.khbm = khbm;
    }

    public String getKhmc() {
        return khmc;
    }

    public void setKhmc(String khmc) {
        this.khmc = khmc;
    }

    public String getCzy() {
        return czy;
    }

    public void setCzy(String czy) {
        this.czy = czy;
    }

    public String getTym() {
        return tym;
    }

    public void setTym(String tym) {
        this.tym = tym;
    }

    public String getGg() {
        return gg;
    }

    public void setGg(String gg) {
        this.gg = gg;
    }

    public String getSccj() {
        return sccj;
    }

    public void setSccj(String sccj) {
        this.sccj = sccj;
    }

    public String getCd() {
        return cd;
    }

    public void setCd(String cd) {
        this.cd = cd;
    }

    public Integer getDbz() {
        return dbz;
    }

    public void setDbz(Integer dbz) {
        this.dbz = dbz;
    }

    public BigDecimal getJs() {
        return js;
    }

    public void setJs(BigDecimal js) {
        this.js = js;
    }

    public String getBmmc() {
        return bmmc;
    }

    public void setBmmc(String bmmc) {
        this.bmmc = bmmc;
    }

    public String getDw() {
        return dw;
    }

    public void setDw(String dw) {
        this.dw = dw;
    }

    public BigDecimal getSpsl() {
        return spsl;
    }

    public void setSpsl(BigDecimal spsl) {
        this.spsl = spsl;
    }

    public BigDecimal getXssl() {
        return xssl;
    }

    public void setXssl(BigDecimal xssl) {
        this.xssl = xssl;
    }

    public BigDecimal getXsdj() {
        return xsdj;
    }

    public void setXsdj(BigDecimal xsdj) {
        this.xsdj = xsdj;
    }

    public BigDecimal getHsje() {
        return hsje;
    }

    public void setHsje(BigDecimal hsje) {
        this.hsje = hsje;
    }

    public BigDecimal getWsje() {
        return wsje;
    }

    public void setWsje(BigDecimal wsje) {
        this.wsje = wsje;
    }

    public BigDecimal getSe() {
        return se;
    }

    public void setSe(BigDecimal se) {
        this.se = se;
    }

    public String getKsbmbm() {
        return ksbmbm;
    }

    public void setKsbmbm(String ksbmbm) {
        this.ksbmbm = ksbmbm;
    }

    public String getZdr() {
        return zdr;
    }

    public void setZdr(String zdr) {
        this.zdr = zdr;
    }

    public String getSpbm() {
        return spbm;
    }

    public void setSpbm(String spbm) {
        this.spbm = spbm;
    }

    public String getPh() {
        return ph;
    }

    public void setPh(String ph) {
        this.ph = ph;
    }

    public Date getScrq() {
        return scrq;
    }

    public void setScrq(Date scrq) {
        this.scrq = scrq;
    }

    public Date getYxqz() {
        return yxqz;
    }

    public void setYxqz(Date yxqz) {
        this.yxqz = yxqz;
    }

    public String getYxqs() {
        return yxqs;
    }

    public void setYxqs(String yxqs) {
        this.yxqs = yxqs;
    }

    public String getHsdy() {
        return hsdy;
    }

    public void setHsdy(String hsdy) {
        this.hsdy = hsdy;
    }

    public String getQhbm() {
        return qhbm;
    }

    public void setQhbm(String qhbm) {
        this.qhbm = qhbm;
    }

    public BigDecimal getMl() {
        return ml;
    }

    public void setMl(BigDecimal ml) {
        this.ml = ml;
    }

    public BigDecimal getMll() {
        return mll;
    }

    public void setMll(BigDecimal mll) {
        this.mll = mll;
    }

    public String getZhgysbm() {
        return zhgysbm;
    }

    public void setZhgysbm(String zhgysbm) {
        this.zhgysbm = zhgysbm;
    }

    public String getZhgysmc() {
        return zhgysmc;
    }

    public void setZhgysmc(String zhgysmc) {
        this.zhgysmc = zhgysmc;
    }
}
