<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.shengchen.mapper.ShengchenJcZzzlMapper">
    <resultMap id="BaseResultMap" type="com.meerkat.shengchen.mapper.dataobj.ShengchenJcZzzlDO">
        <result column="id" property="id" />
        <result column="ksbm" property="ksbm" />
        <result column="zllx" property="zllx" />
        <result column="zzbh" property="zzbh" />
        <result column="zznr" property="zznr" />
        <result column="yxq" property="yxq" />
        <result column="xsbz" property="xsbz" />
        <result column="fjbz" property="fjbz" />
        <result column="zfbz" property="zfbz" />
        <result column="bzsm" property="bzsm" />
        <result column="cjr" property="cjr" />
        <result column="cjrq" property="cjrq" />
        <result column="xgr" property="xgr" />
        <result column="xgrq" property="xgrq" />
        <result column="fzrq" property="fzrq" />
    </resultMap>

    <sql id="insert_column">
        id,
        ksbm,
        zllx,
        zzbh,
        yxq,
        fjbz,
        bzsm,
        cjr,
        cjrq,
        xgr,
        xgrq
    </sql>

    <insert id="insert">
        INSERT INTO JC_ZZZL (
        <include refid="insert_column"/>
        ) VALUES (
            #{id},
            #{ksbm},
            #{zllx},
            #{zzbh},
            #{yxq},
            #{fjbz},
            #{bzsm},
            #{cjr},
            #{cjrq},
            #{xgr},
            #{xgrq}
        )
    </insert>

    <insert id="batchInsert">
        INSERT INTO JC_ZZZL(<include refid="insert_column"/>)
        <foreach collection="list" item="item" separator="UNION ALL" open="(" close=")">
            SELECT
                #{item.id, jdbcType=NUMERIC},
                #{item.ksbm, jdbcType=VARCHAR},
                #{item.zllx, jdbcType=VARCHAR},
                #{item.zzbh, jdbcType=VARCHAR},
                #{item.yxq, jdbcType=DATE},
                #{item.fjbz, jdbcType=NUMERIC},
                #{item.bzsm, jdbcType=VARCHAR},
                #{item.cjr, jdbcType=VARCHAR},
                #{item.cjrq, jdbcType=DATE},
                #{item.xgr, jdbcType=VARCHAR},
                #{item.xgrq, jdbcType=DATE}
            FROM DUAL
        </foreach>
    </insert>

    <select id="nextvalKey" resultType="java.lang.Long">
        SELECT SEQ_JC_ZZZL.nextVal from dual
    </select>

    <select id="getJcZzzlByZzbh" resultType="java.lang.Long">
        SELECT id FROM JC_ZZZL WHERE zzbh = #{zzbh} AND zllx = #{zllx}
    </select>
</mapper>
