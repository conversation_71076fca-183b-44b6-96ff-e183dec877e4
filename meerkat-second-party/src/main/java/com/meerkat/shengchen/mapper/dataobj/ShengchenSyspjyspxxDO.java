package com.meerkat.shengchen.mapper.dataobj;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/14 13:59
 */
public class ShengchenSyspjyspxxDO {

    /**
     * id
     */
    private Long id;

    /**
     * 单据编码
     */
    private String djbm;

    /**
     * 商品编码
     */
    private String spbm;

    /**
     * 商品名称
     */
    private String spmc;

    /**
     * 通用名
     */
    private String tym;

    /**
     * 规格
     */
    private String gg;

    /**
     * 剂型
     */
    private String jx;

    /**
     * 单位
     */
    private String dw;

    /**
     * 生产厂家
     */
    private String sccj;

    /**
     * 商品情况
     */
    private String spqk;

    /**
     * 批准文号
     */
    private String pzwh;

    /**
     * 质量标准
     */
    private String zlbz;

    /**
     * 符合规定标志
     */
    private Integer fhgdbz;

    /**
     * gmp企业/车间标志
     */
    private Integer gmpqycjbz;

    /**
     * gmp认证号
     */
    private String gmprzh;

    /**
     * gmp认证时间
     */
    private Date gmprzsj;

    /**
     * 储存条件
     */
    private String cctj;

    /**
     * 有效期
     */
    private String yxq;

    /**
     * 出厂价
     */
    private BigDecimal ccj;

    /**
     * 采购价
     */
    private BigDecimal cgj;

    /**
     * 销售价
     */
    private BigDecimal xsj;

    /**
     * 零售价
     */
    private BigDecimal lsj;

    /**
     * 批发价
     */
    private BigDecimal pfj;

    /**
     * 包装规格
     */
    private String bzgg;

    /**
     * 业务部门主管意见
     */
    private String ywbmzgyj;

    /**
     * 业务部门主管
     */
    private String ywbmzg;

    /**
     * 业务部门签名日期
     */
    private Date ywbmqmrq;

    /**
     * 采购员申请原因
     */
    private String cgysqyy;

    /**
     * 采购员
     */
    private String cgy;

    /**
     * 采购员签名日期
     */
    private Date cgyqmrq;

    /**
     * 物价部门意见
     */
    private String wjbmyj;

    /**
     * 物价部门签名
     */
    private String wjbmqm;

    /**
     * 物价部门签名日期
     */
    private Date wjbmqmrq;

    /**
     * 质量管理部门意见
     */
    private String zlglbmyj;

    /**
     * 质量管理部门签名
     */
    private String zlglbmqm;

    /**
     * 质量管理部门签名日期
     */
    private Date zlglbmqmrq;

    /**
     * 经理审批意见
     */
    private String jlspyj;

    /**
     * 经理签名
     */
    private String jlqm;

    /**
     * 经理签名日期
     */
    private Date jlqmrq;

    /**
     * 审批通过标志

     0 - 未通过
     1 - 已通过（默认值） 2 - 质管审批不通过 3-质量副总审批不通过
     */
    private Integer sptgbz;

    /**
     * 创建部门
     */
    private String cjbm;

    /**
     * 创建人
     */
    private String cjr;

    /**
     * 创建日期
     */
    private Date cjrq;

    /**
     * 产地
     */
    private String cd;

    /**
     * 拼音码
     */
    private String pym;

    /**
     * 资料齐全标志
     */
    private Integer zlqqbz;

    /**
     * gmp认证范围
     */
    private String gmprzfw;

    /**
     * gmp证书有效期
     */
    private Date gmpzsyxq;

    /**
     * 打印次数
     */
    private Integer dycs;

    /**
     * 作废标志
     */
    private Integer zfbz;

    /**
     * 注册证有效期
     */
    private Date zczyxq;

    /**
     * 委托生产批件有效期
     */
    private Date wtscpjyxq;

    /**
     * 审核状态
     */
    private Integer shzt;

    /**
     * 注册商标
     */
    private String zcsb;

    /**
     * 贮存描述
     */
    private String ccms;

    /**
     * 性状
     */
    private String xz;

    /**
     * 质量标准
     */
    private String zlbz2;

    /**
     * 商品税率
     */
    private BigDecimal spsl;

    /**
     * 商品类别
     */
    private String splb;

    /**
     * 处方类型
     */
    private String cflx;

    /**
     * 是否医保用药
     */
    private Integer sfybyy;

    /**
     * 医保类型
     */
    private String yblx;

    /**
     * 注册证号
     */
    private String zczh;

    /**
     * 生产厂家编码
     */
    private String sccjbm;

    /**
     * 许可号
     */
    private String xkh;

    /**
     * 是否扫描监管码
     */
    private String sfsmjgm;

    /**
     * 用法和用量
     */
    private String yfhyl;

    /**
     * 中包装
     */
    private BigDecimal zbz;

    /**
     * 小包装
     */
    private BigDecimal xbz;

    /**
     * 储存库区
     */
    private String cckq;

    /**
     * 出库单分类
     */
    private String ckdfl;

    /**
     * 品牌类别
     */
    private String pplb;

    /**
     * 品牌考核价
     */
    private BigDecimal ppkhj;

    /**
     * 客商编码
     */
    private String gysbm;

    /**
     * 双人操作标志,1-双人,0-否
     */
    private Integer srcz;

    /**
     * 质检单打印标志,1-是,0-否
     */
    private Integer zjddybz;

    /**
     * 特殊商品管理标志,1-是,0-否
     */
    private Integer tsspgl;

    /**
     * 养护周期,单位：天
     */
    private Integer yhzq;

    /**
     * 禁止现金交易标志,1-禁止,0-否
     */
    private Integer jzxjjybz;

    /**
     * 重点养护标志,1-重点,0-否
     */
    private Integer zdyhbz;

    /**
     * 进口标志,1-进口,0-否
     */
    private Integer jkbz;

    /**
     * 上市许可持有人
     */
    private String ssxkcyr;

    /**
     * 批号管理标志
     */
    private Integer phglbz;

    /**
     * 效期管理标志
     */
    private Integer xqglbz;

    /**
     * 档案编码
     */
    private String dabm;

    /**
     * 厂家许可证有效期
     */
    private Date xkzyxq;

    /**
     * 注册商标有效期
     */
    private Date zcsbyxq;

    /**
     * 通关单打印标志,1-是,0-否
     */
    private Integer tgddybz;

    /**
     * 成份
     */
    private String cf;

    /**
     * 适应症
     */
    private String syz;

    /**
     * 用途
     */
    private String yt;

    /**
     * 医保支付价
     */
    private BigDecimal ybzfj;

    /**
     * 条形码1
     */
    private String txm1;

    /**
     * 分装企业
     */
    private String fzqy;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDjbm() {
        return djbm;
    }

    public void setDjbm(String djbm) {
        this.djbm = djbm;
    }

    public String getSpbm() {
        return spbm;
    }

    public void setSpbm(String spbm) {
        this.spbm = spbm;
    }

    public String getSpmc() {
        return spmc;
    }

    public void setSpmc(String spmc) {
        this.spmc = spmc;
    }

    public String getTym() {
        return tym;
    }

    public void setTym(String tym) {
        this.tym = tym;
    }

    public String getGg() {
        return gg;
    }

    public void setGg(String gg) {
        this.gg = gg;
    }

    public String getJx() {
        return jx;
    }

    public void setJx(String jx) {
        this.jx = jx;
    }

    public String getDw() {
        return dw;
    }

    public void setDw(String dw) {
        this.dw = dw;
    }

    public String getSccj() {
        return sccj;
    }

    public void setSccj(String sccj) {
        this.sccj = sccj;
    }

    public String getSpqk() {
        return spqk;
    }

    public void setSpqk(String spqk) {
        this.spqk = spqk;
    }

    public String getPzwh() {
        return pzwh;
    }

    public void setPzwh(String pzwh) {
        this.pzwh = pzwh;
    }

    public String getZlbz() {
        return zlbz;
    }

    public void setZlbz(String zlbz) {
        this.zlbz = zlbz;
    }

    public Integer getFhgdbz() {
        return fhgdbz;
    }

    public void setFhgdbz(Integer fhgdbz) {
        this.fhgdbz = fhgdbz;
    }

    public Integer getGmpqycjbz() {
        return gmpqycjbz;
    }

    public void setGmpqycjbz(Integer gmpqycjbz) {
        this.gmpqycjbz = gmpqycjbz;
    }

    public String getGmprzh() {
        return gmprzh;
    }

    public void setGmprzh(String gmprzh) {
        this.gmprzh = gmprzh;
    }

    public Date getGmprzsj() {
        return gmprzsj;
    }

    public void setGmprzsj(Date gmprzsj) {
        this.gmprzsj = gmprzsj;
    }

    public String getCctj() {
        return cctj;
    }

    public void setCctj(String cctj) {
        this.cctj = cctj;
    }

    public String getYxq() {
        return yxq;
    }

    public void setYxq(String yxq) {
        this.yxq = yxq;
    }

    public BigDecimal getCcj() {
        return ccj;
    }

    public void setCcj(BigDecimal ccj) {
        this.ccj = ccj;
    }

    public BigDecimal getCgj() {
        return cgj;
    }

    public void setCgj(BigDecimal cgj) {
        this.cgj = cgj;
    }

    public BigDecimal getXsj() {
        return xsj;
    }

    public void setXsj(BigDecimal xsj) {
        this.xsj = xsj;
    }

    public BigDecimal getLsj() {
        return lsj;
    }

    public void setLsj(BigDecimal lsj) {
        this.lsj = lsj;
    }

    public BigDecimal getPfj() {
        return pfj;
    }

    public void setPfj(BigDecimal pfj) {
        this.pfj = pfj;
    }

    public String getBzgg() {
        return bzgg;
    }

    public void setBzgg(String bzgg) {
        this.bzgg = bzgg;
    }

    public String getYwbmzgyj() {
        return ywbmzgyj;
    }

    public void setYwbmzgyj(String ywbmzgyj) {
        this.ywbmzgyj = ywbmzgyj;
    }

    public String getYwbmzg() {
        return ywbmzg;
    }

    public void setYwbmzg(String ywbmzg) {
        this.ywbmzg = ywbmzg;
    }

    public Date getYwbmqmrq() {
        return ywbmqmrq;
    }

    public void setYwbmqmrq(Date ywbmqmrq) {
        this.ywbmqmrq = ywbmqmrq;
    }

    public String getCgysqyy() {
        return cgysqyy;
    }

    public void setCgysqyy(String cgysqyy) {
        this.cgysqyy = cgysqyy;
    }

    public String getCgy() {
        return cgy;
    }

    public void setCgy(String cgy) {
        this.cgy = cgy;
    }

    public Date getCgyqmrq() {
        return cgyqmrq;
    }

    public void setCgyqmrq(Date cgyqmrq) {
        this.cgyqmrq = cgyqmrq;
    }

    public String getWjbmyj() {
        return wjbmyj;
    }

    public void setWjbmyj(String wjbmyj) {
        this.wjbmyj = wjbmyj;
    }

    public String getWjbmqm() {
        return wjbmqm;
    }

    public void setWjbmqm(String wjbmqm) {
        this.wjbmqm = wjbmqm;
    }

    public Date getWjbmqmrq() {
        return wjbmqmrq;
    }

    public void setWjbmqmrq(Date wjbmqmrq) {
        this.wjbmqmrq = wjbmqmrq;
    }

    public String getZlglbmyj() {
        return zlglbmyj;
    }

    public void setZlglbmyj(String zlglbmyj) {
        this.zlglbmyj = zlglbmyj;
    }

    public String getZlglbmqm() {
        return zlglbmqm;
    }

    public void setZlglbmqm(String zlglbmqm) {
        this.zlglbmqm = zlglbmqm;
    }

    public Date getZlglbmqmrq() {
        return zlglbmqmrq;
    }

    public void setZlglbmqmrq(Date zlglbmqmrq) {
        this.zlglbmqmrq = zlglbmqmrq;
    }

    public String getJlspyj() {
        return jlspyj;
    }

    public void setJlspyj(String jlspyj) {
        this.jlspyj = jlspyj;
    }

    public String getJlqm() {
        return jlqm;
    }

    public void setJlqm(String jlqm) {
        this.jlqm = jlqm;
    }

    public Date getJlqmrq() {
        return jlqmrq;
    }

    public void setJlqmrq(Date jlqmrq) {
        this.jlqmrq = jlqmrq;
    }

    public Integer getSptgbz() {
        return sptgbz;
    }

    public void setSptgbz(Integer sptgbz) {
        this.sptgbz = sptgbz;
    }

    public String getCjbm() {
        return cjbm;
    }

    public void setCjbm(String cjbm) {
        this.cjbm = cjbm;
    }

    public String getCjr() {
        return cjr;
    }

    public void setCjr(String cjr) {
        this.cjr = cjr;
    }

    public Date getCjrq() {
        return cjrq;
    }

    public void setCjrq(Date cjrq) {
        this.cjrq = cjrq;
    }

    public String getCd() {
        return cd;
    }

    public void setCd(String cd) {
        this.cd = cd;
    }

    public String getPym() {
        return pym;
    }

    public void setPym(String pym) {
        this.pym = pym;
    }

    public Integer getZlqqbz() {
        return zlqqbz;
    }

    public void setZlqqbz(Integer zlqqbz) {
        this.zlqqbz = zlqqbz;
    }

    public String getGmprzfw() {
        return gmprzfw;
    }

    public void setGmprzfw(String gmprzfw) {
        this.gmprzfw = gmprzfw;
    }

    public Date getGmpzsyxq() {
        return gmpzsyxq;
    }

    public void setGmpzsyxq(Date gmpzsyxq) {
        this.gmpzsyxq = gmpzsyxq;
    }

    public Integer getDycs() {
        return dycs;
    }

    public void setDycs(Integer dycs) {
        this.dycs = dycs;
    }

    public Integer getZfbz() {
        return zfbz;
    }

    public void setZfbz(Integer zfbz) {
        this.zfbz = zfbz;
    }

    public Date getZczyxq() {
        return zczyxq;
    }

    public void setZczyxq(Date zczyxq) {
        this.zczyxq = zczyxq;
    }

    public Date getWtscpjyxq() {
        return wtscpjyxq;
    }

    public void setWtscpjyxq(Date wtscpjyxq) {
        this.wtscpjyxq = wtscpjyxq;
    }

    public Integer getShzt() {
        return shzt;
    }

    public void setShzt(Integer shzt) {
        this.shzt = shzt;
    }

    public String getZcsb() {
        return zcsb;
    }

    public void setZcsb(String zcsb) {
        this.zcsb = zcsb;
    }

    public String getCcms() {
        return ccms;
    }

    public void setCcms(String ccms) {
        this.ccms = ccms;
    }

    public String getXz() {
        return xz;
    }

    public void setXz(String xz) {
        this.xz = xz;
    }

    public String getZlbz2() {
        return zlbz2;
    }

    public void setZlbz2(String zlbz2) {
        this.zlbz2 = zlbz2;
    }

    public BigDecimal getSpsl() {
        return spsl;
    }

    public void setSpsl(BigDecimal spsl) {
        this.spsl = spsl;
    }

    public String getSplb() {
        return splb;
    }

    public void setSplb(String splb) {
        this.splb = splb;
    }

    public String getCflx() {
        return cflx;
    }

    public void setCflx(String cflx) {
        this.cflx = cflx;
    }

    public Integer getSfybyy() {
        return sfybyy;
    }

    public void setSfybyy(Integer sfybyy) {
        this.sfybyy = sfybyy;
    }

    public String getYblx() {
        return yblx;
    }

    public void setYblx(String yblx) {
        this.yblx = yblx;
    }

    public String getZczh() {
        return zczh;
    }

    public void setZczh(String zczh) {
        this.zczh = zczh;
    }

    public String getSccjbm() {
        return sccjbm;
    }

    public void setSccjbm(String sccjbm) {
        this.sccjbm = sccjbm;
    }

    public String getXkh() {
        return xkh;
    }

    public void setXkh(String xkh) {
        this.xkh = xkh;
    }

    public String getSfsmjgm() {
        return sfsmjgm;
    }

    public void setSfsmjgm(String sfsmjgm) {
        this.sfsmjgm = sfsmjgm;
    }

    public String getYfhyl() {
        return yfhyl;
    }

    public void setYfhyl(String yfhyl) {
        this.yfhyl = yfhyl;
    }

    public BigDecimal getZbz() {
        return zbz;
    }

    public void setZbz(BigDecimal zbz) {
        this.zbz = zbz;
    }

    public BigDecimal getXbz() {
        return xbz;
    }

    public void setXbz(BigDecimal xbz) {
        this.xbz = xbz;
    }

    public String getCckq() {
        return cckq;
    }

    public void setCckq(String cckq) {
        this.cckq = cckq;
    }

    public String getCkdfl() {
        return ckdfl;
    }

    public void setCkdfl(String ckdfl) {
        this.ckdfl = ckdfl;
    }

    public String getPplb() {
        return pplb;
    }

    public void setPplb(String pplb) {
        this.pplb = pplb;
    }

    public BigDecimal getPpkhj() {
        return ppkhj;
    }

    public void setPpkhj(BigDecimal ppkhj) {
        this.ppkhj = ppkhj;
    }

    public String getGysbm() {
        return gysbm;
    }

    public void setGysbm(String gysbm) {
        this.gysbm = gysbm;
    }

    public Integer getSrcz() {
        return srcz;
    }

    public void setSrcz(Integer srcz) {
        this.srcz = srcz;
    }

    public Integer getZjddybz() {
        return zjddybz;
    }

    public void setZjddybz(Integer zjddybz) {
        this.zjddybz = zjddybz;
    }

    public Integer getTsspgl() {
        return tsspgl;
    }

    public void setTsspgl(Integer tsspgl) {
        this.tsspgl = tsspgl;
    }

    public Integer getYhzq() {
        return yhzq;
    }

    public void setYhzq(Integer yhzq) {
        this.yhzq = yhzq;
    }

    public Integer getJzxjjybz() {
        return jzxjjybz;
    }

    public void setJzxjjybz(Integer jzxjjybz) {
        this.jzxjjybz = jzxjjybz;
    }

    public Integer getZdyhbz() {
        return zdyhbz;
    }

    public void setZdyhbz(Integer zdyhbz) {
        this.zdyhbz = zdyhbz;
    }

    public Integer getJkbz() {
        return jkbz;
    }

    public void setJkbz(Integer jkbz) {
        this.jkbz = jkbz;
    }

    public String getSsxkcyr() {
        return ssxkcyr;
    }

    public void setSsxkcyr(String ssxkcyr) {
        this.ssxkcyr = ssxkcyr;
    }

    public Integer getPhglbz() {
        return phglbz;
    }

    public void setPhglbz(Integer phglbz) {
        this.phglbz = phglbz;
    }

    public Integer getXqglbz() {
        return xqglbz;
    }

    public void setXqglbz(Integer xqglbz) {
        this.xqglbz = xqglbz;
    }

    public String getDabm() {
        return dabm;
    }

    public void setDabm(String dabm) {
        this.dabm = dabm;
    }

    public Date getXkzyxq() {
        return xkzyxq;
    }

    public void setXkzyxq(Date xkzyxq) {
        this.xkzyxq = xkzyxq;
    }

    public Date getZcsbyxq() {
        return zcsbyxq;
    }

    public void setZcsbyxq(Date zcsbyxq) {
        this.zcsbyxq = zcsbyxq;
    }

    public Integer getTgddybz() {
        return tgddybz;
    }

    public void setTgddybz(Integer tgddybz) {
        this.tgddybz = tgddybz;
    }

    public String getCf() {
        return cf;
    }

    public void setCf(String cf) {
        this.cf = cf;
    }

    public String getSyz() {
        return syz;
    }

    public void setSyz(String syz) {
        this.syz = syz;
    }

    public String getYt() {
        return yt;
    }

    public void setYt(String yt) {
        this.yt = yt;
    }

    public BigDecimal getYbzfj() {
        return ybzfj;
    }

    public void setYbzfj(BigDecimal ybzfj) {
        this.ybzfj = ybzfj;
    }

    public String getTxm1() {
        return txm1;
    }

    public void setTxm1(String txm1) {
        this.txm1 = txm1;
    }

    public String getFzqy() {
        return fzqy;
    }

    public void setFzqy(String fzqy) {
        this.fzqy = fzqy;
    }
}
