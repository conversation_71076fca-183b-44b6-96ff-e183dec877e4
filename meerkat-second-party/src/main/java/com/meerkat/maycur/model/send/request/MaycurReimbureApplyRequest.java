package com.meerkat.maycur.model.send.request;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 对接每刻报销报销单申请参数，参考接口文档：https://maycur.yuque.com/docs/share/8506c171-40c9-4b26-adfc-b3fa072685c0?#pM35l
 * @date 2022/6/6 13:58
 */
public class MaycurReimbureApplyRequest {

    // 非必填，单据号，填写后会使用这个单据号作为单据的编号
    private String formCode;

    // 必填，表单类型的业务编号
    private String formSubTypeBizCode;

    // 必填，提单人的工号
    private String submittedUserEmployeeId;

    // 必填，报销单事由
    private String reimburseName;

    // 必填，公司抬头业务编码
    private String legalEntityBizCode;

    // 必填，承担人工号
    private String coverUserEmployeeId;

    // 必填，承担部门业务编码
    private String coverDepartmentBizCode;

    // 遵循表单配置，备注信息
    private String comments;

    // 遵循表单配置表单中配置为对公账户时，必须传往来单位，收款账户信息
    private PayeeAccount payeeAccount;

    // 遵循表单配置，多人收款，如果已填充该字段，则无需填充上方的收款账户字段
    // private CollectionTradingType collectionSchedule;

    // 勋勋表单配置，多人收款场景表单业务编码，需要填写多人收款时必填
    private String paymentSceneBizCode;

    // 必填，费用的每刻内码Code列表，费用需要使用费用导入接口导入，导入后，会返回费用的Code
    private List<String> expenseCodes;

    // 遵循表单配置，自定义普通字段
    private Map<String, Object> customObject;

    // 遵循表单配置，往来单位（供应商）业务编码
    private String tradingPartnerBizCode;

    // 非必填，提单时间，毫秒时间戳，非必填，默认为单据导入时间
    private String submittedTime;

    // 非必填，暂存标识，非必填，默认为false表示不暂存
    private boolean stagingFlag;

    // 非必填，报销单关联的申请单号
    private List<String> preConsumeCodeList;

    public static class PayeeAccount {
        // 银行账户名
        String bankAcctName;

        // 账户付款账户编号
        String bankAcctNumber;

        // 支付类型，示例值：BANK
        String paymentType;

        // 账户类型，示例值：PERSONAL
        String accountType;

        public PayeeAccount(String bankAcctName, String bankAcctNumber) {
            this.bankAcctName = bankAcctName;
            this.bankAcctNumber = bankAcctNumber;
            this.paymentType = "BANK";
            accountType = "CORP";
        }

        public String getBankAcctName() {
            return bankAcctName;
        }

        public void setBankAcctName(String bankAcctName) {
            this.bankAcctName = bankAcctName;
        }

        public String getBankAcctNumber() {
            return bankAcctNumber;
        }

        public void setBankAcctNumber(String bankAcctNumber) {
            this.bankAcctNumber = bankAcctNumber;
        }

        public String getPaymentType() {
            return paymentType;
        }

        public void setPaymentType(String paymentType) {
            this.paymentType = paymentType;
        }

        public String getAccountType() {
            return accountType;
        }

        public void setAccountType(String accountType) {
            this.accountType = accountType;
        }
    }

    public String getFormCode() {
        return formCode;
    }

    public void setFormCode(String formCode) {
        this.formCode = formCode;
    }

    public String getFormSubTypeBizCode() {
        return formSubTypeBizCode;
    }

    public void setFormSubTypeBizCode(String formSubTypeBizCode) {
        this.formSubTypeBizCode = formSubTypeBizCode;
    }

    public String getSubmittedUserEmployeeId() {
        return submittedUserEmployeeId;
    }

    public void setSubmittedUserEmployeeId(String submittedUserEmployeeId) {
        this.submittedUserEmployeeId = submittedUserEmployeeId;
    }

    public String getReimburseName() {
        return reimburseName;
    }

    public void setReimburseName(String reimburseName) {
        this.reimburseName = reimburseName;
    }

    public String getLegalEntityBizCode() {
        return legalEntityBizCode;
    }

    public void setLegalEntityBizCode(String legalEntityBizCode) {
        this.legalEntityBizCode = legalEntityBizCode;
    }

    public String getCoverUserEmployeeId() {
        return coverUserEmployeeId;
    }

    public void setCoverUserEmployeeId(String coverUserEmployeeId) {
        this.coverUserEmployeeId = coverUserEmployeeId;
    }

    public String getCoverDepartmentBizCode() {
        return coverDepartmentBizCode;
    }

    public void setCoverDepartmentBizCode(String coverDepartmentBizCode) {
        this.coverDepartmentBizCode = coverDepartmentBizCode;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public PayeeAccount getPayeeAccount() {
        return payeeAccount;
    }

    public void setPayeeAccount(PayeeAccount payeeAccount) {
        this.payeeAccount = payeeAccount;
    }

    public String getPaymentSceneBizCode() {
        return paymentSceneBizCode;
    }

    public void setPaymentSceneBizCode(String paymentSceneBizCode) {
        this.paymentSceneBizCode = paymentSceneBizCode;
    }

    public List<String> getExpenseCodes() {
        return expenseCodes;
    }

    public void setExpenseCodes(List<String> expenseCodes) {
        this.expenseCodes = expenseCodes;
    }

    public Map<String, Object> getCustomObject() {
        return customObject;
    }

    public void setCustomObject(Map<String, Object> customObject) {
        this.customObject = customObject;
    }

    public String getTradingPartnerBizCode() {
        return tradingPartnerBizCode;
    }

    public void setTradingPartnerBizCode(String tradingPartnerBizCode) {
        this.tradingPartnerBizCode = tradingPartnerBizCode;
    }

    public String getSubmittedTime() {
        return submittedTime;
    }

    public void setSubmittedTime(String submittedTime) {
        this.submittedTime = submittedTime;
    }

    public boolean isStagingFlag() {
        return stagingFlag;
    }

    public void setStagingFlag(boolean stagingFlag) {
        this.stagingFlag = stagingFlag;
    }

    public List<String> getPreConsumeCodeList() {
        return preConsumeCodeList;
    }

    public void setPreConsumeCodeList(List<String> preConsumeCodeList) {
        this.preConsumeCodeList = preConsumeCodeList;
    }
}