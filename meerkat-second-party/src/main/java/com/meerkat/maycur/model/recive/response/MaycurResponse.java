package com.meerkat.maycur.model.recive.response;

/**
 * <AUTHOR>
 * @description
 * @date 2022/6/10 10:59
 */
public class MaycurResponse<T> {

    private boolean success;

    private String errorMsg;

    private T data;

    public MaycurResponse(boolean success, String errorMsg, T data) {
        this.success = success;
        this.errorMsg = errorMsg;
        this.data = data;
    }

    public static <T> MaycurResponse isSuccess(T data) {
        return new MaycurResponse(true, "", data);
    }

    public static <T> MaycurResponse isFaild(String errorMsg) {
        return new MaycurResponse(false, errorMsg, null);
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
