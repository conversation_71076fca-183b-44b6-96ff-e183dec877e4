package com.meerkat.maycur.dto.param;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 对公费用导入参数
 * @date 2022/6/6 16:38
 */
public class BusinessExpenseImportParamDTO {

    /**
     * 需要导入的对应员工的工号
     */
    private String employeeId;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 预计到票日期（时间戳）
     */
    private Long forecastReceiptDate;

    /**
     * 供应商编码
     */
    private String tradingPartnerBizCode;

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getForecastReceiptDate() {
        return forecastReceiptDate;
    }

    public void setForecastReceiptDate(Long forecastReceiptDate) {
        this.forecastReceiptDate = forecastReceiptDate;
    }

    public String getTradingPartnerBizCode() {
        return tradingPartnerBizCode;
    }

    public void setTradingPartnerBizCode(String tradingPartnerBizCode) {
        this.tradingPartnerBizCode = tradingPartnerBizCode;
    }
}
