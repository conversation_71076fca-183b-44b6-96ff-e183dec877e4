package com.meerkat.maycur.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpDownloader;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.meerkat.common.exception.BizException;
import com.meerkat.maycur.convert.MaycurPurchaseContractConvert;
import com.meerkat.maycur.api.MaycurSendApi;
import com.meerkat.maycur.enums.MaycurException;
import com.meerkat.maycur.model.MaycurPurchaseContract;
import com.meerkat.maycur.model.data.MaycurAttachInput;
import com.meerkat.maycur.model.excel.MaycurPurchaseContractFileExcel;
import com.meerkat.maycur.model.send.request.MaycurContractQueryRequest;
import com.meerkat.maycur.model.send.response.MaycurContractQueryResponse;
import com.meerkat.maycur.model.send.MaycurResponse;
import com.meerkat.maycur.service.MaycurPurchaseContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @description
 * @date 2022/8/11 11:26
 */
@Service
public class MaycurPurchaseContractServiceImpl implements MaycurPurchaseContractService {

    @Autowired
    private MaycurSendApi maycurSendApi;

    @Autowired
    private MaycurPurchaseContractConvert convert;

    @Override
    public MaycurPurchaseContract getPurchaseContractDetail(String formCode, String contractNum) {
        MaycurResponse<MaycurContractQueryResponse> reponse = maycurSendApi.doGet(
                MaycurSendApi.QUERY_CONTRACT, MapUtil.of(MaycurSendApi.PARAM_CONTRACT_FORM_CODE, formCode), new MaycurContractQueryRequest());

        MaycurContractQueryResponse data = reponse.getData();
        if (data == null) {
            throw new BizException(MaycurException.PURCHASE_CONTRACT_DETAIL_NOT_FOUND);
        }

        if (StrUtil.isEmpty(data.getContractNum())) {
            // 若每刻查询详情中无合同编号，使用外部传入合同编号
            data.setContractNum(contractNum);
        }
        return convert.convertResponseToModel(data);
    }

    @Override
    public MaycurPurchaseContractFileExcel analysisExcel(MaycurPurchaseContract maycurPurchaseContract) {
        // 读取文件
        List<MaycurAttachInput> initialContract = maycurPurchaseContract.getAttachments().getInitialContract();
        for (MaycurAttachInput maycurAttachInput : initialContract) {
            // 如果是标准模版Excel，则下载解析处理
            if (maycurAttachInput.getName().endsWith("xlsx") || maycurAttachInput.getName().endsWith("xlx")) {
                MaycurPurchaseContractFileExcel result = new MaycurPurchaseContractFileExcel();

                byte[] bytes = HttpDownloader.downloadBytes(maycurAttachInput.getUrl());
                ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
                EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
                    private int line = 0;

                    @Override
                    public void invoke(Map<Integer, String> data, AnalysisContext context) {
                        // 第五行读取品种、规格、生产厂家、单位
                        if (line == 5) {
                            // 品种
                            result.setVariety(data.get(0));

                            // 规格
                            result.setSpecification(data.get(1));

                            // 生产厂家
                            result.setManuFacturer(data.get(2));

                            // 单位
                            result.setUnit(data.get(3));

                            // 数量
                            result.setQuantity(Integer.valueOf(data.get(4)));

                            // 单价
                            result.setUnitPrice(new BigDecimal(data.get(5)));
                        }

                        line ++;
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {

                    }
                }).doReadAll();

                return result;
            }
        }
        return null;
    }

}
