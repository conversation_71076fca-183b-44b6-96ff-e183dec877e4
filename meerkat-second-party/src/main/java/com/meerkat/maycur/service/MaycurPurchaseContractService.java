package com.meerkat.maycur.service;

import com.meerkat.maycur.model.MaycurPurchaseContract;
import com.meerkat.maycur.model.excel.MaycurPurchaseContractFileExcel;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/11 11:06
 */
public interface MaycurPurchaseContractService {

    /**
     * 获取采购合同明细
     * <AUTHOR>
     * @date 2022/8/15 17:07
     * @param formCode
     * @param contractNum 目前每刻查询详情中没有合同编号，需要手动传入构建
     * @return com.meerkat.maycur.model.MaycurPurchaseContract
     */
    MaycurPurchaseContract getPurchaseContractDetail(String formCode, String contractNum);

    /**
     * 解析每刻合同附件标准Excel
     * <AUTHOR>
     * @date 2022/8/15 17:16
     * @param
     * @return com.meerkat.maycur.model.excel.MaycurPurchaseContractFileExcel
     */
    MaycurPurchaseContractFileExcel analysisExcel(MaycurPurchaseContract maycurPurchaseContract);
}
