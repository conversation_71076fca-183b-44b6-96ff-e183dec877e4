package com.meerkat.meerkat.service;

import com.meerkat.meerkat.dto.param.MeerkatVaccinesCostLoadDTO;
import com.meerkat.meerkat.model.MeerkatVaccinesCostBase;
import com.meerkat.meerkat.model.MeerkatVaccinesCostResult;

import java.util.List;

/**
 * <AUTHOR>
 * @description 疫苗成本数据采集服务
 * @date 2022/10/12 16:46
 */
public interface MeerkatVaccinesCostService {

    /**
     * 加载疫苗成本基础数据
     * <AUTHOR>
     * @date 2022/10/12 17:03
     * @param dto
     * @return java.util.List<com.meerkat.meerkat.model.MeerkatVaccinesCostBase>
     */
    List<MeerkatVaccinesCostBase> loadBase(MeerkatVaccinesCostLoadDTO dto);

    /**
     * 加载疫苗成本结果数据
     * <AUTHOR>
     * @date 2022/10/12 17:06
     * @param dto
     * @return java.util.List<com.meerkat.meerkat.model.MeerkatVaccinesCostBase>
     */
    List<MeerkatVaccinesCostResult> loadResult(MeerkatVaccinesCostLoadDTO dto);
}
