<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.report.mapper.ReportSykhspxxMapper">
    <resultMap id="BaseResultMap" type="com.meerkat.report.mapper.dataobj.ReportSykhspxxDO">
        <result column="skmerp_code" property="skmerpCode" />
        <result column="ksbm" property="ksbm" />
        <result column="ksmc" property="ksmc" />
        <result column="ksjc" property="ksjc" />
        <result column="ksfl" property="ksfl" />
        <result column="kslb" property="kslb" />
        <result column="qhbm" property="qhbm" />
        <result column="sjksbm" property="sjksbm" />
        <result column="frdb" property="frdb" />
        <result column="fzr1" property="fzr1" />
        <result column="fzr2" property="fzr2" />
        <result column="jyfwdl" property="jyfwdl" />
        <result column="jyfwxl" property="jyfwxl" />
        <result column="jyfwxlmc" property="jyfwxlmc" />
        <result column="shbz" property="shbz" />
        <result column="gmpbz" property="gmpbz" />
        <result column="gspbz" property="gspbz" />
        <result column="zlqqbz" property="zlqqbz" />
        <result column="jsfs" property="jsfs" />
        <result column="khh" property="khh" />
        <result column="zh" property="zh" />
        <result column="sfzh" property="sfzh" />
        <result column="lxr" property="lxr" />
        <result column="lxdh" property="lxdh" />
        <result column="jj" property="jj" />
        <result column="dh" property="dh" />
        <result column="cz" property="cz" />
        <result column="dzyj" property="dzyj" />
        <result column="dwdz" property="dwdz" />
        <result column="ckdz" property="ckdz" />
        <result column="yzbm" property="yzbm" />
        <result column="bzsm" property="bzsm" />
        <result column="jsqx" property="jsqx" />
        <result column="dljsbz" property="dljsbz" />
        <result column="htbz" property="htbz" />
        <result column="djxsw" property="djxsw" />
        <result column="xyed" property="xyed" />
        <result column="tzxsbz" property="tzxsbz" />
        <result column="tzcgbz" property="tzcgbz" />
        <result column="pym" property="pym" />
        <result column="llbj" property="llbj" />
        <result column="sh" property="sh" />
        <result column="ysxl" property="ysxl" />
        <result column="fpdyms" property="fpdyms" />
        <result column="wbxtid" property="wbxtid" />
        <result column="ksxz" property="ksxz" />
        <result column="ksabclb" property="ksabclb" />
        <result column="sybz" property="sybz" />
        <result column="sykssj" property="sykssj" />
        <result column="syjssj" property="syjssj" />
        <result column="zkl1" property="zkl1" />
        <result column="zkl2" property="zkl2" />
        <result column="zkl3" property="zkl3" />
        <result column="jgml1" property="jgml1" />
        <result column="jgml2" property="jgml2" />
        <result column="jgml3" property="jgml3" />
        <result column="zyzkyj" property="zyzkyj" />
        <result column="zfbz" property="zfbz" />
        <result column="cjr" property="cjr" />
        <result column="cjrq" property="cjrq" />
        <result column="xgr" property="xgr" />
        <result column="xgrq" property="xgrq" />
        <result column="khzzyxbz" property="khzzyxbz" />
        <result column="khzztsxx" property="khzztsxx" />
        <result column="mhjzzyxbz" property="mhjzzyxbz" />
        <result column="ypxkzyxbz" property="ypxkzyxbz" />
        <result column="qxxkzyxbz" property="qxxkzyxbz" />
        <result column="fkms" property="fkms" />
        <result column="fkxsbl" property="fkxsbl" />
        <result column="fkdhts" property="fkdhts" />
        <result column="ssbm" property="ssbm" />
        <result column="hzwbz" property="hzwbz" />
        <result column="khdygysbm" property="khdygysbm" />
        <result column="jjxz" property="jjxz" />
        <result column="cfyjyzz" property="cfyjyzz" />
        <result column="khxyj" property="khxyj" />
        <result column="zjywfssj" property="zjywfssj" />
        <result column="zlfwdm" property="zlfwdm" />
        <result column="zlfw" property="zlfw" />
        <result column="fldjxsw" property="fldjxsw" />
        <result column="jsr" property="jsr" />
        <result column="oldksbm" property="oldksbm" />
        <result column="kpmc" property="kpmc" />
        <result column="djbz" property="djbz" />
        <result column="cgyfje" property="cgyfje" />
        <result column="ghfpce" property="ghfpce" />
        <result column="khhm" property="khhm" />
        <result column="ywjdlx" property="ywjdlx" />
        <result column="sfyb" property="sfyb" />
        <result column="jyzy" property="jyzy" />
        <result column="wbm" property="wbm" />
        <result column="zdym" property="zdym" />
        <result column="gskplx" property="gskplx" />
        <result column="zcdz" property="zcdz" />
        <result column="yskzje" property="yskzje" />
        <result column="splxdh" property="splxdh" />
        <result column="dwywlx" property="dwywlx" />
        <result column="shcs" property="shcs" />
        <result column="ysy" property="ysy" />
        <result column="kdgs" property="kdgs" />
        <result column="xsyid" property="xsyid" />
        <result column="xsnqid" property="xsnqid" />
        <result column="dlzbjd" property="dlzbjd" />
        <result column="dlzbwd" property="dlzbwd" />
        <result column="jylcsp" property="jylcsp" />
        <result column="jyymsp" property="jyymsp" />
        <result column="kkspje" property="kkspje" />
        <result column="sfqhbm" property="sfqhbm" />
        <result column="xsqhbm" property="xsqhbm" />
        <result column="dqqhbm" property="dqqhbm" />
        <result column="cflx" property="cflx" />
        <result column="khbz" property="khbz" />
        <result column="scfwmc" property="scfwmc" />
        <result column="scfw" property="scfw" />
        <result column="kpdz" property="kpdz" />
        <result column="jgml4" property="jgml4" />
        <result column="jgml5" property="jgml5" />
        <result column="jgml6" property="jgml6" />
        <result column="jgml7" property="jgml7" />
        <result column="jstj" property="jstj" />
        <result column="psbtbl" property="psbtbl" />
        <result column="cyfbl" property="cyfbl" />
        <result column="shfs" property="shfs" />
        <result column="zrbl" property="zrbl" />
        <result column="zrjesx" property="zrjesx" />
        <result column="zq" property="zq" />
        <result column="zjddybz" property="zjddybz" />
        <result column="shr" property="shr" />
        <result column="shrq" property="shrq" />
        <result column="qrr" property="qrr" />
        <result column="qrrq" property="qrrq" />
        <result column="ygtbz" property="ygtbz" />
        <result column="shhr" property="shhr" />
        <result column="shhdh" property="shhdh" />
        <result column="dqjl" property="dqjl" />
        <result column="zg" property="zg" />
        <result column="dabm" property="dabm" />
        <result column="tjr" property="tjr" />
        <result column="tjrq" property="tjrq" />
        <result column="qykhyh" property="qykhyh" />
        <result column="qyzh" property="qyzh" />
        <result column="qyfzr" property="qyfzr" />
        <result column="qyfzrdh" property="qyfzrdh" />
        <result column="zlfzr" property="zlfzr" />
        <result column="zlfzrdh" property="zlfzrdh" />
    </resultMap>

    <sql id = "select_column">

    </sql>

    <sql id = "insert_column">
        id,
        skmerp_code,
        ksbm,
        ksmc,
        ksjc,
        ksfl,
        kslb,
        qhbm,
        frdb,
        fzr1,
        jyfwdl,
        jyfwxl,
        jsfs,
        khh,
        zh,
        dwdz,
        ckdz,
        zcdz,
        pym
    </sql>
    <insert id="insert" keyColumn="id" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO GSP_SYKHSPXX (
            <include refid="insert_column"/>
        ) VALUES (
            #{id},
            #{skmerpCode},
            #{ksbm},
            #{ksmc},
            #{ksjc},
            #{ksfl},
            #{kslb},
            #{qhbm},
            #{frdb},
            #{fzr1},
            #{jyfwdl},
            #{jyfwxl},
            #{jsfs},
            #{khh},
            #{zh},
            #{dwdz},
            #{ckdz},
            #{zcdz},
            #{pym}
        )
    </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="java.util.List">
        INSERT INTO GSP_SYKHSPXX (
            <include refid="insert_column"/>
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id},
                #{item.skmerpCode},
                #{item.ksbm},
                #{item.ksmc},
                #{item.ksjc},
                #{item.ksfl},
                #{item.kslb},
                #{item.qhbm},
                #{item.frdb},
                #{item.fzr1},
                #{item.jyfwdl},
                #{item.jyfwxl},
                #{item.jsfs},
                #{item.khh},
                #{item.zh},
                #{item.dwdz},
                #{item.ckdz},
                #{item.zcdz},
                #{item.pym}
            )
        </foreach>
    </insert>

</mapper>
