package com.meerkat.report.mapper.dataobj;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/23 15:12
 */
public class ReportJcZzzlDO implements Serializable {

    private static final long serialVersionUID = -2922247004018985142L;
    private Long id;

    /**
     * 客商编码
     */
    private String ksbm;

    /**
     * 资料类型
     0 - 器械许可证
     1 - 营业执照
     2 - 药品许可证
     3 - 委托期限
     4 - 质量协议书
     5 - 麻黄碱委托书
     6 - gsp证号
     7 - gmp证号
     8 - 医疗机构执业许可证
     */
    private String zllx;

    /**
     * 证照编号
     */
    private String zzbh;

    /**
     * 证照内容
     */
    private String zznr;

    /**
     * 有效期
     */
    private Date yxq;

    /**
     * 失效标志
     */
    private Integer xsbz;

    /**
     * 附件标志
     */
    private Integer fjbz;

    /**
     * 作废标志
     */
    private Integer zfbz;

    /**
     * 备注说明
     */
    private String bzsm;

    /**
     * 创建人
     */
    private String cjr;

    /**
     * 创建日期
     */
    private Date cjrq;

    /**
     * 修改人
     */
    private String xgr;

    /**
     * 修改日期
     */
    private Date xgrq;

    /**
     * 修改日期
     */
    private Date fzrq;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getKsbm() {
        return ksbm;
    }

    public void setKsbm(String ksbm) {
        this.ksbm = ksbm;
    }

    public String getZllx() {
        return zllx;
    }

    public void setZllx(String zllx) {
        this.zllx = zllx;
    }

    public String getZzbh() {
        return zzbh;
    }

    public void setZzbh(String zzbh) {
        this.zzbh = zzbh;
    }

    public String getZznr() {
        return zznr;
    }

    public void setZznr(String zznr) {
        this.zznr = zznr;
    }

    public Date getYxq() {
        return yxq;
    }

    public void setYxq(Date yxq) {
        this.yxq = yxq;
    }

    public Integer getXsbz() {
        return xsbz;
    }

    public void setXsbz(Integer xsbz) {
        this.xsbz = xsbz;
    }

    public Integer getFjbz() {
        return fjbz;
    }

    public void setFjbz(Integer fjbz) {
        this.fjbz = fjbz;
    }

    public Integer getZfbz() {
        return zfbz;
    }

    public void setZfbz(Integer zfbz) {
        this.zfbz = zfbz;
    }

    public String getBzsm() {
        return bzsm;
    }

    public void setBzsm(String bzsm) {
        this.bzsm = bzsm;
    }

    public String getCjr() {
        return cjr;
    }

    public void setCjr(String cjr) {
        this.cjr = cjr;
    }

    public Date getCjrq() {
        return cjrq;
    }

    public void setCjrq(Date cjrq) {
        this.cjrq = cjrq;
    }

    public String getXgr() {
        return xgr;
    }

    public void setXgr(String xgr) {
        this.xgr = xgr;
    }

    public Date getXgrq() {
        return xgrq;
    }

    public void setXgrq(Date xgrq) {
        this.xgrq = xgrq;
    }

    public Date getFzrq() {
        return fzrq;
    }

    public void setFzrq(Date fzrq) {
        this.fzrq = fzrq;
    }
}
