package com.meerkat.report.mapper;

import com.meerkat.report.mapper.dataobj.ReportDrugSalesRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/1 15:26
 */
@Mapper
public interface ReportDrugSalesRecordMapper {

    List<String> listAlreadyExist(@Param("outRecordSnList") List<String> outRecordSnList, @Param("source") int source);

    int batchInsert(List<ReportDrugSalesRecordDO> list);
}
