package com.meerkat.report.dto.param;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/14 15:36
 */
public class ReportSykhspxxSaveParamDTO {

    /**
     *  id
     */
    private Long id;

    /**
     * 时空客商编码
     */
    private String skmerpCode;

    /**
     * 客商编码
     */
    private String ksbm;

    /**
     * 客商名称
     */
    private String ksmc;

    /**
     * 客商简称
     */
    private String ksjc;

    /**
     * 客商分类
     */
    private Integer ksfl;

    /**
     * 客商类别企业--------------------------
     * 10	医疗机构
     * 1015	市级及市级以上医院
     * 1011	区及区级以上医院
     * 1012	社区卫生服务中心
     * 1014	其他医疗机构
     * 1016	民营医疗机构企业
     * 40	企业---------------------------
     * 4041	批发	自定义类型
     * 4043	零售连锁
     * 4044	零售单体
     */
    private String kslb;

    /**
     * 区划编码
     */
    private String qhbm;

    /**
     * 法人代表
     */
    private String frdb;

    /**
     * 负责人1（销售）
     */
    private String fzr1;

    /**
     * 负责人2（采购）
     */
    private String fzr2;

    /**
     * 经营范围大类
     */
    private String jyfwdl;

    /**
     * 经营范围小类
     */
    private String jyfwxl;

    /**
     * 结算方式 3：电汇、4：承兑汇票
     */
    private Integer jsfs;

    /**
     * 开户行
     */
    private String khh;

    /**
     * 帐户
     */
    private String zh;

    /**
     * 单位地址
     */
    private String dwdz;

    /**
     * 仓库地址
     */
    private String ckdz;

    /**
     * 注册地址
     */
    private String zcdz;

    /**
     * 拼音码
     */
    private String pym;

     /**
     * 税号
     */
    private String sh;

    /**
     * 收货人
     */
    private String shhr;

    /**
     * 收货电话
     */
    private String shhdh;

    private List<ReportJcZzzlSaveParamDTO> jcZzzlSaveParamDTOList;

    private List<ReportJcShdzSaveParamDTO> jcShdzSaveParamDTOList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSkmerpCode() {
        return skmerpCode;
    }

    public void setSkmerpCode(String skmerpCode) {
        this.skmerpCode = skmerpCode;
    }

    public String getKsbm() {
        return ksbm;
    }

    public void setKsbm(String ksbm) {
        this.ksbm = ksbm;
    }

    public String getKsmc() {
        return ksmc;
    }

    public void setKsmc(String ksmc) {
        this.ksmc = ksmc;
    }

    public String getKsjc() {
        return ksjc;
    }

    public void setKsjc(String ksjc) {
        this.ksjc = ksjc;
    }

    public Integer getKsfl() {
        return ksfl;
    }

    public void setKsfl(Integer ksfl) {
        this.ksfl = ksfl;
    }

    public String getKslb() {
        return kslb;
    }

    public void setKslb(String kslb) {
        this.kslb = kslb;
    }

    public String getQhbm() {
        return qhbm;
    }

    public void setQhbm(String qhbm) {
        this.qhbm = qhbm;
    }

    public String getFrdb() {
        return frdb;
    }

    public void setFrdb(String frdb) {
        this.frdb = frdb;
    }

    public String getFzr1() {
        return fzr1;
    }

    public void setFzr1(String fzr1) {
        this.fzr1 = fzr1;
    }

    public String getFzr2() {
        return fzr2;
    }

    public void setFzr2(String fzr2) {
        this.fzr2 = fzr2;
    }

    public String getJyfwdl() {
        return jyfwdl;
    }

    public void setJyfwdl(String jyfwdl) {
        this.jyfwdl = jyfwdl;
    }

    public String getJyfwxl() {
        return jyfwxl;
    }

    public void setJyfwxl(String jyfwxl) {
        this.jyfwxl = jyfwxl;
    }

    public Integer getJsfs() {
        return jsfs;
    }

    public void setJsfs(Integer jsfs) {
        this.jsfs = jsfs;
    }

    public String getKhh() {
        return khh;
    }

    public void setKhh(String khh) {
        this.khh = khh;
    }

    public String getZh() {
        return zh;
    }

    public void setZh(String zh) {
        this.zh = zh;
    }

    public String getDwdz() {
        return dwdz;
    }

    public void setDwdz(String dwdz) {
        this.dwdz = dwdz;
    }

    public String getCkdz() {
        return ckdz;
    }

    public void setCkdz(String ckdz) {
        this.ckdz = ckdz;
    }

    public String getZcdz() {
        return zcdz;
    }

    public void setZcdz(String zcdz) {
        this.zcdz = zcdz;
    }

    public String getPym() {
        return pym;
    }

    public void setPym(String pym) {
        this.pym = pym;
    }

    public String getSh() {
        return sh;
    }

    public void setSh(String sh) {
        this.sh = sh;
    }

    public String getShhr() {
        return shhr;
    }

    public void setShhr(String shhr) {
        this.shhr = shhr;
    }

    public String getShhdh() {
        return shhdh;
    }

    public void setShhdh(String shhdh) {
        this.shhdh = shhdh;
    }

    public List<ReportJcZzzlSaveParamDTO> getJcZzzlSaveParamDTOList() {
        return jcZzzlSaveParamDTOList;
    }

    public void setJcZzzlSaveParamDTOList(List<ReportJcZzzlSaveParamDTO> jcZzzlSaveParamDTOList) {
        this.jcZzzlSaveParamDTOList = jcZzzlSaveParamDTOList;
    }

    public List<ReportJcShdzSaveParamDTO> getJcShdzSaveParamDTOList() {
        return jcShdzSaveParamDTOList;
    }

    public ReportSykhspxxSaveParamDTO setJcShdzSaveParamDTOList(List<ReportJcShdzSaveParamDTO> jcShdzSaveParamDTOList) {
        this.jcShdzSaveParamDTOList = jcShdzSaveParamDTOList;
        return this;
    }
}
