package com.meerkat.report.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.report.dto.param.ReportDrugSalesRecordSaveDTO;
import com.meerkat.report.mapper.ReportDrugSalesRecordMapper;
import com.meerkat.report.mapper.dataobj.ReportDrugSalesRecordDO;
import com.meerkat.report.service.ReportDrugSalesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/5 10:50
 */
@Service
public class ReportDrugSalesServiceImpl implements ReportDrugSalesService {

    @Autowired
    ReportDrugSalesRecordMapper drugSalesRecordMapper;

    @Override
    public int batchSave(List<ReportDrugSalesRecordSaveDTO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return 0;
        }

        // 重复不插入
        List<String> recordSnList = drugSalesRecordMapper.listAlreadyExist(
                list.stream().map(ReportDrugSalesRecordSaveDTO::getOutRecordSn).collect(Collectors.toList()),
                list.get(0).getSource()
        );

        // 去冲并转换DO
        List<ReportDrugSalesRecordDO> insertDOS = list.stream()
                .filter(item -> !recordSnList.contains(item.getOutRecordSn()))
                .map(item -> CopyUtil.copy(item, ReportDrugSalesRecordDO.class))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(insertDOS)) {
            return drugSalesRecordMapper.batchInsert(insertDOS);
        }
        return 0;
    }
}
