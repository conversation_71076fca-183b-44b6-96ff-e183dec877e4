package com.meerkat.report.service.impl;

import com.meerkat.common.utils.CopyUtil;
import com.meerkat.report.dto.param.ReportSyspjyspxxSaveParamDTO;
import com.meerkat.report.mapper.ReportSyspjyspxxMapper;
import com.meerkat.report.mapper.dataobj.ReportSyspjyspxxDO;
import com.meerkat.report.service.ReportSyspjyspxxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/20 13:53
 */
@Service
public class ReportSyspjyspxxServiceImpl implements ReportSyspjyspxxService {

    @Autowired
    private ReportSyspjyspxxMapper reportSyspjyspxxMapper;

    @Override
    public Boolean save(ReportSyspjyspxxSaveParamDTO reportSyspjyspxxSaveParamDTO) {
        return reportSyspjyspxxMapper.insert(
                CopyUtil.copy(reportSyspjyspxxSaveParamDTO, ReportSyspjyspxxDO.class)
        ) == 1;
    }

    @Override
    public int batchSave(List<ReportSyspjyspxxSaveParamDTO> saveParamDTOS) {
        return reportSyspjyspxxMapper.batchInsert(
                CopyUtil.copyList(saveParamDTOS, ReportSyspjyspxxDO.class)
        );
    }
}
