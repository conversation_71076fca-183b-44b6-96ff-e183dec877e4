package com.meerkat.report.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/17 16:46
 */
public class ReportPurchaseContract {

    /**
     * id
     */
    private Long id;

    /**
     * 合同来源 0：安徽火烈鸟、1：杭州火烈鸟
     */
    private String contractSource;

    /**
     * 外部单据编码（每刻
     */
    private String outFormCode;

    /**
     * 合同编号
     */
    private String contractNum;

    /**
     * 上游供应商
     */
    private String upperSupplier;

    /**
     * 联系方式
     */
    private String contactNum;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 提单人
     */
    private String submittingPerson;

    /**
     * 品种
     */
    private String variety;

    /**
     * 规格
     */
    private String specification;

    /**
     * 生产厂家
     */
    private String manuFacturer;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 单价，单位元
     */
    private BigDecimal unitPrice;

    /**
     * 价格
     */
    private Long price;

    /**
     * 总价，单位元
     */
    private BigDecimal totalPrice;

    /**
     * 提单日期
     */
    private LocalDateTime submittingDate;

    /**
     * 到货日期
     */
    private LocalDateTime validatyStartDate;

    /**
     * 签署日期
     */
    private LocalDateTime validatyEndDate;

    /**
     * 附件信息（Json)
     */
    private List<Attachment> attachmentList;

    public static class Attachment {

        private String name;

        private String url;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getContractSource() {
        return contractSource;
    }

    public void setContractSource(String contractSource) {
        this.contractSource = contractSource;
    }

    public String getOutFormCode() {
        return outFormCode;
    }

    public void setOutFormCode(String outFormCode) {
        this.outFormCode = outFormCode;
    }

    public String getContractNum() {
        return contractNum;
    }

    public void setContractNum(String contractNum) {
        this.contractNum = contractNum;
    }

    public String getUpperSupplier() {
        return upperSupplier;
    }

    public void setUpperSupplier(String upperSupplier) {
        this.upperSupplier = upperSupplier;
    }

    public String getContactNum() {
        return contactNum;
    }

    public void setContactNum(String contactNum) {
        this.contactNum = contactNum;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getSubmittingPerson() {
        return submittingPerson;
    }

    public void setSubmittingPerson(String submittingPerson) {
        this.submittingPerson = submittingPerson;
    }

    public String getVariety() {
        return variety;
    }

    public void setVariety(String variety) {
        this.variety = variety;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getManuFacturer() {
        return manuFacturer;
    }

    public void setManuFacturer(String manuFacturer) {
        this.manuFacturer = manuFacturer;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Long getPrice() {
        return price;
    }

    public void setPrice(Long price) {
        this.price = price;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public LocalDateTime getSubmittingDate() {
        return submittingDate;
    }

    public void setSubmittingDate(LocalDateTime submittingDate) {
        this.submittingDate = submittingDate;
    }

    public LocalDateTime getValidatyStartDate() {
        return validatyStartDate;
    }

    public void setValidatyStartDate(LocalDateTime validatyStartDate) {
        this.validatyStartDate = validatyStartDate;
    }

    public LocalDateTime getValidatyEndDate() {
        return validatyEndDate;
    }

    public void setValidatyEndDate(LocalDateTime validatyEndDate) {
        this.validatyEndDate = validatyEndDate;
    }

    public List<Attachment> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<Attachment> attachmentList) {
        this.attachmentList = attachmentList;
    }
}
