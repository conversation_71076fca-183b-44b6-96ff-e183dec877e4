# XXL-JOB配置
xxl.job.admin.addresses=https://xxljob-test.menggejk.com/admin
xxl.job.accessToken=
xxl.job.executor.appname=meerkat-second-party
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=9999
xxl.job.executor.logpath=/opt/www/logs/meerkat-second-party/xxl-job/job
xxl.job.executor.logretentiondays=30

# JTA 事务超时时间 (10分钟)
spring.jta.atomikos.properties.default-jta-timeout=600000

# 时空ERP Oracle配置
spring.datasource.skmerp.url=******************************************
spring.datasource.skmerp.driver-class-name=oracle.jdbc.driver.OracleDriver
spring.datasource.skmerp.username=skzyadmin
spring.datasource.skmerp.password=hln1224an
spring.datasource.skmerp.login-timeout=60
spring.datasource.skmerp.max-idle-timeout=60
spring.datasource.skmerp.max-pool-size=20
spring.datasource.skmerp.connection-test-query=SELECT 1 FROM DUAL

# 圣辰erp
# 圣辰无测试环境数据库
spring.datasource.shengchen.url=
spring.datasource.shengchen.driver-class-name=oracle.jdbc.driver.OracleDriver
spring.datasource.shengchen.username=
spring.datasource.shengchen.password=
spring.datasource.shengchen.login-timeout=60
spring.datasource.shengchen.max-idle-timeout=60
spring.datasource.shengchen.max-pool-size=20
spring.datasource.shengchen.connection-test-query=SELECT 1 FROM DUAL

# 报表库
spring.datasource.report.url=**********************************************************************************************
spring.datasource.report.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.report.username=mg_data_report
spring.datasource.report.password=g6Mi3mYK6yAF
spring.datasource.report.login-timeout=60
spring.datasource.report.max-idle-timeout=60
spring.datasource.report.max-pool-size=20
spring.datasource.report.connection-test-query=SELECT 1

# 主业务读库 meerkat
spring.datasource.meerkat.url=*****************************************************************************************************
spring.datasource.meerkat.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.meerkat.username=root
spring.datasource.meerkat.password=test1234!@#$
spring.datasource.meerkat.login-timeout=60
spring.datasource.meerkat.max-idle-timeout=60
spring.datasource.meerkat.max-pool-size=20
spring.datasource.meerkat.connection-test-query=SELECT 1

# 腾讯云配置
tencent.cos.enable=true
tencent.cos.secretId=AKIDYx6zb0JQK0TiiT4SbWFk15wYw5ze4FCv
tencent.cos.secretKey=qdHjy0pRovmPnvnOjtpY2HFxSoxsZ2HI
tencent.cos.region=ap-shanghai
tencent.cos.bucketName=meerkat-test-1307863402
tencent.cos.domain=https://meerkat-test-1307863402.cos.ap-shanghai.myqcloud.com
tencent.cos.private.bucketName=meerkat-private-1307863402
tencent.cos.private.domain=https://meerkat-private-1307863402.cos.ap-shanghai.myqcloud.com

# Redis配置
spring.redis.database=2
spring.redis.host=***********
spring.redis.port=6379
spring.redis.password=Mgjk123456!
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=-1ms
spring.redis.lettuce.pool.max-idle=5
spring.redis.lettuce.pool.min-idle=0
redis.expire.common=7200
redis.expire.authCode=7200

# 每刻云请求APIHosts (每刻报销二方系统）
maycur.api=https://ng.maycur.com

# appCode & appSecret 根据appCode和appSecret生成secret请求每刻
maycur.appCode=AP38CSWE68H7I7
maycur.appSecret=m9oE3WrwF6oX46DWlH2Z

# 飞书通知
feishu.robot.vaccinesCostStatisticsWebHook=https://open.feishu.cn/open-apis/bot/v2/hook/1ff9f2b8-df4e-4f3b-8ddb-3bf65f434b0b
