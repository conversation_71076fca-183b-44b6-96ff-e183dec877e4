import Vue from 'vue'
import Router from 'vue-router'
// import HelloWorld from '@/components/HelloWorld'

Vue.use(Router)

const router = new Router({
  routes: [
    {
      path: '/',
      redirect: '/index'
    },
    {
      path: '/index',
      name: 'Index',
      component: () => import('@/components/Index.vue'),
      meta: { requiresAuth: true } // 需要登录
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/components/Login.vue')
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/components/Register.vue')
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      component: () => import('@/components/Dashboard.vue'),
      meta: { requiresAuth: true } // 需要登录
    },

  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const token = localStorage.getItem('token')

  // 如果路由需要认证
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!token) {
      // 没有token，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath } // 保存原来要访问的路径
      })
      return
    }



    next()
  } else {
    // 如果是登录页且已经有token，跳转到首页
    if (to.path === '/login' && token) {
      next('/index')
    } else {
      next()
    }
  }
})

export default router
