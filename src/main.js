// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import axios from 'axios'
import SockJS from 'sockjs-client'
import Stomp from 'stompjs'

Vue.use(ElementUI)

// 配置axios全局拦截器
axios.interceptors.request.use(
  config => {
    // 添加token到请求头（除了登录请求）
    const token = localStorage.getItem('token')
    if (token && config.url !== '/api/user/login') {
      config.headers['Authorization'] = token
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器处理错误
axios.interceptors.response.use(
  response => {
    return response
  },
  error => {
    // 避免重复处理401错误（如果组件已经处理了）
    if (error.response && error.response.status === 401) {
      // 检查是否已经在处理401错误
      if (!window._handling401) {
        window._handling401 = true;

        // token过期或无效，清除本地存储
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        localStorage.removeItem('username')
        localStorage.removeItem('userInfoCacheTime')

        // 延迟跳转，避免与组件内的处理冲突
        setTimeout(() => {
          if (router.currentRoute.path !== '/login') {
            router.push('/login')
          }
          window._handling401 = false;
        }, 100);
      }
    }
    return Promise.reject(error)
  }
)

Vue.config.productionTip = false

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  components: { App },
  template: '<App/>'
})
