#jwt
jwt.secret=6c617e2380074946b92065d54c15f7af
jwt.expiration=604800
jwt.tokenHead=meerkat
jwt.tokenHeader=Authorization

# 白名单路径配置 - 支持多种配置方式
# 方式1：逗号分隔（推荐）
secure.ignored.urls=/isOk,/**/login,/swagger**/**,/v2/**,/doc.html,/ws/**,/static/**,/test,/api/test,/chat-page,/chat-test**


# qiniuyun
qiniu.access-key=3HR8bAdou0QgziVmaYAzKR2FhKOFEpCYH4NBwIvH
qiniu.secret-key=HyaayyKhIX5tFdTKmba7BFTfQjvN7BbzvcwtowlN
qiniu.bucket-name=0518sg
qiniu.domain=qny.jdb1109.xyz
qiniu.enable=false
#minio
minio.endpoint=http://**************:9000
minio.access-key=jkBlVcZ60GyKQL76FnRY
minio.secret-key=1UNI5DqslAwLe2aon4d0sfqGm8AJ1omDpXtQLfQO
minio.bucket-name=test
minio.enable=true

#server.port=8081

# 动态切换存储服务
storage.service=qiNiuServiceImpl

# 设置单个文件的最大上传大小（例如 10MB）
spring.servlet.multipart.max-file-size=10MB

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=*******************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456

mybatis.mapper-locations=classpath*:com/yqs/**/mapper/*.xml
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

pagehelper.helper-dialect=mysql

spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.database=0
spring.redis.jedis.pool.max-active=100
spring.redis.jedis.pool.max-idle=10
spring.redis.jedis.pool.min-idle=1
spring.redis.jedis.pool.max-wait=10000ms
spring.redis.timeout=10000ms