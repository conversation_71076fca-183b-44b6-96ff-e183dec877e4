package com.yqs.springbootminio.mapper;

import com.yqs.springbootminio.dao.ChatRoomDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 聊天室表数据库访问层接口
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Mapper
public interface ChatRoomMapper {

    /**
     * 查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ChatRoomDO selectById(Long id);

    /**
     * 根据房间编码查询
     *
     * @param roomCode 房间编码
     * @return 实例对象
     */
    ChatRoomDO selectByRoomCode(String roomCode);

    /**
     * 根据用户ID查询聊天室列表
     *
     * @param userId 用户ID
     * @return 聊天室列表
     */
    List<ChatRoomDO> selectByUserId(Long userId);

    /**
     * 根据客服ID查询聊天室列表
     *
     * @param customerServiceId 客服ID
     * @return 聊天室列表
     */
    List<ChatRoomDO> selectByCustomerServiceId(Long customerServiceId);

    /**
     * 查询用户的聊天室
     *
     * @param userId 用户ID
     * @return 聊天室
     */
    ChatRoomDO selectExitRoomByUserId(Long userId);

    /**
     * 新增数据
     *
     * @param chatRoomDO 实例对象
     * @return 影响行数
     */
    int insert(ChatRoomDO chatRoomDO);

    /**
     * 修改数据
     *
     * @param chatRoomDO 实例对象
     * @return 影响行数
     */
    int update(ChatRoomDO chatRoomDO);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 更新聊天室状态
     *
     * @param id     聊天室ID
     * @param status 状态
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 分配客服
     *
     * @param id                聊天室ID
     * @param customerServiceId 客服ID
     * @return 影响行数
     */
    int assignCustomerService(@Param("id") Long id, @Param("customerServiceId") Long customerServiceId);

    /**
     * 查询等待客服的聊天室列表
     *
     * @return 等待客服的聊天室列表
     */
    List<ChatRoomDO> selectPendingRooms();

    /**
     * 通过指定列查询指定行数据
     *
     * @param offset 查询起始位置
     * @param size  查询条数
     * @return 对象列表
     */
    List<ChatRoomDO> queryAllByLimit(@Param("offset") int offset, @Param("size") int size);
}
