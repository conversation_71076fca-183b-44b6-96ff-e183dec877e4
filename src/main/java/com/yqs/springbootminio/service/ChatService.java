package com.yqs.springbootminio.service;

import com.yqs.springbootminio.model.ChatMessage;
import com.yqs.springbootminio.model.ChatRoom;

import java.util.List;
import java.util.Map;

/**
 * 聊天服务接口
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface ChatService {

    /**
     * 创建或获取聊天室
     *
     * @param userId 用户ID
     * @return 聊天室
     */
    ChatRoom createOrGetChatRoom(Long userId);

    /**
     * 发送消息
     *
     * @param roomId      聊天室ID
     * @param senderId    发送者ID
     * @param senderType  发送者类型
     * @param messageType 消息类型
     * @param content     消息内容
     * @return 消息
     */
    ChatMessage sendMessage(Long roomId, Long senderId, Integer senderType, Integer messageType, String content);

    /**
     * 获取聊天历史
     *
     * @param roomId 聊天室ID
     * @param page   页码
     * @param size   每页大小
     * @return 消息列表
     */
    List<ChatMessage> getChatHistory(Long roomId, Integer page, Integer size);

    /**
     * 获取用户聊天室列表
     *
     * @param userId 用户ID
     * @return 聊天室列表
     */
    List<ChatRoom> getUserChatRooms(Long userId);

    /**
     * 根据房间编码获取聊天室
     *
     * @param roomCode 房间编码
     * @return 聊天室
     */
    ChatRoom getChatRoomByCode(String roomCode);

    /**
     * 标记消息为已读
     *
     * @param roomId   聊天室ID
     * @param readerId 阅读者ID
     */
    void markMessagesAsRead(Long roomId, Long readerId);

    /**
     * 获取未读消息数量
     *
     * @param roomId   聊天室ID
     * @param readerId 阅读者ID
     * @return 未读消息数量
     */
    int getUnreadMessageCount(Long roomId, Long readerId);

    /**
     * 分配客服
     *
     * @param roomId            聊天室ID
     * @param customerServiceId 客服ID
     */
    void assignCustomerService(Long roomId, Long customerServiceId);

    /**
     * 自动分配客服
     *
     * @param roomId 聊天室ID
     * @return 是否分配成功
     */
    boolean autoAssignCustomerService(Long roomId);

    /**
     * 获取客服的聊天室列表
     *
     * @param customerServiceId 客服ID
     * @return 聊天室列表
     */
    List<ChatRoom> getCustomerServiceChatRooms(Long customerServiceId);

    /**
     * 获取等待客服的聊天室列表（包含最新消息和用户信息）
     *
     * @return 等待客服的聊天室列表
     */
    List<Map<String, Object>> getPendingChatRooms();

    /**
     * 结束聊天室（客服完成服务）
     *
     * @param roomId 聊天室ID
     */
    void endChatRoom(Long roomId);

    /**
     * 获取所有聊天室（分页）
     *
     * @param page 页码
     * @param size 每页大小
     * @return 聊天室列表
     */
    List<ChatRoom> getAllChatRooms(Integer page, Integer size);
}
