package com.meerkat.distributionapi.model;

import java.util.Date;

/**
 * 返回条件
 *
 * <AUTHOR>
 */
public class GoodsDistributionRelationValue {


    /**
     * 主键id
     */
    private Long id;

    /**
     * 商品中心id
     */
    private Long goodsId;

    /**
     * 商品价格
     */
    private Long goodsPrice;

    /**
     * 渠道商品名称
     */
    private String chorgaGoodsName;

    /**
     * 渠道划线价格
     */
    private Long chorgaScribingPrice;

    /**
     * 渠道供货价格
     */
    private Long chorgaSupplyPrice;

    /**
     * 渠道商品价格
     */
    private Long chorgaGoodsPrice;

    /**
     * 渠道/机构id
     */
    private Long chorgaId;

    /**
     * 渠道/机构 商品编码
     */
    private String chorgaGoodsCode;

    /**
     * 类型 0渠道1机构
     */
    private int chorgaType;

    /**
     * 上架状态：0->未上架；1->已上架
     */
    private int saleable;

    /**
     * 删除状态：0->未删除；1->已删除
     */
    private int isDeleted;

    private Date gmtCreated;

    /**
     * 更新时间
     */
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "TbGoodsDistributionRelationVo{" + "id=" + id + ", goodsId=" + goodsId + ", goodsPrice=" + goodsPrice + ", chorgaGoodsName='" + chorgaGoodsName + '\'' + ", chorgaScribingPrice=" + chorgaScribingPrice + ", chorgaSupplyPrice=" + chorgaSupplyPrice + ", chorgaGoodsPrice=" + chorgaGoodsPrice + ", chorgaId=" + chorgaId + ", chorgaGoodsCode='" + chorgaGoodsCode + '\'' + ", chorgaType=" + chorgaType + ", saleable=" + saleable + ", isDeleted=" + isDeleted + ", gmtCreated=" + gmtCreated + ", gmtModified=" + gmtModified + '}';
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public Long getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(Long goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    public String getChorgaGoodsName() {
        return chorgaGoodsName;
    }

    public void setChorgaGoodsName(String chorgaGoodsName) {
        this.chorgaGoodsName = chorgaGoodsName;
    }

    public Long getChorgaScribingPrice() {
        return chorgaScribingPrice;
    }

    public void setChorgaScribingPrice(Long chorgaScribingPrice) {
        this.chorgaScribingPrice = chorgaScribingPrice;
    }

    public Long getChorgaSupplyPrice() {
        return chorgaSupplyPrice;
    }

    public void setChorgaSupplyPrice(Long chorgaSupplyPrice) {
        this.chorgaSupplyPrice = chorgaSupplyPrice;
    }

    public Long getChorgaGoodsPrice() {
        return chorgaGoodsPrice;
    }

    public void setChorgaGoodsPrice(Long chorgaGoodsPrice) {
        this.chorgaGoodsPrice = chorgaGoodsPrice;
    }

    public Long getChorgaId() {
        return chorgaId;
    }

    public void setChorgaId(Long chorgaId) {
        this.chorgaId = chorgaId;
    }

    public String getChorgaGoodsCode() {
        return chorgaGoodsCode;
    }

    public void setChorgaGoodsCode(String chorgaGoodsCode) {
        this.chorgaGoodsCode = chorgaGoodsCode;
    }

    public int getChorgaType() {
        return chorgaType;
    }

    public void setChorgaType(int chorgaType) {
        this.chorgaType = chorgaType;
    }

    public int getSaleable() {
        return saleable;
    }

    public void setSaleable(int saleable) {
        this.saleable = saleable;
    }

    public int getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(int isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

}
