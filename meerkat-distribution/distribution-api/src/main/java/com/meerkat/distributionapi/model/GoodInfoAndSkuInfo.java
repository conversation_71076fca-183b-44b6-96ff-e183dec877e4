package com.meerkat.distributionapi.model;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 传输商品和分销表信息
 * @date 2022/4/14 14:52
 */
public class GoodInfoAndSkuInfo {
    private DistributionRelationValue distributionRelationVo;
    private GoodsDistributionRelationValue goodsDistributionRelationVo;

    private String identity;
    public DistributionRelationValue getDistributionRelationVo() {
        return distributionRelationVo;
    }

    public void setDistributionRelationVo(DistributionRelationValue distributionRelationVo) {
        this.distributionRelationVo = distributionRelationVo;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public GoodsDistributionRelationValue getGoodsDistributionRelationVo() {
        return goodsDistributionRelationVo;
    }

    public void setGoodsDistributionRelationVo(GoodsDistributionRelationValue goodsDistributionRelationVo) {
        this.goodsDistributionRelationVo = goodsDistributionRelationVo;
    }

    public static class IdentityUtil{
        public static String generate(String skuCode,Long chorgaId) {
            return skuCode + "_" + chorgaId;
        }
    }

}
