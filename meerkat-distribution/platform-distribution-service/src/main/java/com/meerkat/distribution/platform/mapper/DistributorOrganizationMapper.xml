<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.distribution.platform.mapper.DistributorOrganizationMapper">
    <resultMap id="BaseResultMap" type="com.meerkat.distribution.platform.mapper.dataobj.DistributorOrganizationDO" >
        <result column="id" property="id" />
        <result column="distributor_id" property="distributorId" />
        <result column="organization_id" property="organizationId" />
        <result column="env" property="env" />
        <result column="profit_type" property="profitType" />
        <result column="profit_percentage" property="profitPercentage" />
        <result column="distribution_goods_id" property="distributionGoodsId" />
        <result column="url" property="url" />
        <result column="url_param" property="urlParam" />
        <result column="goods_page_ids" property="goodsPageIds" />
        <result column="operator_id" property="operatorId" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <sql id="base_column_list">
        `id`,
        `distributor_id`,
        `organization_id`,
        `env`,
        `profit_type`,
        `profit_percentage`,
        `distribution_goods_id`,
        `url`,
        `url_param`,
        `goods_page_ids`,
        `operator_id`,
        `is_deleted`,
        `gmt_created`,
        `gmt_modified`
    </sql>

    <sql id="insert_column_list">
        `distributor_id`,
        `organization_id`,
        `env`,
        `profit_type`,
        `profit_percentage`,
        `distribution_goods_id`,
        `url`,
        `url_param`,
        `goods_page_ids`,
        `operator_id`
    </sql>

    <insert id="insert">
        INSERT INTO tb_distributor_organization (
        <include refid="insert_column_list"/>
        ) VALUES (
        #{distributorId},
        #{organizationId},
        #{env},
        #{profitType},
        #{distributionGoodsId},
        #{profitPercentage},
        #{url},
        #{urlParam},
        #{goodsPageIds},
        #{operatorId}
        )
    </insert>

    <update id="batchInsert">
        INSERT INTO tb_distributor_organization (
        <include refid="insert_column_list"/>
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.distributorId},
            #{item.organizationId},
            #{item.env},
            #{item.profitType},
            #{item.profitPercentage},
            #{item.distributionGoodsId},
            #{item.url},
            #{item.urlParam},
            #{item.goodsPageIds},
            #{item.operatorId}
            )
        </foreach>
    </update>

    <update id="deleteById">
        UPDATE tb_distributor_organization SET is_deleted = 1 WHERE id = #{id}
    </update>

    <select id="list" resultMap="BaseResultMap">
        SELECT <include refid="base_column_list"/>
        FROM tb_distributor_organization
        WHERE is_deleted = 0
        <if test="null != distributorId">
            AND distributor_id = #{distributorId}
        </if>
        <if test="null != organizationId">
            AND organization_id = #{organizationId}
        </if>
        <if test="null != env">
            AND env = #{env}
        </if>
        <if test="null != profitType">
            AND profit_type = #{profitType}
        </if>
        <if test="null != url and '' != url">
            AND url = #{url}
        </if>
        <if test="null != urlParam and '' != urlParam">
            AND url_param = #{urlParam}
        </if>
        <if test="null != goodsPageIds and '' != goodsPageIds">
            AND goods_page_ids = #{goodsPageIds}
        </if>
        <if test="null != operatorId">
            AND operator_id = #{operatorId}
        </if>
    </select>

    <select id="listByDistributorId"
            resultMap="BaseResultMap">
        select <include refid="base_column_list"/>
        from tb_distributor_organization
        where distributor_id = #{distributorId}
        AND is_deleted = 0
    </select>

    <select id="listByDistributorIdAndOrganizationId"
            resultMap="BaseResultMap">
        SELECT <include refid="base_column_list"/>
        FROM tb_distributor_organization
        WHERE distributor_id = #{distributorId}
        AND organization_id = #{organizationId}
        AND is_deleted = 0
    </select>

    <select id="getById"
            resultMap="BaseResultMap">
        select <include refid="base_column_list"/>
        from tb_distributor_organization
        where id = #{id}
          AND is_deleted = 0
    </select>
</mapper>