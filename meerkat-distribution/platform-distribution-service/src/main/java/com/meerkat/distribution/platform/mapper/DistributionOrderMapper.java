package com.meerkat.distribution.platform.mapper;

import com.meerkat.distribution.platform.mapper.dataobj.DistributionOrderDO;
import com.meerkat.distribution.platform.mapper.dataobj.param.DistributionOrderQueryDO;
import com.meerkat.distribution.platform.model.DistributionOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/11 4:49 下午
 */
@Mapper
public interface DistributionOrderMapper {

    int insert(DistributionOrderDO distributionOrderDO);

    int update(DistributionOrderDO distributionOrderDO);

    int batchUpdate(@Param("list") List<DistributionOrderDO> list);

    DistributionOrderDO getByOrderNum(@Param("orderNum")String orderNum);

    List<DistributionOrderDO> listByQuery(DistributionOrderQueryDO distributionOrderQueryDO);
}
