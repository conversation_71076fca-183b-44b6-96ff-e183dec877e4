package com.meerkat.distribution.platform.dto.param;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 分销订单保存参数
 * @date 2022/5/12 2:58 下午
 */
public class DistributionOrderSaveParamDTO {

    /**
     * 分销人Id
     */
    private Long distributiorId;

    /**
     * 购买用户userId
     */
    private Long userId;

    /**
     * 机构id
     */
    private Long organizationId;

    /**
     * 分销人分销码
     */
    private String distributorCode;

    /**
     * 订单编号
     */
    private String orderNum;

    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 订单创建时间
     */
    private LocalDateTime orderCreated;

    /**
     * 实付金额
     */
    private Long payAmount;

    public Long getDistributiorId() {
        return distributiorId;
    }

    public void setDistributiorId(Long distributiorId) {
        this.distributiorId = distributiorId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public String getDistributorCode() {
        return distributorCode;
    }

    public void setDistributorCode(String distributorCode) {
        this.distributorCode = distributorCode;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }

    public LocalDateTime getOrderCreated() {
        return orderCreated;
    }

    public void setOrderCreated(LocalDateTime orderCreated) {
        this.orderCreated = orderCreated;
    }

    public Long getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(Long payAmount) {
        this.payAmount = payAmount;
    }
}
