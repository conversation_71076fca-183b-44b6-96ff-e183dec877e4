package com.meerkat.distribution.platform.dto.result;

import com.meerkat.distribution.platform.model.DistributionGoods;
import com.meerkat.distribution.platform.model.Distributor;

import java.util.List;

/**
 * 分销信息，对应前端Local缓存中信息
 * <AUTHOR>
 * @description
 * @date 2022/7/4 18:33
 */
public class DistributionInfo {

    private Distributor distributor;

    private List<DistributionGoods> distributionGoodsList;

    public DistributionInfo(Distributor distributor) {
        this.distributor = distributor;
    }

    public DistributionInfo(Distributor distributor, List<DistributionGoods> distributionGoodsList) {
        this.distributor = distributor;
        this.distributionGoodsList = distributionGoodsList;
    }

    public Distributor getDistributor() {
        return distributor;
    }

    public void setDistributor(Distributor distributor) {
        this.distributor = distributor;
    }

    public List<DistributionGoods> getDistributionGoodsList() {
        return distributionGoodsList;
    }

    public void setDistributionGoodsList(List<DistributionGoods> distributionGoodsList) {
        this.distributionGoodsList = distributionGoodsList;
    }
}
