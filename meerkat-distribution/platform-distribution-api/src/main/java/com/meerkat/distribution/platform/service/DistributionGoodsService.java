package com.meerkat.distribution.platform.service;

import com.meerkat.distribution.platform.dto.param.DistributionGoodsQueryParamDTO;
import com.meerkat.distribution.platform.dto.param.DistributionGoodsSaveParamDTO;
import com.meerkat.distribution.platform.model.DistributionGoods;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/5 18:18
 */
public interface DistributionGoodsService {

    Long saveDistributionGoods(DistributionGoodsSaveParamDTO distributionGoodsSaveParamDTO);

    /**
     * <AUTHOR>
     * @date 2022/7/5 20:14
     * @param list
     * @return java.util.Map<java.lang.Long,java.lang.Long> K: goodsId, V: distributionGoodsId
     */
    Map<Long, Long> saveBatchDistributionGoods(List<DistributionGoodsSaveParamDTO> list);

    List<DistributionGoods> listDistributionGoods(DistributionGoodsQueryParamDTO distributionGoodsQueryParamDTO);

    DistributionGoods getDistributionGoodsById(Long id);

    int deleteDistributionGoods(Long id);
}
