package com.meerkat.trade.dto;

public class AcctConf {
    /**
     * id
     */
    private Long id;

    /**
     * 商户号
     */
    private String mchid;

    /**
     * 请求网关地址
     */
    private String getawayHost;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 私钥文件路径
     */
    private String privateKeyPath;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 公钥路径
     */
    private String publicKeyPath;

    /**
     * 应用证书路径
     */
    private String appPrivateCertPath;

    /**
     * 应用证书路径
     */
    private String appPublicCertPath;

    /**
     * 平台证书路径
     */
    private String channelCertPath;

    /**
     * 平台根证书路径
     */
    private String channelRootCertPath;

    /**
     * 签名算法（主要用于发送请求对请求体进行签名计算），对方收到后利用签名和请求体生成签名做对比
     */
    private String signType;

    /**
     * 加密密钥
     */
    private String encryptKey;

    /**
     * 加密算法
     */
    private String encryptType;

    /**
     * 支付渠道，0微信、1支付宝
     */
    private int payChannel;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMchid() {
        return mchid;
    }

    public void setMchid(String mchid) {
        this.mchid = mchid;
    }

    public String getGetawayHost() {
        return getawayHost;
    }

    public void setGetawayHost(String getawayHost) {
        this.getawayHost = getawayHost;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getPrivateKeyPath() {
        return privateKeyPath;
    }

    public void setPrivateKeyPath(String privateKeyPath) {
        this.privateKeyPath = privateKeyPath;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getPublicKeyPath() {
        return publicKeyPath;
    }

    public void setPublicKeyPath(String publicKeyPath) {
        this.publicKeyPath = publicKeyPath;
    }

    public String getAppPrivateCertPath() {
        return appPrivateCertPath;
    }

    public void setAppPrivateCertPath(String appPrivateCertPath) {
        this.appPrivateCertPath = appPrivateCertPath;
    }

    public String getAppPublicCertPath() {
        return appPublicCertPath;
    }

    public void setAppPublicCertPath(String appPublicCertPath) {
        this.appPublicCertPath = appPublicCertPath;
    }

    public String getChannelCertPath() {
        return channelCertPath;
    }

    public void setChannelCertPath(String channelCertPath) {
        this.channelCertPath = channelCertPath;
    }

    public String getChannelRootCertPath() {
        return channelRootCertPath;
    }

    public void setChannelRootCertPath(String channelRootCertPath) {
        this.channelRootCertPath = channelRootCertPath;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getEncryptKey() {
        return encryptKey;
    }

    public void setEncryptKey(String encryptKey) {
        this.encryptKey = encryptKey;
    }

    public String getEncryptType() {
        return encryptType;
    }

    public void setEncryptType(String encryptType) {
        this.encryptType = encryptType;
    }

    public int getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(int payChannel) {
        this.payChannel = payChannel;
    }
}
