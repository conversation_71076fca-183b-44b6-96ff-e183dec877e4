package com.meerkat.trade.config.service;

import com.meerkat.trade.config.dto.param.AddPaymentConfParam;
import com.meerkat.trade.config.dto.param.UpdatePaymentConfParam;
import com.meerkat.trade.dto.PayProduct;
import com.meerkat.trade.dto.BusinessPaymentConf;
import com.meerkat.trade.dto.PaymentAcct;
import com.meerkat.trade.enums.PayEnvEnum;
import com.meerkat.trade.pay.enums.PayChannelEnum;
import com.meerkat.trade.pay.enums.PayProductEnum;

import java.util.List;

/**
 * @description:
 * @author: wangjiaw<PERSON>
 * @date: 2022/1/11 3:44 下午
 */
public interface BusinessPaymentConfService {

    /**
     * 获取支持支付产品列表
     *
     * @param chorgaId   站点id，内容为机构id或渠道id
     * @param chorgaType 站点类型，1：机构、2:渠道
     * @return 支付产品列表
     */
    List<PayProduct> acctList(Long chorgaId, Integer chorgaType);

    /**
     * 根据渠道获取config信息
     * @param payChannel 支付渠道
     * @return 对应支付渠道下所有商户信息对应下的支付配置信息
     */
    List<BusinessPaymentConf> listChannelConf(Integer payChannel);

    /**
     * 获取全部config信息
     *
     * @return 对应支付渠道下所有商户信息对应下的支付配置信息
     */
    List<BusinessPaymentConf> listAllConf();


    /**
     * 获取商户入驻信息及其商户下所有账户、配置信息
     *
     * @param chorgaId   商户入驻id，分为机构/渠道
     * @param chorgaType 商户类型，1：机构、2:渠道
     * @return
     */
    BusinessPaymentConf getConf(Long chorgaId, Integer chorgaType);

    /**
     * 获取商户支付appid
     *
     * @param chorgaId   商户入驻id，分为机构/渠道
     * @param chorgaType 商户类型，1：机构、2:渠道
     * @param payChannel 支付渠道
     * @param payEnv     支付环境
     * @param payProduct 支付产品
     * @return java.lang.String
     * <AUTHOR>
     */
    String getAppId(Long chorgaId, Integer chorgaType, PayChannelEnum payChannel, PayEnvEnum payEnv, PayProductEnum payProduct);

    /**
     * 提供商户下具体支付渠道支付产品的账户及配置信息
     *
     * @param chorgaId   商户入驻id，分为机构/渠道
     * @param chorgaType 商户类型，1：机构、2:渠道
     * @param payChannel 支付渠道
     * @param payEnv     支付环境
     * @param payProduct 支付产品
     * @return
     */
    PaymentAcct getAcct(Long chorgaId, Integer chorgaType, Integer payChannel, Integer payEnv, String payProduct);


    /**
     * 添加支付配置
     * <AUTHOR>
     * @date 2022/4/19 3:30 下午
     * @param param
     * @return java.lang.Boolean
     */
    Boolean addPaymentConf(AddPaymentConfParam param);


    /**
     * 更新支付配置
     * <AUTHOR>
     * @date 2022/4/20 5:27 下午
     * @param param
     * @return java.lang.Boolean
     */
    Boolean updatePaymentConf(UpdatePaymentConfParam param);
}
