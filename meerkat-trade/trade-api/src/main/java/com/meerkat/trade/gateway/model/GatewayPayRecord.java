package com.meerkat.trade.gateway.model;

/**
 * @description:
 * @author: wa<PERSON><PERSON><PERSON><PERSON>
 * @date: 2022/2/11 4:55 下午
 */
public class GatewayPayRecord {

    /**
     * id
     */
    private Long id;

    /**
     * 内部流水
     */
    private String sn;

    /**
     * originally sn
     * 原内部流水（退款使用)
     */
    private String oriSn;

    /**
     * 外部流水（由第三方支付渠道返回）
     */
    private String tradeNo;

    /**
     * 支付金额，存储单位统一为分，暂时只考虑人民币的情况
     */
    private Long amount;

    /**
     * 支付渠道用户id，付款者/购买者id
     */
    private String buyerId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品描述
     */
    private String goodsDesc;

    /**
     * com.meerkat.trade.pay.enums.PayChannelEnum
     * 支付渠道，0：微信、1：支付宝
     */
    private Integer payChannel;

    /**
     * 流水发生应用appid
     */
    private String appid;

    /**
     * 支付渠道动账商户号，同时也是支付配置的唯一标识
     */
    private String mchid;

    /**
     * com.meerkat.trade.pay.enums.PaymentCodeEnum
     * 支付代码，0：支付、1：退款、2：提现
     */
    private Integer paymentCode;

    /**
     * 网关流水状态，1：支付中、2：支付成功、3：支付失败、4：支付关闭
     */
    private Integer status;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 业务代码
     */
    private String subCode;

    /**
     * 业务信息
     */
    private String subMsg;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getOriSn() {
        return oriSn;
    }

    public void setOriSn(String oriSn) {
        this.oriSn = oriSn;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsDesc() {
        return goodsDesc;
    }

    public void setGoodsDesc(String goodsDesc) {
        this.goodsDesc = goodsDesc;
    }

    public Integer getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(Integer payChannel) {
        this.payChannel = payChannel;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getMchid() {
        return mchid;
    }

    public void setMchid(String mchid) {
        this.mchid = mchid;
    }

    public Integer getPaymentCode() {
        return paymentCode;
    }

    public void setPaymentCode(Integer paymentCode) {
        this.paymentCode = paymentCode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    public String getSubMsg() {
        return subMsg;
    }

    public void setSubMsg(String subMsg) {
        this.subMsg = subMsg;
    }
}
