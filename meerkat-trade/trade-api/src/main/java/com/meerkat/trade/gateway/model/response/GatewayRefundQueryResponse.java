package com.meerkat.trade.gateway.model.response;

/**
 * 退款查询返回对象
 *
 * <AUTHOR>
 * @date 2021/10/20 17:23
 */
public class GatewayRefundQueryResponse extends AbstractBaseResponse {

    private static final long serialVersionUID = 6634015589977595094L;
    /**
     * 订单总金额(分)
     */
    private Long totalAmount;

    /**
     * 退款金额(分)
     */
    private Long refundAmount;

    /**
     * 订单状态
     */
    private String tradeStatus;

    /*
     * 渠道退款订单号
     */
    private String tradeNo;

    /**
     * 退款时间
     */
    private String refundTime;

    public Long getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Long totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Long getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Long refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(String tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(String refundTime) {
        this.refundTime = refundTime;
    }
}
