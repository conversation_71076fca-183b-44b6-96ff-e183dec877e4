<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.trade.refund.mapper.TradeRefundRecordMapper">

    <resultMap id="BaseResultMap" type="com.meerkat.trade.refund.mapper.dataobj.TradeRefundRecordDO">
        <result column="id" property="id"/>
        <result column="sn" property="sn"/>
        <result column="trade_order_num" property="tradeOrderNum"/>
        <result column="ref_order_num" property="refOrderNum"/>
        <result column="ref_pay_sn" property="refPaySn"/>
        <result column="pay_type" property="payType"/>
        <result column="pay_product" property="payProduct"/>
        <result column="refund_status" property="refundStatus"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="origin_pay_amount" property="originPayAmount"/>
        <result column="trade_no" property="tradeNo"/>
        <result column="remark" property="remark"/>
        <result column="payment_type" property="paymentType"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
                sn,
                trade_order_num,
                ref_order_num,
                ref_pay_sn,
                pay_type,
                pay_product,
                refund_status,
                refund_amount,
                origin_pay_amount,
                trade_no,
                remark,
                payment_type,
                gmt_created,
                gmt_modified,
                is_deleted
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.meerkat.trade.refund.mapper.dataobj.TradeRefundRecordDO">
        INSERT INTO tb_trade_refund_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != sn and '' != sn">
                sn,
            </if>
            <if test="null != tradeOrderNum and '' != tradeOrderNum">
                trade_order_num,
            </if>
            <if test="null != refOrderNum and '' != refOrderNum">
                ref_order_num,
            </if>
            <if test="null != refPaySn and '' != refPaySn">
                ref_pay_sn,
            </if>
            <if test="null != payProduct and '' != payProduct">
                pay_product,
                pay_type,
            </if>
            <if test="null != refundStatus">
                refund_status,
            </if>
            <if test="null != refundAmount">
                refund_amount,
            </if>
            <if test="null != originPayAmount">
                origin_pay_amount,
            </if>
            <if test="null != tradeNo and '' != tradeNo">
                trade_no,
            </if>
            <if test="null != remark and '' != remark">
                remark,
            </if>
            <if test="null != paymentType and '' != paymentType">
                payment_type
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != sn and '' != sn">
                #{sn},
            </if>
            <if test="null != tradeOrderNum and '' != tradeOrderNum">
                #{tradeOrderNum},
            </if>
            <if test="null != refOrderNum and '' != refOrderNum">
                #{refOrderNum},
            </if>
            <if test="null != refPaySn and '' != refPaySn">
                #{refPaySn},
            </if>
            <if test="null != payProduct and '' != payProduct">
                #{payProduct},
                #{payType},
            </if>
            <if test="null != refundStatus">
                #{refundStatus},
            </if>
            <if test="null != refundAmount">
                #{refundAmount},
            </if>
            <if test="null != originPayAmount">
                #{originPayAmount},
            </if>
            <if test="null != tradeNo and '' != tradeNo">
                #{tradeNo},
            </if>
            <if test="null != remark and '' != remark">
                #{remark},
            </if>
            <if test="null != paymentType and '' != paymentType">
                #{paymentType}
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.meerkat.trade.refund.mapper.dataobj.TradeRefundRecordDO">
        UPDATE tb_trade_refund_record
        <set>
            <if test="null != refundStatus">refund_status = #{refundStatus},</if>
            <if test="null != refundAmount">refund_amount = #{refundAmount},</if>
            <if test="null != tradeNo and '' != tradeNo">trade_no = #{tradeNo},</if>
            <if test="null != remark and '' != remark">remark = #{remark},</if>
        </set>
        WHERE id = #{id}
    </update>
    <update id="updateRefundStatusBySn">
        UPDATE tb_trade_refund_record
        <set>
            <if test="null != refundStatus">refund_status = #{refundStatus},</if>
            <if test="null != tradeNo and '' != tradeNo">trade_no = #{tradeNo},</if>
            <if test="null != remark and '' != remark">remark = #{remark},</if>
        </set>
        WHERE sn = #{sn} and refund_status = #{oldRefundStatus}
    </update>


    <select id="loadByOrderNum" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_trade_refund_record
        WHERE ref_order_num = #{refOrderNum}
    </select>
    <select id="loadBySn" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_trade_refund_record
        WHERE sn = #{sn}
    </select>
    <select id="selectRefundingRecord" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_trade_refund_record
        WHERE refund_status=#{refundStatus} and is_deleted = 0
        order by id desc
    </select>


</mapper>