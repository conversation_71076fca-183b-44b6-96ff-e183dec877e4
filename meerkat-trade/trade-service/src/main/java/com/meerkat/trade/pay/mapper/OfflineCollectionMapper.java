package com.meerkat.trade.pay.mapper;

import com.meerkat.trade.pay.mapper.dataobj.OfflineCollectionDO;
import com.meerkat.trade.pay.mapper.dataobj.param.OfflineCollectionQueryDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/6/20 09:55
 */
@Mapper
public interface OfflineCollectionMapper {

    List<OfflineCollectionDO> list(OfflineCollectionQueryDO tradeOrderQueryDO);

    List<OfflineCollectionDO> queryByIds(@Param("ids") List<String> ids);
}
