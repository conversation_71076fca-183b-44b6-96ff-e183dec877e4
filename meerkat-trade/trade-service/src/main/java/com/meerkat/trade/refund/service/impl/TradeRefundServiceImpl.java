package com.meerkat.trade.refund.service.impl;

import com.meerkat.trade.pay.enums.TradeStateEnum;
import com.meerkat.trade.refund.mapper.TradeRefundRecordMapper;
import com.meerkat.trade.refund.mapper.dataobj.TradeRefundRecordDO;
import com.meerkat.trade.refund.model.TradeRefundRecord;
import com.meerkat.trade.refund.service.impl.inner.TradeRefundRecordService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: pantaoling
 * @date: 2021/10/29
 */
@Service
public class TradeRefundServiceImpl implements TradeRefundRecordService {

    @Autowired
    private TradeRefundRecordMapper tradeRefundRecordMapper;

    @Override
    public List<TradeRefundRecord> listRecordByRefOrderNum(String refOrderNum) {
        List<TradeRefundRecordDO> tradeRefundRecordDoS = tradeRefundRecordMapper.loadByOrderNum(refOrderNum);
        return convertToModelList(tradeRefundRecordDoS);
    }

    @Override
    public TradeRefundRecord loadBySn(String sn) {
        TradeRefundRecordDO recordDO = tradeRefundRecordMapper.loadBySn(sn);
        return convertToModel(recordDO);
    }

    @Override
    public void saveRecord(TradeRefundRecord tradeRefundRecord) {
        TradeRefundRecordDO recordDO = convertToDo(tradeRefundRecord);
        tradeRefundRecordMapper.insert(recordDO);
    }

    @Override
    public boolean update(String sn,
                       Integer refundStatus,
                       Integer oldRefundStatus,
                       String remark,
                       String tradeNo) {
        return 1== tradeRefundRecordMapper.updateRefundStatusBySn(sn, refundStatus, oldRefundStatus, remark, tradeNo);
    }

    @Override
    public List<TradeRefundRecord> listRefundingRecord() {
        List<TradeRefundRecordDO> tradeRefundRecordDOS = tradeRefundRecordMapper.selectRefundingRecord(TradeStateEnum.DOING.getState());
        return convertToModelList(tradeRefundRecordDOS);
    }

    private List<TradeRefundRecord> convertToModelList(List<TradeRefundRecordDO> refundRecordDoS) {
        List<TradeRefundRecord> records = new ArrayList<>();
        if (CollectionUtils.isEmpty(refundRecordDoS)) {
            return records;
        }
        refundRecordDoS.forEach(tradeRefundRecordDO -> {
            records.add(convertToModel(tradeRefundRecordDO));
        });
        return records;
    }

    private TradeRefundRecord convertToModel(TradeRefundRecordDO recordDO) {
        TradeRefundRecord record = new TradeRefundRecord();
        if (recordDO == null) {
            return null;
        }
        BeanUtils.copyProperties(recordDO, record);
        return record;
    }


    private TradeRefundRecordDO convertToDo(TradeRefundRecord record) {
        if (record == null) {
            return null;
        }
        TradeRefundRecordDO recordDO = new TradeRefundRecordDO();
        BeanUtils.copyProperties(record, recordDO);
        return recordDO;
    }
}
