package com.meerkat.trade.pay.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Strings;
import com.meerkat.common.db.TransactionHelper;
import com.meerkat.common.enums.OperatorEnum;
import com.meerkat.common.enums.SystemEnum;
import com.meerkat.common.exception.BizException;
import com.meerkat.order.event.OrderPayEvent;
import com.meerkat.order.service.OrderFsmEngine;
import com.meerkat.trade.constants.TradeConstants;
import com.meerkat.trade.dto.Notify;
import com.meerkat.trade.dto.TradeOrderUpdateDTO;
import com.meerkat.trade.gateway.enums.GatewayStatusEnum;
import com.meerkat.trade.gateway.service.PaymentGatewayService;
import com.meerkat.trade.model.TradeOrder;
import com.meerkat.trade.pay.dto.*;
import com.meerkat.trade.pay.enums.PayExceptionEnum;
import com.meerkat.trade.pay.enums.TradeStateEnum;
import com.meerkat.trade.pay.model.TradePayRecord;
import com.meerkat.trade.pay.service.UnifyPayService;
import com.meerkat.trade.pay.service.handler.IContinuePayHandler;
import com.meerkat.trade.pay.service.handler.IPayHandler;
import com.meerkat.trade.pay.service.helper.FeishuHelper;
import com.meerkat.trade.pay.service.helper.PayRecordHelper;
import com.meerkat.trade.pay.service.inner.TradePayRecordService;
import com.meerkat.trade.service.TradeOrderService;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description: 通用支付服务
 * @author: pantaoling
 * @date: 2021/10/13
 */
@Service
public class UnifyPayServiceImpl implements UnifyPayService {

    private static final Logger LOG = LoggerFactory.getLogger(UnifyPayServiceImpl.class);

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private List<IPayHandler> payHandlers;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private TradeOrderService tradeOrderService;

    @Autowired
    private TradePayRecordService tradePayRecordService;

    @Autowired
    private List<IContinuePayHandler> continuePayhandlers;

    @Autowired
    private PayRecordHelper payRecordHelper;

    @Autowired
    private OrderFsmEngine orderFsmEngine;

    @Autowired
    private PaymentGatewayService paymentGatewayService;

    @Autowired
    private FeishuHelper feishuHelper;

    @Override
    public PayResponse pay(PayRequest request) {
        validate(request);

        TradeOrder existsTradeOrder = tradeOrderService.getTradeOrder(request.getOrderNum(), TradeConstants.TradeType.PAY);
        RLock rLock = null;
        try {
            //
            // 订单已经支付过，可能是继续支付、也可能是重复提交
            //
            if (existsTradeOrder != null) {
                // 如果上一次支付失败，则
                if (existsTradeOrder.getTradeStatus() == TradeStateEnum.DOING.getState()) {
                    return continuePay(existsTradeOrder, request);
                } else {
                    throw new BizException(PayExceptionEnum.TRADE_ORDER_STATE_ERROR);
                }
            } else {
                rLock = tryLockTradePay(request.getOrderNum());
                // 普通的支付页提交支付
                return generalPay(request);
            }
        } catch (Exception e) {
            LOG.error("支付失败 request:{}, exception:{}",
                    ToStringBuilder.reflectionToString(request), e);
            return buildFailPayResponse(e.getMessage());
        } finally {
            if (rLock != null && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    /**
     * <pre>
     * 继续支付 "一般支付" 可能选择了多种支付方式，余额、卡、支付宝(微信) 但是用户关闭了弹出的 支付宝(微信)支付弹框或页面，
     * 这时候订单列表和订单详情页提供"继续支付功能"
     *
     * 把原来的第三方支付记录状态修改为"取消" 然后创建新的支付记录
     *
     * @param existsTradeOrder
     * @param request
     * @return
     */
    private PayResponse continuePay(TradeOrder existsTradeOrder, PayRequest request) {
        // 提取继续支付处理器
        List<IContinuePayHandler> handlers = continuePayhandlers.stream().sorted(Comparator.comparing(IContinuePayHandler::sort)).collect(Collectors.toList());

        PayContext context = new PayContext(existsTradeOrder.getTradeOrderNum(), request, existsTradeOrder.getAmount());
        List<TradePayRecord> list = this.tradePayRecordService.listByTradeOrderNum(existsTradeOrder.getTradeOrderNum());
        context.setAllRecords(list);
        TransactionStatus transactionStatus = transactionHelper.beginTransaction();

        boolean useHandler = false;
        try {
            for (IContinuePayHandler handler : handlers) {
                if (handler.accept(context)) {
                    context = handler.process(context);
                    useHandler = true;
                }
            }

            if (!useHandler) {
                transactionHelper.rollback(transactionStatus);
                return buildFailPayResponse("未找到支付处理器");
            }

            boolean updateTradeOrder = false;
            TradeOrderUpdateDTO update = new TradeOrderUpdateDTO();
            update.setId(existsTradeOrder.getId());
            update.setOldTradeStatus(existsTradeOrder.getTradeStatus());
            if (!context.isNeedNextAction() && context.getPaying() == 0) {
                update.setNewTradeStatus(TradeStateEnum.SUCCESSFUL.getState());
                update.setNewSuccessAmount(existsTradeOrder.getSuccAmount() + context.getPaid());
                updateTradeOrder = tradeOrderService.updateTradeOrder(update);
            }

            if (!updateTradeOrder) {
                // 这个更新对于业务来说并不是那么重要 如果失败是否可以只记录日志而不影响业务
                LOG.warn("继续支付 更新交易订单支付方式失败 {}", request.getOrderNum());
            }

            transactionHelper.commit(transactionStatus);
            PayResponse payResponse = context.getPayResponse();
            if (payResponse == null || payResponse.isSuccess()) {
                return buildSuccessPayResponse(context);
            } else {
                return payResponse;
            }
        } catch (Exception e) {
            transactionHelper.rollback(transactionStatus);
            LOG.error(String.format("继续支付失败, 交易订单: %s, 支付请求: %s", ToStringBuilder.reflectionToString(existsTradeOrder),
                            ToStringBuilder.reflectionToString(request)),
                    e);
            return buildFailPayResponse(e.getMessage());
        }
    }

    /**
     * <pre>
     * 一般支付 订单创建、或者改项等情况跳转到支付页面后点击确认支付发起的支付申请
     *
     * @param request
     * @return
     */
    private PayResponse generalPay(PayRequest request) {

        List<IPayHandler> handlerList = payHandlers.stream().sorted(Comparator.comparing(IPayHandler::sort)).collect(Collectors.toList());

        TradeOrder tradeOrder = buildTradeOrder(request);
        PayContext context = new PayContext(tradeOrder.getTradeOrderNum(), request, tradeOrder.getAmount());

        TransactionStatus status = transactionHelper.beginTransaction();

        boolean useHandler = false;
        try {
            for (IPayHandler handler : handlerList) {
                if (handler.accept(context)) {
                    context = handler.process(context);
                    useHandler = true;
                    if (context.getPayResponse() != null && !context.getPayResponse().isSuccess()) {
                        break;
                    }
                }
            }

            if (!useHandler) {
                transactionHelper.rollback(status);
                return buildFailPayResponse("没找到支付处理器");
            }

            tradeOrder.setSuccAmount(context.getPaid());

            // 若还有下一步操作，或金额未完全支付，则视为进行中
            tradeOrder.setTradeStatus(
                    !context.isNeedNextAction() && Objects.equals(context.getPaid(), context.getCount())
                            ? TradeStateEnum.SUCCESSFUL.getState() : TradeStateEnum.DOING.getState()
            );

            boolean tradeOrderSave = tradeOrderService.saveTradeOrderWithTransaction(tradeOrder);
            if (tradeOrderSave) {
                transactionHelper.commit(status);
                PayResponse payResponse = context.getPayResponse();
                if (payResponse == null || payResponse.isDone()) {
                    return buildSuccessPayResponse(context);
                } else {
                    return payResponse;
                }
            } else {
                throw new BizException(PayExceptionEnum.TRADE_ORDER_SAVE_FAIL);
            }
        } catch (Exception e) {
            transactionHelper.rollback(status);
            LOG.error("一般支付失败, 支付请求: {}", JSONUtil.toJsonStr(request), e);
            return buildFailPayResponse(e.getMessage());
        }
    }

    private TradeOrder buildTradeOrder(PayRequest payRequest) {
        if (payRequest == null) {
            throw new BizException(PayExceptionEnum.ILLEGAL_ARGUMENT, "支付请求参数为空");
        }
        TradeOrder order = new TradeOrder();

        // 计算金额
        Long amount = 0L;
        for (PaymentDetail paymentDetail : payRequest.getPaymentDetails()) {
            amount += paymentDetail.getPayAmount();
        }

        order.setAmount(amount);
        order.setSuccAmount(0L);

        order.setRefOrderGoodsDesc(payRequest.getGoodsDesc());
        order.setRefOrderGoodsName(payRequest.getGoodsName());
        order.setRefOrderNum(payRequest.getOrderNum());
        order.setRefOrderNumVersion(payRequest.getOrderNumVersion());

        order.setTradeType(TradeConstants.TradeType.PAY);
        order.setTradeStatus(TradeStateEnum.CREATED.getState());

        String tradeOrderNum = tradeOrderService.genTradeOrderNum(order.getRefOrderNum());
        order.setTradeOrderNum(tradeOrderNum);

        order.setExtraCommonParam(payRequest.getExtraCommonParam());
        order.setChorgaId(payRequest.getChorgaId());
        order.setChorgaType(payRequest.getChorgaType());
        order.setIsOfflineCollection(payRequest.getIsOfflineCollection());
        order.setTradeSource(payRequest.getTradeSource());
        return order;
    }

    private PayResponse buildSuccessPayResponse(PayContext context) {
        PayResponse resp = new PayResponse();
        resp.setSuccess(true);
        PayRequest req = context.getPayRequest();

        boolean done = !context.isNeedNextAction() && Objects.equals(context.getPaid(), context.getCount());

        resp.setDone(done);
        resp.setRequireAmount(context.getCount());
        resp.setSuccessAmount(context.getPaid());
        resp.setPayAmount(context.getOnlinePaid());
        resp.setPayingAmount(context.getPaying());
        resp.setDiscountAmount(context.getDiscountAmountMap());

        resp.setNeedNextAction(context.isNeedNextAction());
        resp.setNextActionName(context.getNextAction());
        resp.setNextActionBizContext(context.getNextActionBizContext());

        resp.setRefOrderNum(req.getOrderNum());
        resp.setTradeOrderNum(context.getTradeOrderNum());
        resp.setPayScene(context.getPayScene());
        return resp;
    }

    private PayResponse buildFailPayResponse(String msg) {
        PayResponse resp = new PayResponse();
        resp.setSuccess(false);
        resp.setMsg(msg);
        return resp;
    }


    private void validate(PayRequest request) {
        List<PaymentDetail> paymentDetails = request.getPaymentDetails();
        if (CollectionUtils.isEmpty(paymentDetails)) {
            throw new BizException(PayExceptionEnum.ILLEGAL_ARGUMENT, "支付金额不能为空，或者不能小于等于0");
        }


        long amount = paymentDetails.stream().mapToLong(PaymentDetail::getPayAmount).sum();
        if (amount < 0) {
            throw new BizException(PayExceptionEnum.ILLEGAL_ARGUMENT, "支付金额不能小于等于0");
        }
        if (Strings.isNullOrEmpty(request.getOrderNum())) {
            throw new BizException(PayExceptionEnum.ILLEGAL_ARGUMENT, "订单号不能为空");
        }
    }

    private RLock tryLockTradePay(String orderNum) {
        String distributedLockName = "TRADE_PAY_" + orderNum;
        RLock lock = redissonClient.getLock(distributedLockName);
        boolean tryLock = false;
        try {
            tryLock = lock.tryLock(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            LOG.info("分布式所上锁失败 lockName:{}, StackTraces", distributedLockName, e);
        }
        if (!tryLock) {
            LOG.info("分布式所上锁失败(出现并发) lockName:{}, StackTraces:{}",
                    distributedLockName, Thread.getAllStackTraces());
            throw new BizException(PayExceptionEnum.REDIS_LOCK_ERROR);
        }
        return lock;
    }

    @Override
    public void notify(Notify payNotify) throws Exception {

        PayNotify notify = null;
        if (payNotify instanceof PayNotify) {
            notify = (PayNotify) payNotify;
        }
        if (notify == null) {
            LOG.error("支付异步回调通知数据为空 notify={}", JSONUtil.toJsonStr(payNotify));
            return;
        }
        TradePayRecord tradePayRecord = tradePayRecordService.loadBySn(notify.getOutTradeNo());
        if (tradePayRecord == null) {
            LOG.error("支付记录不存在,outTradeNo={}", notify.getOutTradeNo());
            return;
        }
        if (Objects.equals(tradePayRecord.getPayStatus(), TradeStateEnum.SUCCESSFUL.getState())) {
            return;
        }
        if (!Objects.equals(tradePayRecord.getPayAmount(), Long.valueOf(notify.getAmount().getPayerTotal()))) {
            LOG.error("支付异步回调通知支付金额与流水金额不相等 notify={},amount={}", JSONUtil.toJsonStr(payNotify), tradePayRecord.getPayAmount());
            return;
        }
        // 通知网关回调
        paymentGatewayService.callback(notify.getOutTradeNo(), GatewayStatusEnum.SUCCESS.getCode(), notify.getTradeNo());

        payRecordHelper.correctTradeRecord(tradePayRecord, TradeStateEnum.SUCCESSFUL.getState(), notify.getTradeNo(), notify.getSuccessTime());

        //todo:本来订单与支付应该解耦的，目前没有引入消息先这么做
        payFinish(notify, tradePayRecord.getRefOrderNum());

        //扫码订单发送飞书消息
        feishuHelper.sendFeiShuMsg(tradePayRecord.getTradeOrderNum(),
                tradePayRecord.getSn(),
                DateUtil.parse(notify.getSuccessTime()),
                tradePayRecord.getPayChannel());

    }

    private void payFinish(PayNotify payNotify, String orderNum) {
        try {
            OrderPayEvent event = new OrderPayEvent();
            event.setOrderNum(orderNum);
            event.setPayAmount(Long.valueOf(payNotify.getAmount().getPayerTotal()));
            event.setPayTime(DateUtil.parse(payNotify.getSuccessTime()));
            event.setOperator(OperatorEnum.CALL_BACK.getId());
            event.setSystem(SystemEnum.C.getCode());
            orderFsmEngine.sendEvent(event);
        } catch (Exception e) {
            LOG.warn("回调发送订单完成事件出现异常，可能为线下收单支付，error: ", e);
        }
    }
}
