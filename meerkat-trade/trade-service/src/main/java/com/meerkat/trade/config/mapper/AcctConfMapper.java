package com.meerkat.trade.config.mapper;

import com.meerkat.trade.config.mapper.dataobj.AcctConfDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AcctConfMapper {

    List<AcctConfDO> listByAccts(List<Long> list);

    AcctConfDO get(@Param("id") Long id);

    AcctConfDO getByMchid(@Param("mchid") String mchid);

    int insert(AcctConfDO acctConfDO);

    int batchInsert(List<AcctConfDO> list);

    int batchUpdate(List<AcctConfDO> list);
}
