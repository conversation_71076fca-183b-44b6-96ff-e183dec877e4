package com.meerkat.trade.config.factory;

import com.alipay.api.*;
import com.meerkat.trade.config.model.AlipayAppConfig;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 支付宝支付客户端工厂
 * @date 2022/2/9 3:44 下午
 */
public class AlipayPayService {

    private final Object lock = new Object();

    private Map<String, AlipayAppConfig> map = new HashMap<>();

    public AlipayPayService(Map<String, AlipayAppConfig> map) {
        this.map = map;
    }

    public AlipayClient getAlipayClient(String appid) throws AlipayApiException {
        DefaultAlipayClient defaultAlipayClient = null;
        try {
            defaultAlipayClient = new DefaultAlipayClient(map.get(appid));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return defaultAlipayClient;
    }

    public AlipayAppConfig getAlipayAppConfig(String appid) {
        return map.get(appid);
    }

    public <T extends AlipayResponse> T certificateExecute(String appid, AlipayRequest<T> request) throws AlipayApiException {
        AlipayAppConfig alipayAppConfig = map.get(appid);
        return new DefaultAlipayClient(alipayAppConfig).certificateExecute(request, null, alipayAppConfig.getAppAuthToken());
    }

    public <T extends AlipayResponse> T pageExecute(String appid, AlipayRequest<T> request) throws AlipayApiException {
        return new DefaultAlipayClient(map.get(appid)).pageExecute(request);
    }

//    public Boolean putCertRequest(String appid, CertAlipayRequest certAlipayRequest) {
//        synchronized (lock) {
//            map.put(appid, certAlipayRequest);
//        }
//        return Boolean.TRUE;
//    }

    public CertAlipayRequest getCertAlipayRequest(String mchid) {
        return map.get(mchid);
    }

    public Map<String, AlipayAppConfig> getMap() {
        return map;
    }

    public void setMap(Map<String, AlipayAppConfig> map) {
        this.map = map;
    }
}
