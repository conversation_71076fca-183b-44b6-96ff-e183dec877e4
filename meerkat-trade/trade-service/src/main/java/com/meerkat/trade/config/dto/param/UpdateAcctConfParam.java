package com.meerkat.trade.config.dto.param;

/**
 * <AUTHOR>
 * @description
 * @date 2022/4/21 10:57 上午
 */
public class UpdateAcctConfParam {

    /**
     * id
     */
    private Long id;

    /**
     * 商户号
     */
    private String mchid;

    /**
     * apiclient_key.pem 商户私钥部分证书路径
     */
    private String privateKeyPath;

    /**
     * apiclient_cert.pem商户私钥证书部分证书
     */
    private String appPrivateCertPath;

    /**
     * 加密密钥
     */
    private String encryptKey;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMchid() {
        return mchid;
    }

    public void setMchid(String mchid) {
        this.mchid = mchid;
    }

    public String getPrivateKeyPath() {
        return privateKeyPath;
    }

    public void setPrivateKeyPath(String privateKeyPath) {
        this.privateKeyPath = privateKeyPath;
    }

    public String getAppPrivateCertPath() {
        return appPrivateCertPath;
    }

    public void setAppPrivateCertPath(String appPrivateCertPath) {
        this.appPrivateCertPath = appPrivateCertPath;
    }

    public String getEncryptKey() {
        return encryptKey;
    }

    public void setEncryptKey(String encryptKey) {
        this.encryptKey = encryptKey;
    }
}
