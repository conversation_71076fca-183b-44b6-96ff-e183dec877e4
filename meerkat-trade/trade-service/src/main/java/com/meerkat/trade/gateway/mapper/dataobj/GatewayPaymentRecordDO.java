package com.meerkat.trade.gateway.mapper.dataobj;

import java.util.Date;

/**
 * @description:
 * @author: wa<PERSON><PERSON><PERSON><PERSON>
 * @date: 2022/2/11 11:04 上午
 */
public class GatewayPaymentRecordDO {

    /**
     * id
     */
    private Long id;

    /**
     * 内部流水
     */
    private String sn;

    /**
     * originally sn
     * 原内部流水（退款使用)
     */
    private String oriSn;

    /**
     * 外部流水（由第三方支付渠道返回）
     */
    private String tradeNo;

    /**
     * 支付金额，存储单位统一为分，暂时只考虑人民币的情况
     */
    private Long amount;

    /**
     * 支付渠道用户id，付款者/购买者id
     */
    private String buyerId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品描述
     */
    private String goodsDesc;

    /**
     * com.meerkat.trade.pay.enums.PayChannelEnum
     * 支付渠道，0：微信、1：支付宝
     */
    private Integer payChannel;

    /**
     * 流水发生应用appid
     */
    private String appid;

    /**
     * 支付渠道动账商户号，同时也是支付配置的唯一标识
     */
    private String mchid;

    /**
     * com.meerkat.trade.gateway.enums.PaymentCodeEnum
     * 支付代码，0：支付、1：退款、2：提现
     */
    private Integer paymentCode;

    /**
     * 网关流水状态，1：支付中、2：支付成功、3：支付失败、4：支付关闭
     */
    private Integer status;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 删除状态，0：未删除、1：已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 更新时间
     */
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getOriSn() {
        return oriSn;
    }

    public void setOriSn(String oriSn) {
        this.oriSn = oriSn;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsDesc() {
        return goodsDesc;
    }

    public void setGoodsDesc(String goodsDesc) {
        this.goodsDesc = goodsDesc;
    }

    public Integer getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(Integer payChannel) {
        this.payChannel = payChannel;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getMchid() {
        return mchid;
    }

    public void setMchid(String mchid) {
        this.mchid = mchid;
    }

    public Integer getPaymentCode() {
        return paymentCode;
    }

    public void setPaymentCode(Integer paymentCode) {
        this.paymentCode = paymentCode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
}
