package com.meerkat.trade.refund.mapper.dataobj;

import java.util.Date;

/**
 * @description:
 * @author: pantaoling
 * @date: 2021/11/3
 */
public class RefundApplyRecordDO {

    private Long id;
    /**
     * 退款审批id
     */
    private Long refRefundApplyId;
    /**
     * 订单编号
     */
    private String orderNum;

    /**
     * 审批操作人
     */
    private Long operatorId;

    /**
     * 审批人姓名
     */
    private String operatorName;

    /**
     * 当前审批状态
     */
    private Integer currentStatus;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date gmtCreated;
    /**
     * 更新时间
     */
    private Date gmtModified;

    private String reason;

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRefRefundApplyId() {
        return refRefundApplyId;
    }

    public void setRefRefundApplyId(Long refRefundApplyId) {
        this.refRefundApplyId = refRefundApplyId;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(Integer currentStatus) {
        this.currentStatus = currentStatus;
    }
}
