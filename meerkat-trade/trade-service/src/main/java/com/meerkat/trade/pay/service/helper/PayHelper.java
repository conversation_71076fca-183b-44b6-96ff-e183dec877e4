package com.meerkat.trade.pay.service.helper;

import com.meerkat.trade.pay.dto.*;
import com.meerkat.trade.pay.enums.TradeStateEnum;
import com.meerkat.trade.pay.model.TradePayRecord;
import com.meerkat.trade.pay.service.inner.TradePayRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Random;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Component
public class PayHelper {


	@Autowired
	private TradePayRecordService tradePayRecordService;

	public TradePayRecord buildTradeRecord(PayContext context, PayRequest payRequest, PaymentDetail paymentDetail) {
		TradePayRecord tradePayRecord = new TradePayRecord();
		tradePayRecord.setRefOrderNum(payRequest.getOrderNum());
		tradePayRecord.setSn(genUnifySn(context.getTradeOrderNum()));
		tradePayRecord.setTradeOrderNum(context.getTradeOrderNum());
		tradePayRecord.setPayProduct(paymentDetail.getPayProduct());
		tradePayRecord.setPayChannel(paymentDetail.getPayChannel());
		tradePayRecord.setPayAmount(paymentDetail.getPayAmount());
		tradePayRecord.setPayStatus(TradeStateEnum.DOING.getState());
		tradePayRecord.setPayScene(payRequest.getPayScene());
		tradePayRecord.setPaymentType(paymentDetail.getPaymentType().getCode());
		tradePayRecord.setPaymentSubAccount(paymentDetail.getPaymentSubAccount());
		return tradePayRecord;
	}
	
	/**
	 * 获取一个不重复的 SN
	 * @return
	 */
	public String genUnifySn(String tradeOrderNum) {
		String sn;
		do {
			UUID uuid = UUID.randomUUID();
			Random random = new Random(uuid.getMostSignificantBits());
			int a = random.nextInt(999999);
			sn = String.format("%s%06d", tradeOrderNum, a);
		} while (tradePayRecordService.isExistsSn(sn));
		return sn;
	}

	public void buildFailedResponse(PayContext context, TradePayRecord tradePayRecord, String errorMessage) {
		TradePayRecordUpdateDTO tradePayRecordUpdateDTO = new TradePayRecordUpdateDTO();
		tradePayRecordUpdateDTO.setId(tradePayRecord.getId());
		tradePayRecordUpdateDTO.setNewPayStatus(TradeStateEnum.FAILED.getState());
		tradePayRecordUpdateDTO.setOldPayStatus(tradePayRecord.getPayStatus());
		tradePayRecordService.update(tradePayRecordUpdateDTO);
		PayResponse resp = new PayResponse();
		resp.setSuccess(false);
		resp.setMsg(errorMessage);
		context.setPayResponse(resp);
	}

}
