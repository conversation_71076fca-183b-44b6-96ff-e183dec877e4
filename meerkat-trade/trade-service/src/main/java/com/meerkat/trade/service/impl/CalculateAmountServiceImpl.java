package com.meerkat.trade.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Lists;
import com.meerkat.common.enums.PaymentTypeEnum;
import com.meerkat.trade.dto.PaymentChannel;
import com.meerkat.trade.dto.TradeAmount;
import com.meerkat.trade.pay.enums.PayChannelEnum;
import com.meerkat.trade.pay.enums.TradeStateEnum;
import com.meerkat.trade.pay.model.TradePayRecord;
import com.meerkat.trade.pay.service.inner.TradePayRecordService;
import com.meerkat.trade.refund.model.TradeRefundRecord;
import com.meerkat.trade.refund.service.helper.RefundHelper;
import com.meerkat.trade.refund.service.impl.inner.TradeRefundRecordService;
import com.meerkat.trade.service.CalculateAmountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: pantaoling
 * @date: 2021/11/25
 */
@Service
public class CalculateAmountServiceImpl implements CalculateAmountService {

    @Autowired
    private TradePayRecordService tradePayRecordService;

    @Autowired
    private TradeRefundRecordService tradeRefundRecordService;

    @Autowired
    private RefundHelper refundHelper;

    @Override
    public List<TradeAmount> getPayAmountByOrderNum(String orderNum) {
        return null;
    }

    @Override
    public List<TradeAmount> getRefundAmountByOrderNum(String orderNum, Long refundAmount) {
        List<TradePayRecord> tradePayRecords = tradePayRecordService.listByRefOrderNum(orderNum);
        if (CollectionUtils.isEmpty(tradePayRecords)) {
            return new ArrayList<>();
        }
        Map<Integer, List<TradePayRecord>> listMap = tradePayRecords.stream()
                .filter(tradePayRecord -> Objects.equals(tradePayRecord.getPayStatus(), TradeStateEnum.SUCCESSFUL.getState()))
                .collect(Collectors.groupingBy(TradePayRecord::getPayChannel));
        Map<String, List<TradeRefundRecord>> refundRecords = getRefundRecords(orderNum);
        List<TradeAmount> tradeAmounts = Lists.newArrayList();
        listMap.forEach((k, v) -> {
            for (TradePayRecord record : v) {
                TradeAmount tradeAmount = new TradeAmount();
                List<TradeRefundRecord> records = refundRecords.get(record.getSn());
                //该笔支付记录已经退款的金额
                long alreadyRefundAmount = refundHelper.getAlreadyRefundAmount(records);
                //该笔支付记录剩余未退款金额
                long needRefundAmount = record.getPayAmount() - alreadyRefundAmount;
                // 该笔支付记录金额已经全部退款了 獴哥健康平台支付可能产生0元支付，允许退款
                if (needRefundAmount <= 0) {
                    // 必须为獴哥健康支付渠道并且支付单金额为0才允许0元退款
                    if (!Objects.equals(record.getPayChannel(), PayChannelEnum.MEERKAT.getCode())) {
                        continue;
                    }
                }
                tradeAmount.setAmount(NumberUtil.min(needRefundAmount, refundAmount));
                tradeAmount.setSn(record.getSn());
                tradeAmount.setPayProduct(record.getPayProduct());
                tradeAmount.setPayChannel(record.getPayChannel());
                tradeAmount.setPaymentType(PaymentTypeEnum.getByCode(record.getPaymentType()));
                tradeAmount.setPaymentSubAccount(record.getPaymentSubAccount());
                tradeAmount.setPayAmount(record.getPayAmount());
                tradeAmounts.add(tradeAmount);
            }
        });
        return tradeAmounts;
    }

    private Map<String, List<TradeRefundRecord>> getRefundRecords(String orderNum) {
        List<TradeRefundRecord> allTradeRefundRecords = tradeRefundRecordService.listRecordByRefOrderNum(orderNum);
        // 该笔订单所有退款成功的记录，key:ref_pay_sn(支付流水号), value:该支付流水号对应的退款成功的退款记录集合
        Map<String, List<TradeRefundRecord>> tradeRefundRecordMap = new HashMap<>();
        for (TradeRefundRecord tradeRefundRecord : allTradeRefundRecords) {
            if (TradeStateEnum.SUCCESSFUL.getState() == tradeRefundRecord.getRefundStatus()) {
                List<TradeRefundRecord> tradeRefundRecords = tradeRefundRecordMap.computeIfAbsent(tradeRefundRecord.getRefPaySn(), k -> new ArrayList<>());
                tradeRefundRecords.add(tradeRefundRecord);
            }
        }
        return tradeRefundRecordMap;
    }
}
