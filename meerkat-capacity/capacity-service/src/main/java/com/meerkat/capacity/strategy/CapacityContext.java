package com.meerkat.capacity.strategy;

import com.meerkat.capacity.dto.CapacityReqDTO;
import com.meerkat.capacity.model.Capacity;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName CapacityContext.java
 * @Description TODO
 * @createTime 2021-10-21 00:30:00
 */
public class CapacityContext<E extends Capacity> {

    private CapacityStrategy capacityStrategy;

    public CapacityContext(CapacityStrategy capacityStrategy){
        this.capacityStrategy = capacityStrategy;
    }

    public boolean deduct(Long capacityId){
        return this.capacityStrategy.deduct(capacityId);
    }

    public boolean add(Long capacityId){
        return this.capacityStrategy.add(capacityId);
    }

    public E getCapacity(Long capacityId) {
        return (E) this.capacityStrategy.getCapacity(capacityId);
    }

    public List<E> listCapacity(CapacityReqDTO capacityReqDTO) {
        return this.capacityStrategy.listCapacity(capacityReqDTO);
    }

    public List<E> listCapacityByOrgaId(Long id, String startDate, String endDate,Integer bizType) {
        return this.capacityStrategy.listCapacityByOrgaId(id, startDate, endDate, bizType);
    }
}
