package com.meerkat.capacity.mapper;

import com.meerkat.capacity.dao.object.TjsyUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @ClassName TjsyUserMapper.java
 * @Description TODO
 * @createTime 2021-10-29 14:23:00
 */
@Mapper
public interface TjsyUserMapper {

    int insert(TjsyUserDO tjsyUserDO);

    TjsyUserDO selectByIdCard(@Param("idCard") String idCard);

    TjsyUserDO selectByMobile(@Param("mobile") String mobile);

}
