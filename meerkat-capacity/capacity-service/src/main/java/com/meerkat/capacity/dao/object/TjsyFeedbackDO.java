package com.meerkat.capacity.dao.object;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName TjsyFeedbackDO.java
 * @Description TODO
 * @createTime 2021-12-30 11:41:00
 */
public class TjsyFeedbackDO implements Serializable {

    private static final long serialVersionUID = 6926954377170281533L;

    /**
     * id
     */
    private Integer id;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 是否赞扬:1-是， 0-否
     */
    private Integer praise;

    /**
     * 评价
     */
    private String message;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public Integer getPraise() {
        return praise;
    }

    public void setPraise(Integer praise) {
        this.praise = praise;
    }

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
