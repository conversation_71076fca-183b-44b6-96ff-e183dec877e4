package com.meerkat.capacity.strategy;

import com.meerkat.capacity.dto.CapacityReqDTO;
import com.meerkat.capacity.model.Capacity;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName Capacity.java
 * @Description TODO
 * @createTime 2021-10-21 00:15:00
 */
public interface CapacityStrategy<E extends Capacity> {

    /**
     *  减库存
     * @param capacityId
     * @return
     */
    boolean deduct(Long capacityId);


    /**
     * 加库存
     * @param capacityId
     * @return
     */
    boolean add(Long capacityId);


    /**
     *  获取库存
     * @param capacityId
     * @param <E>
     * @return
     */
    <E extends Capacity> E getCapacity(Long capacityId);

    /**
     *  获取排期列表
     * @param capacityReqDTO
     * @return
     */
    List<E> listCapacity(CapacityReqDTO capacityReqDTO);

    /**
     *  获取排期列表
     * @param id ：商品id、机构id、
     * @param bizType
     * @return
     */
    List<E> listCapacityByOrgaId(Long id, String startDate, String endDate, Integer bizType);
}
