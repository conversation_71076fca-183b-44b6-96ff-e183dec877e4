package com.meerkat.capacity.template;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meerkat.capacity.dao.object.CapacityUsedDetailDO;
import com.meerkat.capacity.dto.RecycleCapacityReqDTO;
import com.meerkat.capacity.enums.CapacityUsedTypeEnum;

import com.meerkat.capacity.exception.CapacityBizExEnum;
import com.meerkat.capacity.mapper.CapacityUsedDetailMapper;
import com.meerkat.capacity.mapper.OrgaCapacityMapper;
import com.meerkat.capacity.model.CapacityUsedDetail;
import com.meerkat.capacity.model.CounterContext;
import com.meerkat.common.exception.BizException;
import com.meerkat.common.utils.CopyUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program meerkat-origin
 * @description: 回收排期
 * @author: fandongdong
 * @create: 2022/07/11 14:14
 */
@Service
public class RecycleCapacityService extends CounterOperateTemplate<CounterContext<RecycleCapacityReqDTO>> {
    @Autowired
    private CapacityUsedDetailMapper capacityUsedDetailMapper;
    @Autowired
    private OrgaCapacityMapper orgaCapacityMapper;

    @Override
    public void preProcess(CounterContext<RecycleCapacityReqDTO> counterContext) {
        RecycleCapacityReqDTO counterReqDto = counterContext.getCounterReqDto();
        List<String> orderNumList = counterReqDto.getOrderNumList();
        List<CapacityUsedDetailDO> capacityUsedDetailDOList = capacityUsedDetailMapper.getCapacityUsedDetail(orderNumList, CapacityUsedTypeEnum.DEDUCT.getType(), null, null, null);
        if (capacityUsedDetailDOList.size() == 0 && counterReqDto.getCapacityId() == null) {
            return;
        }
        List<CapacityUsedDetail> capacityUsedDetailList = CopyUtil.copyList(capacityUsedDetailDOList,
                CapacityUsedDetail.class);
        counterContext.setCapacityUsedDetailList(capacityUsedDetailList);
    }

    @Override
    public void compute(CounterContext<RecycleCapacityReqDTO> counterContext) {
        List<CapacityUsedDetail> capacityUsedDetailList = counterContext.getCapacityUsedDetailList();
        if (CollectionUtils.isNotEmpty(capacityUsedDetailList)) {
            for (CapacityUsedDetail capacityUsedDetail : capacityUsedDetailList) {
                capacityUsedDetail.setUsedType(CapacityUsedTypeEnum.RECYCLE.getType());
            }
        }
    }

    @Override
    public void store(CounterContext<RecycleCapacityReqDTO> counterContext) {

        List<CapacityUsedDetail> capacityUsedDetailList = counterContext.getCapacityUsedDetailList();
        List<CapacityUsedDetailDO> capacityUsedDetailDOS = CopyUtil.copyList(capacityUsedDetailList, CapacityUsedDetailDO.class);
        if (CollectionUtils.isNotEmpty(capacityUsedDetailDOS)) {
            for (CapacityUsedDetailDO capacityUsedDetailDO : capacityUsedDetailDOS) {
                capacityUsedDetailMapper.updateCapacityUsedDetail(capacityUsedDetailDO);
            }
        }
        //capacityUsedDetailMapper.batchUpdateCapacityUsedDetail(capacityUsedDetailDOS);
        //todo 临时替换批量操作
        Set<Long> capacityIds = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(capacityUsedDetailList)) {
            Set<Long> capacityIdSets = capacityUsedDetailList.stream().map(CapacityUsedDetail::getCapacityId).collect(Collectors.toSet());
            capacityIds.addAll(capacityIdSets);
        }
        Long capacityId = counterContext.getCounterReqDto().getCapacityId();
        if (capacityId != null) {
            capacityIds.add(capacityId);
        }
        if (CollectionUtils.isNotEmpty(capacityIds)) {
            int i = orgaCapacityMapper.batchIncreaseCapacity(Lists.newArrayList(capacityIds));
            if (i != capacityIds.size()) {
                throw new BizException(CapacityBizExEnum.CAPACITY_INCREASE_FAIL);
            }
        }
    }
}
