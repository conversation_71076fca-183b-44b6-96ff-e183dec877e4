package com.meerkat.capacity.strategy;

import com.meerkat.capacity.dto.CapacityReqDTO;
import com.meerkat.capacity.exception.CapacityBizExEnum;
import com.meerkat.capacity.model.OrgaCapacity;
import com.meerkat.capacity.service.OrgaCapacityService;
import com.meerkat.common.exception.BizException;
import com.meerkat.smart.organization.service.OrganizationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName CompCapacityStrategy.java
 * @Description TODO
 * @createTime 2021-12-16 17:09:00
 */

@Service("compCapacityStrategy")
public class CompCapacityStrategy implements CapacityStrategy {

    private static final Logger logger = LoggerFactory.getLogger(CompCapacityStrategy.class);

    @Resource(name = "compCapacityService")
    private OrgaCapacityService compCapacityService;

    @Resource
    private OrganizationService organizationService;

    @Override
    public boolean deduct(Long capacityId) {
        /**
         * 通过capacity有没有办法唯一确定排期信息。
         * 1、下单时候的capacityId是哪些？
         *      1、1预约人数控制项目
         *      1、2全局人数控制项目
         * 2、
         */
        OrgaCapacity orgaCapacity = compCapacityService.getOrgaCapacity(capacityId);

        Date closeDate = organizationService.getRecentCanBookDate(orgaCapacity.getOrganizationId());

        if(closeDate.after(orgaCapacity.getDate())){
            throw new BizException(CapacityBizExEnum.NOT_BOOK_TIME_SEGMENT);
        }
        return compCapacityService.reduceCapacity(capacityId);
    }

    @Override
    public boolean add(Long capacityId) {
        OrgaCapacity orgaCapacity = compCapacityService.getOrgaCapacity(capacityId);
        if (orgaCapacity == null){
            throw new BizException(CapacityBizExEnum.CAPACITY_NOT_FOUND);
        }

        return compCapacityService.increaseCapacity(capacityId);
    }

    @Override
    public List<OrgaCapacity> listCapacity(CapacityReqDTO capacityReqDTO) {

        return compCapacityService.listCompCapacity(capacityReqDTO.getOrgaId(),
                capacityReqDTO.getCompanyId(), capacityReqDTO.getLastDate(), null);
    }

    @Override
    public List listCapacityByOrgaId(Long id, String startDate, String endDate, Integer bizType) {
        return null;
    }

    @Override
    public OrgaCapacity getCapacity(Long capacityId) {
        return compCapacityService.getOrgaCapacity(capacityId);
    }
}
