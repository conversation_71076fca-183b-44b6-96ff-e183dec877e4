package com.meerkat.capacity.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 回收人数
 */
public class RecycleCapacityReqDTO extends BaseCapacityReqDTO implements Serializable {
    private static final long serialVersionUID = -8208942974135298960L;
    private List<String> orderNumList;
    private Long capacityId;

    public Long getCapacityId() {
        return capacityId;
    }

    public void setCapacityId(Long capacityId) {
        this.capacityId = capacityId;
    }

    public List<String> getOrderNumList() {
        return orderNumList;
    }

    public void setOrderNumList(List<String> orderNumList) {
        this.orderNumList = orderNumList;
    }

}
