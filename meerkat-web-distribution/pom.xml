<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>origin</artifactId>
        <groupId>com.meerkat.origin</groupId>
        <version>1.2.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>meerkat-web-distribution</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>message-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>message-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>base-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>wx-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>wx-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>smart-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>smart-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>shop-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>shop-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>user-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>user-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>order-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>order-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>platform-distribution-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>platform-distribution-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>distribution-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>distribution-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>marketing-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>marketing-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>coupon-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>coupon-service</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>meerkat-auth</artifactId>
        </dependency>
        <!-- 引入Swagger3依赖 -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- https://mvnrepository.com/artifact/io.netty/netty-all -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <finalName>meerkat-web-distribution</finalName>
    </build>

</project>