package com.meerkat.sso;

import com.google.common.collect.Maps;
import com.meerkat.common.api.CommonResult;
import com.meerkat.common.cache.CacheModelEnum;
import com.meerkat.common.domain.AuthCode;
import com.meerkat.common.enums.ChorgaTypeEnum;
import com.meerkat.common.enums.SystemEnum;
import com.meerkat.common.exception.BizException;
import com.meerkat.common.utils.AuthCodeUtil;
import com.meerkat.message.constants.SmsTemplateParamConstants;
import com.meerkat.message.enums.SmsTemplateEnum;
import com.meerkat.message.model.param.MessageParam;
import com.meerkat.message.model.param.SmsParam;
import com.meerkat.message.service.MessageService;
import com.meerkat.user.exception.UserBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/26 16:37
 */
@RestController
@Api(tags = "LoginController", value = "用户注册登录管理")
@RequestMapping("/sso")
public class LoginController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private MessageService messageService;

    private static final Long DEFAULT_ORGANIZATION = 0L;

    /**
     * 手机号验证码登录
     *
     * @param mobile
     * @param authCode
     * @return
     */
    @ApiOperation("手机号登录")
    @PostMapping("/mobileCode/login")
    public CommonResult<Map<String, Object>> mobileCodeLogin(String mobile,
                                                             String authCode) {
        return null;
    }

    /**
     * 发送短信验证码，并返回生成时间
     *
     * @param mobile
     * @return com.meerkat.common.api.CommonResult<java.util.Date>
     * <AUTHOR>
     * @date 2022/5/26 17:23
     */
    @ApiOperation("发送短信验证码")
    @GetMapping("/sendAuthCode")
    public CommonResult<Date> sendAuthCode(String mobile) {
        // 校验
        validate(mobile);

        // 生成验证码
        AuthCode authCode = AuthCodeUtil.generateAuthCodeAndSave(mobile, SystemEnum.DIS, CacheModelEnum.SMS_CODE);

        // 构建短信参数
        Map<String, String> param = Maps.newHashMap();
        param.put(SmsTemplateParamConstants.AUTH_CODE, authCode.getAuthCode());
        MessageParam messageParam = new SmsParam(
                DEFAULT_ORGANIZATION,
                ChorgaTypeEnum.ORGANIZATION.getCode(),
                null,
                SmsTemplateEnum.GENERAL_AUTH_CODE,
                param,
                mobile);

        // 发送
        messageService.send(messageParam);
        return CommonResult.success(authCode.getCreateTime());
    }

    /**
     * 校验手机号格式 & 60s内是否发送过短信
     *
     * @param mobile
     * @return void
     * <AUTHOR>
     * @date 2022/5/26 17:22
     */
    private void validate(String mobile) {
        boolean validateGenerateAuthCodeMobile = AuthCodeUtil.validateGenerateAuthCodeMobile(mobile);
        if (!validateGenerateAuthCodeMobile) {
            throw new BizException(UserBizException.MOBILE_FORMAT_ERROR);
        }
        if (!AuthCodeUtil.validateSendSms60s(mobile, SystemEnum.C, CacheModelEnum.SMS_CODE)) {
            throw new BizException();
        }
    }
}
