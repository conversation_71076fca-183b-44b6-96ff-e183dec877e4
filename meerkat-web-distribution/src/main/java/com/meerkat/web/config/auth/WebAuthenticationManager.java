package com.meerkat.web.config.auth;

import com.meerkat.auth.component.token.DefaultAuthenticationToken;
import com.meerkat.user.enums.UserTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/26 16:24
 */
@Component
public class WebAuthenticationManager implements AuthenticationManager {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private MobileProvider mobileProvider;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        DefaultAuthenticationToken token = (DefaultAuthenticationToken) authentication;

        // 分销端只有手机号登录
        if (token.getUserTypeEnum().equals(UserTypeEnum.MOBILE_CHECK)){
            return mobileProvider.authenticate(authentication);
        }
        return token;
    }
}
