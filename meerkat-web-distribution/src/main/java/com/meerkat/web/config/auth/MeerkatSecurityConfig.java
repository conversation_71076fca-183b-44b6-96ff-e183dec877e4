package com.meerkat.web.config.auth;

import com.meerkat.auth.component.filter.SmsAuthenticationFilter;
import com.meerkat.auth.component.handler.MeerkatAuthenticationFailureHandler;
import com.meerkat.auth.config.SecurityConfig;
import com.meerkat.common.enums.SystemEnum;
import io.netty.handler.codec.http.HttpMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/26 15:26
 */
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@Configuration
public class MeerkatSecurityConfig extends SecurityConfig {

    @Autowired
    private WebAuthenticationManager webAuthenticationManager;

    @Autowired
    private MeerkatAuthenticationSuccessHandler successHandler;

    @Autowired
    private MeerkatAuthenticationFailureHandler failureHandler;

    @Override
    protected void configure(HttpSecurity httpSecurity) throws Exception {
        ExpressionUrlAuthorizationConfigurer<HttpSecurity>.ExpressionInterceptUrlRegistry registry = httpSecurity
                .authorizeRequests();
        super.configure(httpSecurity);

        // 添加手机短信验证码校验过滤器
        registry.and()
                //判断用户登录状态
                .addFilterBefore(jwtAuthenticationTokenFilter(), UsernamePasswordAuthenticationFilter.class)
                .addFilterBefore(smsAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        super.configure(auth);
    }

    /**
     * 短信登录过滤器
     */
    @Bean
    public SmsAuthenticationFilter smsAuthenticationFilter() {
        SmsAuthenticationFilter filter = new SmsAuthenticationFilter("/sso/mobileCode/login/**", HttpMethod.POST.name());
        filter.setAuthenticationManager(webAuthenticationManager);
        filter.setAuthenticationSuccessHandler(successHandler);
        filter.setAuthenticationFailureHandler(failureHandler);
        filter.setSystemEnum(SystemEnum.DIS);
        return filter;
    }

    /**
     * token校验器
     */
    @Bean
    public JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter() {
        return new JwtAuthenticationTokenFilter();
    }

}
