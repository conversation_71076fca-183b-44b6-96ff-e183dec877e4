package com.meerkat.web.config;

import com.meerkat.common.api.BaseBizCodeEnum;
import com.meerkat.common.api.CommonResult;
import com.meerkat.common.exception.BizException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName UniformResponseHandler.java
 * @Description TODO
 * @createTime 2021-10-12 14:56:00
 */

@RestControllerAdvice
public class UniformExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(UniformExceptionHandler.class);

    @ExceptionHandler(BizException.class)
    @ResponseStatus(HttpStatus.OK)
    public <T> CommonResult<T> sendBizExceptionResponse(BizException bizException){
        logger.error("接口调用发生业务异常, errorCode:{}", bizException.getErrorCode(), bizException);
        return CommonResult.failed(bizException.getErrorCode(), bizException.getMessage());
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public <T> CommonResult<T> sendSysExceptionResponse(Exception exception){
        if(exception instanceof BizException){
            BizException bizException = (BizException) exception;
            logger.error("接口调用发生业务异常, errorCode:{}", bizException.getErrorCode(), bizException);
            return this.sendBizExceptionResponse((BizException)exception);
        }
        logger.error("接口调用发生系统异常", exception);
        return CommonResult.failed(BaseBizCodeEnum.FAILED);
    }

    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public <T> CommonResult<T> bindExceptionResponse(BindException exception){
        StringBuilder sbf = new StringBuilder();
        List<FieldError> fieldErrors = exception.getFieldErrors();
        for (FieldError fieldError : fieldErrors) {
            sbf.append(fieldError.getField()).append(fieldError.getDefaultMessage()).append(",");
        }
        sbf.delete(sbf.length()-1, sbf.length());
        logger.warn("接口入参错误", exception);
        return CommonResult.failed(BaseBizCodeEnum.ILLEGAL_ARGUMENT, sbf.toString());
    }
}
