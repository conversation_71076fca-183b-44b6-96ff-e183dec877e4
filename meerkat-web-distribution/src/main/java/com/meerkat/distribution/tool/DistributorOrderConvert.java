package com.meerkat.distribution.tool;

import cn.hutool.core.collection.CollectionUtil;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.distribution.platform.model.DistributionOrder;
import com.meerkat.distribution.vo.DistributionOrderVO;
import com.meerkat.order.enums.OrderSelectEnum;
import com.meerkat.order.model.Order;
import com.meerkat.order.param.OrderCustomerQuery;
import com.meerkat.order.service.OrderReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/27 17:40
 */
@Component
public class DistributorOrderConvert {

    @Autowired
    private OrderReadService orderReadService;

    public List<DistributionOrderVO> convertDistributionOrderVOS(List<DistributionOrder> distributionOrders) {
        if (CollectionUtil.isEmpty(distributionOrders)) {
            return new ArrayList<>();
        }
        List<String> orderNums = distributionOrders.stream().map(DistributionOrder::getOrderNum).collect(Collectors.toList());

        // 查询订单集合
        OrderCustomerQuery orderCustomerQuery = new OrderCustomerQuery();
        orderCustomerQuery.setOrderNums(orderNums);

        List<Order> orders = orderReadService.listByCustomerQuery(orderCustomerQuery, OrderSelectEnum.GOODS);

        return distributionOrders.stream().map(
                distributionOrder -> {
                    DistributionOrderVO distributionOrderVO = CopyUtil.copy(distributionOrder, DistributionOrderVO.class);
                    for (Order order: orders) {
                        if (Objects.equals(distributionOrder.getOrderNum(), order.getOrderNum())) {
                            distributionOrderVO.setOrder(order);
                        }
                    }
                    return distributionOrderVO;
                }
        ).collect(Collectors.toList());
    }
}
