spring.profiles.active=dev
spring.application.name=meerkat-web-distribution


spring.jackson.time-zone=GMT+8
spring.jackson.serialization.write-dates-as-timestamps=true
spring.jackson.default-property-inclusion=non_null

server.port=9092
server.servlet.context-path=/meerkat/distribution

secure.ignored.urls[0]=/isOK
secure.ignored.urls[1]=/v3/api-docs
secure.ignored.urls[2]=/swagger**/**
secure.ignored.urls[3]=/druid/**
secure.ignored.urls[4]=/sso/**
secure.ignored.urls[5]=/**/login

mybatis.mapper-locations=classpath*:com/meerkat/**/mapper/*.xml
pagehelper.helper-dialect=mysql
pagehelper.reasonable=false

spring.rabbitmq.username=hcrm_user
spring.rabbitmq.password=Mgjk123456!
spring.rabbitmq.virtual-host=/
spring.rabbitmq.publisher-returns=true
spring.rabbitmq.addresses=**********:5672

wx.miniapp.configs[0].appid=wx9e1748edab2e14c3
wx.miniapp.configs[0].secret=24b053976ce8241e3a9bac2289d5c213
wx.miniapp.configs[0].msgDataFormat=JSON

# 微信第三方平台配置
wx.open.component-app-id=wxe03c0eabbda1642f
wx.open.component-secret=005f5cc24ccd7ae116a019b616157562
wx.open.component-token=menggejkJkjkUjj8Acjk
wx.open.component-aes-key=menggejkJOjkljk76jkl99dsjkfkjasHBkjkljllkdA