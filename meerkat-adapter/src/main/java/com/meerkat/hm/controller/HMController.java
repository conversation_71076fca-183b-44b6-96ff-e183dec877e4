package com.meerkat.hm.controller;

import com.meerkat.common.api.CommonResult;
import com.meerkat.common.db.PageView;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.hm.model.HmPrivilege;
import com.meerkat.hm.model.HmUserPrivilege;
import com.meerkat.hm.param.HmPrivilegeQuery;
import com.meerkat.hm.param.HmUserPrivilegeQuery;
import com.meerkat.hm.service.HmPrivilegeService;
import com.meerkat.hm.vo.HmUserPrivilegeVO;
import com.meerkat.shop.goods.model.Goods;
import com.meerkat.shop.goods.service.GoodsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName HMController.java
 * @Description
 * @createTime 2022-09-02 17:23:00
 */
@Api(tags = {"HMController"})
@RestController
@RequestMapping("/hm")
public class HMController {

    @Autowired
    private HmPrivilegeService hmPrivilegeService;

    @Autowired
    private GoodsService goodsService;

    @ApiOperation(value = "查询用户领取权益列表")
    @PostMapping("/listCustomerHmPrivilege")
    public CommonResult<PageView<HmUserPrivilegeVO>> listCustomerHmPrivilege(@RequestBody HmUserPrivilegeQuery query) {
        PageView<HmUserPrivilege> pageHmUserPrivilege = hmPrivilegeService.pageViewHmUserPrivilegeByQuery(query);
        List<HmUserPrivilegeVO> list = Lists.newArrayList();
        List<HmUserPrivilege> hmUserPrivileges = pageHmUserPrivilege.getRecords();
        if (CollectionUtils.isNotEmpty(hmUserPrivileges)) {
            List<Long> hmPrivilegeIds = hmUserPrivileges.stream()
                    .map(HmUserPrivilege::getHmPrivilegeId)
                    .distinct()
                    .collect(Collectors.toList());
            HmPrivilegeQuery hmPrivilegeQuery = new HmPrivilegeQuery();
            hmPrivilegeQuery.setIds(hmPrivilegeIds);
            Map<Long, HmPrivilege> hmPrivilegeMap = hmPrivilegeService.listHmPrivilegeListByQuery(hmPrivilegeQuery).stream().collect(Collectors.toMap(HmPrivilege::getId, Function.identity()));
            List<Long> goodsIds = hmUserPrivileges.stream().map(HmUserPrivilege::getGoodsId).distinct().collect(Collectors.toList());
            Map<Long, Goods> goodsMap = goodsService.listGoodsByIds(goodsIds).stream().collect(Collectors.toMap(Goods::getId, Function.identity()));
            for (HmUserPrivilege hmUserPrivilege : hmUserPrivileges) {
                HmUserPrivilegeVO hmUserPrivilegeVO = CopyUtil.copy(hmUserPrivilege, HmUserPrivilegeVO.class);
                HmPrivilege hmPrivilege = hmPrivilegeMap.get(hmUserPrivilege.getHmPrivilegeId());
                if (hmPrivilege != null) {
                    hmUserPrivilegeVO.setHmPrivilege(hmPrivilege);
                }
                Goods goods = goodsMap.get(hmUserPrivilege.getGoodsId());
                if (goods != null) {
                    hmUserPrivilegeVO.setGoods(goods);
                }
                list.add(hmUserPrivilegeVO);
            }
        }
        return CommonResult.success(new PageView<>(list, pageHmUserPrivilege.getPage()));
    }
}
