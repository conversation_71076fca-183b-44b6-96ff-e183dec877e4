package com.meerkat.crdt.controller;

import com.meerkat.common.api.CommonResult;

import com.meerkat.crdt.utils.HeaderUtils;
import com.meerkat.shop.goods.param.GoodsSyncParam;
import com.meerkat.shop.goods.service.GoodsSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName GoodsSyncController.java
 * @Description TODO
 * @createTime 2022-02-16 13:41:00
 */
@RequestMapping("/crdt/goodsSync")
@RestController
public class GoodsSyncController {

    private static final Logger logger = LoggerFactory.getLogger(GoodsSyncController.class);

    @Resource
    GoodsSyncService goodsSyncService;

    /**
     * CRM创建商品同步至体检OS
     * @param request 请求
     * @return 返回同步数据
     */
    @GetMapping("/listNeedCreatGoods")
    public CommonResult<List<GoodsSyncParam>> listNeedCreatGoods(HttpServletRequest request){
        // 拿到当前的机构id
        Long orgaId = HeaderUtils.getOrgaId(request);
        return CommonResult.success(goodsSyncService.listNeedCreatGoods(orgaId));
    }


    /**
     * CRM创建商品同步至体检OS
     * @param request 请求
     * @return 返回同步数据
     */
    @PostMapping("/backWriteGoodsCode")
    public CommonResult<Void> backWriteGoodsCode(HttpServletRequest request, @RequestBody List<GoodsSyncParam> params){
        // 拿到当前的机构id
        Long orgaId = HeaderUtils.getOrgaId(request);
        // 回写服务
        goodsSyncService.backWriteGoodsCode(orgaId, params);
        return CommonResult.success();
    }

}
