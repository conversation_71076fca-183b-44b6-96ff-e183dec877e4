package com.meerkat.crdt.param;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/2/23 11:08
 */
public class DoneOrderItemParam  implements Serializable {

    /**
     *项目名称
     */
    private String name;

    /**
     * 内网项目code
     */
    private String innerItemCode;

    /**
     * 项目售价
     */
    private Long price;

    /**
     * 拒检状态：
     * 1—拒检，
     * 2—已检
     */
    private Integer refuseStatus;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getInnerItemCode() {
        return innerItemCode;
    }

    public void setInnerItemCode(String innerItemCode) {
        this.innerItemCode = innerItemCode;
    }

    public Long getPrice() {
        return price;
    }

    public void setPrice(Long price) {
        this.price = price;
    }

    public Integer getRefuseStatus() {
        return refuseStatus;
    }

    public void setRefuseStatus(Integer refuseStatus) {
        this.refuseStatus = refuseStatus;
    }
}
