package com.meerkat.fulmt.plan.controller;

import com.meerkat.auth.annotation.Auth;
import com.meerkat.common.api.CommonResult;
import com.meerkat.fulfillment.plan.service.FulmtCustomerPlanService;
import com.meerkat.fulmt.plan.query.CustomerVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 履约计划控制器
 *
 * <AUTHOR>
 * @date 2022/05/05 14:25
 */
@Api(tags = "履约对外接口")
@RestController
@RequestMapping("/fulmt/plan")
public class FulmtPlanController {


    @Autowired
    private FulmtCustomerPlanService fulmtCustomerPlanService;


    @ApiOperation("根据客户id获取是否关联套餐")
    @PostMapping("/getPlanPkgCountByCustomerId")
    @Auth
    public CommonResult<Long> getPlanPkgCountByCustomerId(@RequestBody CustomerVO customerVO) {
        Long count = fulmtCustomerPlanService.getListByCustomer(customerVO.getCustomerId(), customerVO.getGroupId());
        return CommonResult.success(count);
    }

    @ApiOperation("根据客户组id获取是否关联套餐")
    @PostMapping("/getPlanPkgCountByGroupId")
    @Auth
    public CommonResult<Long> getPlanPkgCountByGroupId(@RequestBody CustomerVO customerVO) {
        Long count = fulmtCustomerPlanService.getListByCustomer(null, customerVO.getGroupId());
        return CommonResult.success(count);
    }

}
