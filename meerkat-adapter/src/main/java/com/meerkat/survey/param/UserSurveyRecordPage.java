package com.meerkat.survey.param;

import com.meerkat.common.db.Page;
import com.meerkat.evaluation.survey.dao.object.UserSurveyRecordDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/8 14:03
 */
public class UserSurveyRecordPage {

    private Page page;

    private List<UserSurveyRecordDTO> records;

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public List<UserSurveyRecordDTO> getRecords() {
        return records;
    }

    public void setRecords(List<UserSurveyRecordDTO> records) {
        this.records = records;
    }

}
