package com.meerkat.order.vo;

import java.io.Serializable;

/**
 * @description: 订单写返回值
 * @author: pantaoling
 * @date: 2022/3/29
 */
public class OrderWriteResult implements Serializable {

    /**
     * 订单编号
     */
    private String orderNum;

    /**
     * 外部订单编号
     */
    private String outOrderNum;

    /**
     * 是否成功
     */
    private Boolean success;

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getOutOrderNum() {
        return outOrderNum;
    }

    public void setOutOrderNum(String outOrderNum) {
        this.outOrderNum = outOrderNum;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }
}
