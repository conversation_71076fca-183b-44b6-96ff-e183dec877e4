apiVersion: apps/v1
kind: Deployment
metadata:
  name: meerkat-adapter
  namespace: meerkat-web
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: meerkat-adapter
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: meerkat-adapter
    spec:
      imagePullSecrets:
        - name: coding-docker
      containers:
        - name: meerkat-adapter
          volumeMounts:
            - mountPath: /app/config
              name: config
            - mountPath: /plugins/skywalking-agent/config
              name: agent-config
          image: mgjk-docker.pkg.coding.net/meerkat/image/meerkat-origin
          command:
            - java
            - -javaagent:/plugins/skywalking-agent/skywalking-agent.jar
            - -Xmx400m
            - -jar
            - meerkat-adapter.jar
          ports:
            - containerPort: 8080
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /meerkat/adapter/isOK
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 30
            failureThreshold: 10
          readinessProbe:
            httpGet:
              path: /meerkat/adapter/isOK
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 30
            failureThreshold: 10
          resources:
            limits:
              cpu: 2000m
              memory: 8192Mi
            requests:
              cpu: 500m
              memory: 2048Mi
      volumes:
        - name: config
          configMap:
            name: meerkat-adapter-services-config
            items:
              - key: config
                path: application-dev.properties
              - key: base
                path: application.properties
        - name: agent-config
          configMap:
            name: meerkat-adapter-services-config
            items:
              - key: agent
                path: agent.config


