package com.meerkat.web.controller;

import com.meerkat.order.event.OrderPayEvent;
import com.meerkat.order.service.OrderFsmEngine;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
public class TestController {

    @Autowired
    private OrderFsmEngine orderFsmEngine;

    @ApiOperation("hello")
    @GetMapping("/isOK")
    public String hello() {
        return "isOK";
    }

    @GetMapping("/validateSendMsg")
    public void validateSendMsg() throws Exception {

        OrderPayEvent orderPayEvent = new OrderPayEvent();
        orderPayEvent.setOrderNum("20220702105803909000190");
        orderPayEvent.setSystem(1);
        orderPayEvent.setOperator(1L);
        orderPayEvent.setPayTime(new Date());
        orderPayEvent.setPayAmount(1L);
        orderFsmEngine.sendEvent(orderPayEvent);
    }
}
