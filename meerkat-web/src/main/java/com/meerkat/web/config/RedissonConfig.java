package com.meerkat.web.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @description:
 * @author: pantaoling
 * @date: 2021/10/20
 */
@Configuration
public class RedissonConfig {

    @Value("${spring.redis.host}")
    private String redisIp;

    @Value("${spring.redis.port}")
    private String port;

    @Value("${spring.redis.password}")
    private String password;

    @Value("${spring.redis.database}")
    private Integer database;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer() // 使用单机模式
                .setAddress("redis://" + redisIp + ":" + port)
                .setKeepAlive(true)
                // 设置1秒钟ping一次来维持连接
                .setPingConnectionInterval(1000)
                .setPassword(password)
                .setDatabase(database);
        return Redisson.create(config);
    }
}
