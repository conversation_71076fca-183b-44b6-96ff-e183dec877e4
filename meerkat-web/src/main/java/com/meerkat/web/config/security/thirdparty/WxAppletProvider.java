package com.meerkat.web.config.security.thirdparty;

import cn.hutool.core.util.ObjectUtil;
import com.meerkat.common.exception.BizException;
import com.meerkat.auth.component.token.DefaultAuthenticationToken;
import com.meerkat.auth.model.MeerkatUserDetails;
import com.meerkat.auth.service.UserDetailExpandService;
import com.meerkat.user.enums.UserTypeEnum;
import com.meerkat.user.model.User;
import com.meerkat.user.service.LoginService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <p>
 * 微信认证器
 * </p>
 *
 * <AUTHOR>
 * @since 2022/2/25 15:41
 */
@Component
public class WxAppletProvider implements AuthenticationProvider {

    private static final Logger LOG = LoggerFactory.getLogger(WxAppletProvider.class);

    @Autowired
    private UserDetailExpandService userDetailExpandService;

    @Autowired
    private LoginService loginService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        DefaultAuthenticationToken token = (DefaultAuthenticationToken) authentication;
        MeerkatUserDetails userDetails = null;
        try {
            userDetails = (MeerkatUserDetails) userDetailExpandService.loadUserByUsername((String) token.getPrincipal(),
                    token.getUserTypeEnum(), token.getSystemEnum());
        }catch (BizException e){
            LOG.info("新用户登录:{}", token.getPrincipal());
        }

        if (Objects.isNull(userDetails)){
            // 注册账号
            User user = loginService.register(buildUser(token));
            if (ObjectUtil.isEmpty(user)) {
                // 注册失败
                throw new RuntimeException("注册失败：" + token);
            }
            userDetails = (MeerkatUserDetails) userDetailExpandService.loadUserByUsername((String) token.getPrincipal(),
                    UserTypeEnum.MOBILE_CHECK, token.getSystemEnum());
        }
        DefaultAuthenticationToken tokenResult = new DefaultAuthenticationToken(userDetails, token.getSystemEnum(), token.getUserTypeEnum(), token.getAuthorities());
        tokenResult.setAuth(token.getAuth());
        tokenResult.setDetails(userDetails.getUser().getId());
        tokenResult.setAuthenticated(true);
        return tokenResult;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return false;
    }

    private User buildUser(DefaultAuthenticationToken token) {
        User user = new User();
        user.setSystem(token.getSystemEnum().getCode());
        user.setMobile((String) token.getPrincipal());
        return user;
    }
}
