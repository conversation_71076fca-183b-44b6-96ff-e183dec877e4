package com.meerkat.web.config;

import com.alibaba.csp.sentinel.adapter.spring.webmvc.callback.BlockExceptionHandler;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.authority.AuthorityException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowException;
import com.alibaba.fastjson.JSON;
import com.meerkat.common.api.BaseBizCodeEnum;
import com.meerkat.common.api.CommonResult;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class SentinelExceptionHandler implements BlockExceptionHandler {

    @Override
    public void handle(HttpServletRequest httpServletRequest, HttpServletResponse response, BlockException e) throws Exception {
        CommonResult result = new CommonResult();
        if (e instanceof FlowException) {
            result = CommonResult.failed(BaseBizCodeEnum.SENTINEL_FLOW);
        } else if (e instanceof ParamFlowException) {
            result = CommonResult.failed(BaseBizCodeEnum.SENTINEL_PARAM_FLOW);
        } else if (e instanceof DegradeException) {
            result = CommonResult.failed(BaseBizCodeEnum.SENTINEL_DEGRADE);
        } else if (e instanceof AuthorityException) {
            result = CommonResult.failed(BaseBizCodeEnum.SENTINEL_AUTHORITY);
        }
        response.setContentType("application/json;charset=utf-8");
        response.getWriter().println(JSON.toJSON(result));
    }
}
