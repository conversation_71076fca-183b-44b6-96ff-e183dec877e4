package com.meerkat.web.util;

import com.meerkat.auth.enums.SecurityExceptionEnum;
import com.meerkat.auth.model.MeerkatUserDetails;
import com.meerkat.common.exception.BizException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * @description:
 * @author: pantaoling
 * @date: 2021/9/24
 */
public class LoginUtil {

    /**
     * 获取当前登录用户
     *
     * @return
     */
    public static MeerkatUserDetails getCurrentUser() {
        SecurityContext ctx = SecurityContextHolder.getContext();
        Authentication auth = ctx.getAuthentication();
        if (auth != null && auth.getPrincipal() instanceof MeerkatUserDetails) {
            return (MeerkatUserDetails) auth.getPrincipal();
        }
        throw new BizException(SecurityExceptionEnum.TOKEN_ERROR);
    }
}
