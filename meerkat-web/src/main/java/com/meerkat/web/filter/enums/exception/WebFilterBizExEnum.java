package com.meerkat.web.filter.enums.exception;

import com.meerkat.common.api.BizCode;

/**
 * <AUTHOR>
 */
public enum WebFilterBizExEnum implements BizCode {

    /**
     * 域名错误
     */
    DOMAIN_ERROR("WEB_EX_DOMAIN_ERROR_001", "{0}"),

    ;

    private final String code;

    private final String message;

    WebFilterBizExEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
