package com.meerkat.hm.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @program meerkat-origin
 * @description:
 * @author: fandongdong
 * @create: 2022/09/02 17:39
 */
@ApiModel
public class HmUserInfoBaseEditParam implements Serializable {

    @ApiModelProperty(value = "生日 (yyyy-MM-dd)")
    //@Size(max = 10, min = 10, message = "请按格式输入生日,如2022-01-01")
    private String birthday;
    @ApiModelProperty(value = "身高")
    //@Min(value = 1, message = "请输入正确的身高")
    //@Max(value = 300, message = "请输入正确的身高")
    private Float height;
    @ApiModelProperty(value = "体重")
    //@Min(value = 1, message = "请输入正确的体重")
    //@Max(value = 300, message = "请输入正确的体重")
    private Float weight;
    @ApiModelProperty(value = "腰围")
    //@Min(value = 1, message = "请输入正确的腰围")
    //@Max(value = 500, message = "请输入正确的腰围")
    private Float waist;
    @ApiModelProperty(value = "臀围")
    //@Min(value = 1, message = "请输入正确的臀围")
    //@Max(value = 500, message = "请输入正确的臀围")
    private Float hipline;

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public Float getHeight() {
        return height;
    }

    public void setHeight(Float height) {
        this.height = height;
    }

    public Float getWeight() {
        return weight;
    }

    public void setWeight(Float weight) {
        this.weight = weight;
    }

    public Float getWaist() {
        return waist;
    }

    public void setWaist(Float waist) {
        this.waist = waist;
    }

    public Float getHipline() {
        return hipline;
    }

    public void setHipline(Float hipline) {
        this.hipline = hipline;
    }
}

