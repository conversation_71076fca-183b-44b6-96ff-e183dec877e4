package com.meerkat.hm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @program meerkat-origin
 * @description:
 * @author: fandongdong
 * @create: 2022/09/02 16:09
 */
@ApiModel("HmPrivilegeVO")
public class HmPrivilegeVO {

    @ApiModelProperty("权益id")
    private Long id;
    @ApiModelProperty("权益名称")
    private String name;
    @ApiModelProperty("权益标题")
    private String title;
    @ApiModelProperty("权益概要")
    private String summary;
    @ApiModelProperty("权益内容")
    private String content;
    @ApiModelProperty("权益介绍")
    private String introduce;
    @ApiModelProperty("权益图片地址")
    private String picUrl;
    @ApiModelProperty("权益备注")
    private String remark;
    @ApiModelProperty("权益类型：1:疫苗权益,2:体检权益,3:健管权益,4:免费权益")
    private Integer type;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getIntroduce() {
        return introduce;
    }

    public void setIntroduce(String introduce) {
        this.introduce = introduce;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
