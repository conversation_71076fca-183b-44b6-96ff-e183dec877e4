package com.meerkat.hm.feishu.mq;

import cn.hutool.core.net.NetUtil;
import com.meerkat.notify.consumer.RabbitConsumerDailyConfig;
import com.meerkat.notify.listener.AbstractMessageListener;
import com.meerkat.order.enums.MQConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OrderConsumerConfig
 * @description
 * @createTime 2022/09/19 10:08
 */
@Configuration
public class HMConsumerConfig extends RabbitConsumerDailyConfig {
    @Autowired
    private HMMessageListener orderMessageListener;

    @Override
    public String queueName() {
        return HMMqEnum.MIDDLE_PLATFORM_ORDER_HM_QUEUE.getCode();
    }

    @Override
    public AbstractMessageListener messageListener() {
        return orderMessageListener;
    }

    @Override
    public String topicExchangeName() {
        return MQConstants.EXCHANGE;
    }

    @Override
    public String producerIp() {
        return NetUtil.getLocalhost().getHostAddress();
    }

    @Override
    public List<String> routingKeys() {
        // 监听支付完成
        return List.of(MQConstants.RoutingKey.MIDDLE_PLATFORM_ORDER_PAY);
    }

}
