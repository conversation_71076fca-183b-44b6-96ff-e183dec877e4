package com.meerkat.coupon.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @program meerkat-origin
 * @description:
 * @author: fandong<PERSON>
 * @create: 2022/10/09 10:17
 */
@ApiModel("RedeemCouponParam")
public class RedeemCouponParam {

    /**
     * 兑换码
     */
    @ApiModelProperty("兑换码")
    private String couponRedemptionCode;

    public String getCouponRedemptionCode() {
        return couponRedemptionCode;
    }

    public void setCouponRedemptionCode(String couponRedemptionCode) {
        this.couponRedemptionCode = couponRedemptionCode;
    }
}
