package com.meerkat.coupon.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @program meerkat-origin
 * @description: 领取优惠券参数
 * @author: fan<PERSON><PERSON>
 * @create: 2022/10/09 10:17
 */
@ApiModel("ReceiveCouponParam")
public class ReceiveCouponParam {

    /**
     * 优惠券批次
     */
    @ApiModelProperty("优惠券批次")
    private List<Long> templateIds;

    /**
     * 机构信息
     */
    @ApiModelProperty("机构id")
    private Long chorgaId;


    public List<Long> getTemplateIds() {
        return templateIds;
    }

    public void setTemplateIds(List<Long> templateIds) {
        this.templateIds = templateIds;
    }

    public Long getChorgaId() {
        return chorgaId;
    }

    public void setChorgaId(Long chorgaId) {
        this.chorgaId = chorgaId;
    }
}
