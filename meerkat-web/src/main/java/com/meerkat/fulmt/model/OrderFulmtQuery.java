package com.meerkat.fulmt.model;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.meerkat.common.db.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 履约单查询入参
 * @date 2022/4/21 11:31
 */
@ApiModel("履约单查询")
public class OrderFulmtQuery {


    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNum;


    /**
     * 履约机构名称 模糊查询
     */
    @ApiModelProperty("履约机构名称")
    private String fulmtOrgaName;

    /**
     * 履约状态 0=待预约 1=已预约 2=待二方核销 3=待机构确认 4=待核销 5=待改期  6=已履约 7=已取消 8= 改期待定
     */
    @ApiModelProperty("履约状态 0=待预约 1=已预约 2=待二方核销 3=待机构确认 4=待核销 5=待改期  6=已履约 7=已取消 8= 改期待定")
    private Integer state;

    /**
     * 状态列表
     */
    @ApiModelProperty("履约状态 0=待预约 1=已预约 2=待二方核销 3=待机构确认 4=待核销 5=待改期  6=已履约 7=已取消 8= 改期待定")
    private List<Integer> stateList;
    /**
     * 筛选开始时间
     */
    @ApiModelProperty("筛选开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreatedStart;

    /**
     * 筛选结束时间
     */
    @ApiModelProperty("筛选结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreatedEnd;

    /**
     * 履约人姓名 模糊查询
     */
    @ApiModelProperty("履约人姓名")
    private String acceptorName;

    /**
     * 履约人手机号 模糊查询
     */
    @ApiModelProperty("履约人手机号")
    private String acceptorMobile;

    /**
     * 销售渠道
     */
    @ApiModelProperty("销售渠道Id")
    private Long chorgaId;

    @ApiModelProperty("分页")
    private Page page;

    /**
     * 销售渠道
     */
    @ApiModelProperty("销售渠道名称")
    private String chorgaName;

    /**
     * 销售渠道 list
     */
    private List<Long> chorgaIdList;

    /**
     * 城市信息
     */
    @ApiModelProperty("城市信息")
    private String cityName;

    /**
     * 履约时间时间
     */
    @ApiModelProperty("履约开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date fulmtStartDate;

    /**
     * 履约时间时间
     */
    @ApiModelProperty("履约结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date fulmtEndDate;

    /**
     * 机构确认 0待确认 1已确认 -1确认失败
     */
    @ApiModelProperty("机构确认 0待确认 1已确认 -1确认失败 ")
    private Integer orgCheckStatus;


    /**
     * 父订单
     */
    @ApiModelProperty("父订单")
    private String parentOrderNum;

    /**
     * 订单类型 exam 体检 vaccine 疫苗 retail 实物
     */
    @ApiModelProperty("订单类型 exam 体检 vaccine 疫苗 retail 实物")
    private String bizCode;

    @ApiModelProperty("外部订单号")
    private String outOrderNum;

    /**
     * 履约暂停标签：0->未暂停；1->已挂起暂停
     */
    @ApiModelProperty("履约暂停标签：0->未暂停；1->已挂起暂停")
    private Integer fulmtFlag;

    /**
     * 履约单编号
     */
    @ApiModelProperty("履约单编号")
    private String fulmtNum;

    public String getFulmtNum() {
        return fulmtNum;
    }

    public void setFulmtNum(String fulmtNum) {
        this.fulmtNum = fulmtNum;
    }

    public List<Integer> getStateList() {
        return stateList;
    }

    public void setStateList(List<Integer> stateList) {
        this.stateList = stateList;
    }

    public Integer getFulmtFlag() {
        return fulmtFlag;
    }

    public void setFulmtFlag(Integer fulmtFlag) {
        this.fulmtFlag = fulmtFlag;
    }

    public String getOutOrderNum() {
        return outOrderNum;
    }

    public void setOutOrderNum(String outOrderNum) {
        this.outOrderNum = outOrderNum;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getParentOrderNum() {
        return parentOrderNum;
    }

    public void setParentOrderNum(String parentOrderNum) {
        this.parentOrderNum = parentOrderNum;
    }
    @Override
    public String toString() {
        return "OrderFulmtQuery{" + "orderNum='" + orderNum + '\'' + ", fulmtOrgaName='" + fulmtOrgaName + '\'' + ", state=" + state + ", " +
                "gmtCreatedStart=" + gmtCreatedStart + ", gmtCreatedEnd=" + gmtCreatedEnd + ", acceptorName='" + acceptorName + '\'' + ", " +
                "acceptorMobile='" + acceptorMobile + '\'' + ", chorgaId=" + chorgaId + ", page=" + page + ", chorgaName='" + chorgaName + '\'' + '}';
    }

    public List<Long> getChorgaIdList() {
        return chorgaIdList;
    }

    public void setChorgaIdList(List<Long> chorgaIdList) {
        this.chorgaIdList = chorgaIdList;
    }

    public String getChorgaName() {
        return chorgaName;
    }

    public void setChorgaName(String chorgaName) {
        this.chorgaName = chorgaName;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getFulmtOrgaName() {
        return fulmtOrgaName;
    }

    public void setFulmtOrgaName(String fulmtOrgaName) {
        this.fulmtOrgaName = fulmtOrgaName;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Date getGmtCreatedStart() {
        return gmtCreatedStart;
    }

    public void setGmtCreatedStart(Date gmtCreatedStart) {
        this.gmtCreatedStart = gmtCreatedStart;
    }

    public Date getGmtCreatedEnd() {
        return gmtCreatedEnd;
    }

    public void setGmtCreatedEnd(Date gmtCreatedEnd) {
        this.gmtCreatedEnd = gmtCreatedEnd;
    }

    public String getAcceptorName() {
        return acceptorName;
    }

    public void setAcceptorName(String acceptorName) {
        this.acceptorName = acceptorName;
    }

    public String getAcceptorMobile() {
        return acceptorMobile;
    }

    public void setAcceptorMobile(String acceptorMobile) {
        this.acceptorMobile = acceptorMobile;
    }

    public Long getChorgaId() {
        return chorgaId;
    }

    public void setChorgaId(Long chorgaId) {
        this.chorgaId = chorgaId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getOrgCheckStatus() {
        return orgCheckStatus;
    }

    public void setOrgCheckStatus(Integer orgCheckStatus) {
        this.orgCheckStatus = orgCheckStatus;
    }


    public Date getFulmtStartDate() {
        return fulmtStartDate;
    }

    public void setFulmtStartDate(Date fulmtStartDate) {
        this.fulmtStartDate = fulmtStartDate;
    }

    public Date getFulmtEndDate() {
        return fulmtEndDate;
    }

    public void setFulmtEndDate(Date fulmtEndDate) {
        this.fulmtEndDate = fulmtEndDate;
    }
}
