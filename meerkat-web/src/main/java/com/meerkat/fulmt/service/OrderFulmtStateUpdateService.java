package com.meerkat.fulmt.service;

import com.meerkat.fulfillment.fulfillment.model.OrderFulmt;
import com.meerkat.fulmt.model.OrderFulmtParam;

public interface OrderFulmtStateUpdateService {
    /**
     * 创建履约单相关流程
     *
     * @param orderFulmt orderFulmt
     * @param id id
     * @return int
     * <AUTHOR>
     * @date 2022/5/4 20:45
     */
    int creatOrder(OrderFulmt orderFulmt, Long id) throws Exception;

    /**
     * 工单创建代预约履约单
     *
     * @param orderFulmtParam orderFulmtParam
     * @return int
     * <AUTHOR>
     * @date 2022/5/4 20:45
     */

    int updateFulmt(OrderFulmtParam orderFulmtParam) throws Exception;


}
