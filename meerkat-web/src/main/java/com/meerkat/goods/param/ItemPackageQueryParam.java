package com.meerkat.goods.param;

import com.meerkat.shop.goods.enums.GoodsGenderEnum;
import com.meerkat.shop.goods.enums.GoodsMarriageStatusEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName ItemPackageQuery.java
 * @Description TODO
 * @createTime 2022-02-26 12:57:00
 */
public class ItemPackageQueryParam implements Serializable {

    private static final long serialVersionUID = -7665800801457460696L;

    private Long orgaId;

    private Long goodsId;

    private GoodsGenderEnum goodsGenderEnum;

    private GoodsMarriageStatusEnum goodsMarriageStatusEnum;

    public Long getOrgaId() {
        return orgaId;
    }

    public void setOrgaId(Long orgaId) {
        this.orgaId = orgaId;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public GoodsGenderEnum getGoodsGenderEnum() {
        return goodsGenderEnum;
    }

    public void setGoodsGenderEnum(GoodsGenderEnum goodsGenderEnum) {
        this.goodsGenderEnum = goodsGenderEnum;
    }

    public GoodsMarriageStatusEnum getGoodsMarriageStatusEnum() {
        return goodsMarriageStatusEnum;
    }

    public void setGoodsMarriageStatusEnum(GoodsMarriageStatusEnum goodsMarriageStatusEnum) {
        this.goodsMarriageStatusEnum = goodsMarriageStatusEnum;
    }
}
