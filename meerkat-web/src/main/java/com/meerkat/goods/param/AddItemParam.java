package com.meerkat.goods.param;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName AddItemCheckParam.java
 * @Description TODO
 * @createTime 2022-02-26 13:06:00
 */
public class AddItemParam implements Serializable {

    private static final long serialVersionUID = -5974583618086138510L;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 加项ID
     */
    private Long addItemId;

    /**
     * 取消的项目ID
     */
    private Long cancelItemId;

    /**
     * 选中的所有项目ID
     */
    private List<Long> checkedItemIds;

    /**
     * 加项包ID
     */
    private List<Long> itemPkgIds;

    /**
     * 商品内移除的项目ID
     */
    private List<Long> goodsRemoveItemIds;

    /**
     * 各加项包内移除的项目ID
     */
    private Map<Long, Long> itemPkgRemoveItemMap;

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public Long getAddItemId() {
        return addItemId;
    }

    public void setAddItemId(Long addItemId) {
        this.addItemId = addItemId;
    }

    public Long getCancelItemId() {
        return cancelItemId;
    }

    public void setCancelItemId(Long cancelItemId) {
        this.cancelItemId = cancelItemId;
    }

    public List<Long> getCheckedItemIds() {
        return checkedItemIds;
    }

    public void setCheckedItemIds(List<Long> checkedItemIds) {
        this.checkedItemIds = checkedItemIds;
    }

    public List<Long> getItemPkgIds() {
        return itemPkgIds;
    }

    public void setItemPkgIds(List<Long> itemPkgIds) {
        this.itemPkgIds = itemPkgIds;
    }

    public List<Long> getGoodsRemoveItemIds() {
        return goodsRemoveItemIds;
    }

    public void setGoodsRemoveItemIds(List<Long> goodsRemoveItemIds) {
        this.goodsRemoveItemIds = goodsRemoveItemIds;
    }

    public Map<Long, Long> getItemPkgRemoveItemMap() {
        return itemPkgRemoveItemMap;
    }

    public void setItemPkgRemoveItemMap(Map<Long, Long> itemPkgRemoveItemMap) {
        this.itemPkgRemoveItemMap = itemPkgRemoveItemMap;
    }
}
