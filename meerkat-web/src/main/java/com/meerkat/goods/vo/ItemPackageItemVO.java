package com.meerkat.goods.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName ItemPackageItemVO.java
 * @Description TODO
 * @createTime 2022-02-26 12:15:00
 */
@ApiModel("ItemPackageItemVO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ItemPackageItemVO implements Serializable {

    private Long id;

    /**
     * 加项包id
     */
    private Long itemPkgId;

    /**
     * 项目id
     */
    private Long itemId;

    /**
     * 是否必选: 1-必须 0-非必选
     */
    private Integer isNecessary;

    /**
     * 售价
     */
    private Long price;

    /**
     * 是否可选，1:可选，0：不可选
     */
    private Integer enableSelect;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 项目
     */
    private ItemVO itemVO;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getItemPkgId() {
        return itemPkgId;
    }

    public void setItemPkgId(Long itemPkgId) {
        this.itemPkgId = itemPkgId;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Integer getIsNecessary() {
        return isNecessary;
    }

    public void setIsNecessary(Integer isNecessary) {
        this.isNecessary = isNecessary;
    }

    public Long getPrice() {
        return price;
    }

    public void setPrice(Long price) {
        this.price = price;
    }

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public ItemVO getItemVO() {
        return itemVO;
    }

    public void setItemVO(ItemVO itemVO) {
        this.itemVO = itemVO;
    }

    public Integer getEnableSelect() {
        return enableSelect;
    }

    public void setEnableSelect(Integer enableSelect) {
        this.enableSelect = enableSelect;
    }
}
