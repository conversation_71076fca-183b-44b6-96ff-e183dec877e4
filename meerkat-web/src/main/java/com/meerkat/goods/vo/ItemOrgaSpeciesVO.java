package com.meerkat.goods.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ItemOrgaSpeciesVO
 * @description 机构下项目分类VO
 * @createTime 2022/5/10 19:53
 */
@ApiModel("ItemOrgaSpeciesVO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ItemOrgaSpeciesVO {

    @ApiModelProperty("机构下分类id")
    private Integer id;

    @ApiModelProperty("分类名称")
    private String name;

    @ApiModelProperty("拼音")
    private String pinyin;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("1.按专业分类 2. 按身体部位")
    private Integer type;

    @ApiModelProperty("排序")
    private Integer sequence;

    @ApiModelProperty("icon图标url")
    private String iconUrl;

    @ApiModelProperty("分类下项目")
    private List<ItemVO> itemVOS;

    @ApiModelProperty("互斥项目")
    private Map<Long, List<ItemVO>> conflictItemMap;

    @ApiModelProperty("项目依赖关系")
    private List<DependenceItemVO> dependenceItemVOS;

    @ApiModelProperty("同组项目")
    private Map<Integer, List<ItemVO>> sameGroupItemMap;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public List<ItemVO> getItemVOS() {
        return itemVOS;
    }

    public void setItemVOS(List<ItemVO> itemVOS) {
        this.itemVOS = itemVOS;
    }

    public Map<Long, List<ItemVO>> getConflictItemMap() {
        return conflictItemMap;
    }

    public void setConflictItemMap(Map<Long, List<ItemVO>> conflictItemMap) {
        this.conflictItemMap = conflictItemMap;
    }

    public List<DependenceItemVO> getDependenceItemVOS() {
        return dependenceItemVOS;
    }

    public void setDependenceItemVOS(List<DependenceItemVO> dependenceItemVOS) {
        this.dependenceItemVOS = dependenceItemVOS;
    }

    public Map<Integer, List<ItemVO>> getSameGroupItemMap() {
        return sameGroupItemMap;
    }

    public void setSameGroupItemMap(Map<Integer, List<ItemVO>> sameGroupItemMap) {
        this.sameGroupItemMap = sameGroupItemMap;
    }
}
