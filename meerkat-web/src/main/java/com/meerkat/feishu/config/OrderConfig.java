package com.meerkat.feishu.config;

/**
 * <AUTHOR>
 * @ClassName OrderConfig
 * @description 订单配置
 * @createTime 2022/5/23 12:34
 */
public class OrderConfig {

    /**
     * 本地机器人URL
     */
    public static final String WEB_HOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/76f44004-ead8-4f7c-9f0a-61e5fcfbca5c";
    /**
     * 线上机器人URL
     */
    public static final String PRO_WEB_HOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/459ce16a-2aa1-4cab-adbf-580af0361045";
    public static final String ORGANIZATION_NAME = "机构:";
    public static final String COMPANY_NAME = "单位名称:";
    public static final String GOODS_NAME = "套餐:";
    public static final String GOODS_PRICE = "套餐售价:";
    public static final String GOODS_DISPLAY_PRICE = "套餐标价:";
    public static final String GOODS_ITEM = "套餐内项目:";
    public static final String ADD_ITEM = "加项:";
    public static final String PRICE = "订单价格:";
    public static final String EXAM_DATE = "预约日期:";
    public static final String ACCEPTOR_NAME = "体检人姓名:";
    public static final String ACCEPTOR_GENDER = "性别:";
    public static final String ACCEPTOR_MARRIAGE_STATUS = "婚否:";
    public static final String ACCEPTOR_IDCARD = "证件号码:";
    public static final String AGE = "年龄:";
    public static final String ACCEPTOR_MOBILE = "体检人电话:";
    public static final String ORDER_NUM = "订单编号:";

}
