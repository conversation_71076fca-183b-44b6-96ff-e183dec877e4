package com.meerkat.feishu.mq;

import com.meerkat.notify.consumer.RabbitConsumerProConfig;
import com.meerkat.notify.listener.AbstractMessageListener;
import com.meerkat.trade.refund.enums.RefundMqConstants;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName RefundApplyConsumerProConfig
 * @description
 * @createTime 2022/8/10 20:53
 */
@Configuration
public class RefundApplyConsumerProConfig extends RabbitConsumerProConfig {

    @Autowired
    private RefundApplyMessageListener refundApplyMessageListener;

    @Override
    public String queueName() {
        return RefundApplyMqEnum.MIDDLE_PLATFORM_ORDER_PHYSICAL_REFUND_QUEUE.getCode();
    }

    @Override
    public AbstractMessageListener messageListener() {
        return refundApplyMessageListener;
    }

    @Override
    public List<String> routingKeys() {
        // 监听退款申请
        return List.of(RefundMqConstants.RoutingKey.REFUND_APPLY);
    }

    @Override
    public Exchange exchange() {
        return new TopicExchange(topicExchangeName());
    }

    @Override
    public String topicExchangeName() {
        return RefundMqConstants.EXCHANGE;
    }
}
