package com.meerkat.order.util;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meerkat.order.enums.OrderStateEnum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 订单状态映射
 * @author: pantaoling
 * @date: 2021/10/20
 */
public class OrderStatusMapperUtil {

    /**
     * 订单状态查询映射
     */
    public static class OrderListQuery {
        private static final Map<Integer, List<Integer>> QUERY_STATE_MAPPER = new HashMap<>();
        /**
         * 待付款
         */
        private static final List<Integer> UNPAID = Lists.newArrayList(OrderStateEnum.PAYING.getCode(), OrderStateEnum.INIT.getCode());

        private static final List<Integer> UNAPPOINT = Lists.newArrayList(OrderStateEnum.PAID.getCode());

        /**
         * 待体检
         */
        private static final List<Integer> DOING = Lists.newArrayList(
                OrderStateEnum.APPOINT.getCode(),
                OrderStateEnum.APPOINT_SUBMIT.getCode(),
                OrderStateEnum.PAID.getCode(),
                OrderStateEnum.APPOINT_FAIL.getCode());
        /**
         * 已完成
         */
        private static final List<Integer> FINISH = Lists.newArrayList(OrderStateEnum.FINISHED.getCode());

        static {
            QUERY_STATE_MAPPER.put(State.UNPAID, UNPAID);
            QUERY_STATE_MAPPER.put(State.FINISH, FINISH);
            QUERY_STATE_MAPPER.put(State.DOING, DOING);
            QUERY_STATE_MAPPER.put(State.UNAPPOINT, UNAPPOINT);
        }

        private static class State {
            /**
             * 未付款
             */
            public static final Integer UNPAID = 0;

            /**
             * 待体检
             */
            public static final Integer DOING = 1;
            /**
             * /**
             * 已完成
             */
            public static final Integer FINISH = 2;

            /**
             * 待预约
             */
            public static final Integer UNAPPOINT = 3;

        }

        public static List<Integer> getMapperStates(List<Integer> states) {
            if (CollectionUtil.isEmpty(states)) {
                return null;
            }
            List<Integer> mapperStates = new ArrayList<>();
            for (Integer state : states) {
                List<Integer> mapperState = QUERY_STATE_MAPPER.get(state);
                if (!CollectionUtil.isEmpty(mapperState)) {
                    mapperStates.addAll(mapperState);
                }
            }
            return mapperStates;
        }
    }


    /**
     * 订单状态显示映射
     */
    public static class OrderStateShow {
        /**
         * 待付款
         */
        private static final List<Integer> UNPAID = Lists.newArrayList(OrderStateEnum.PAYING.getCode(), OrderStateEnum.INIT.getCode());
        /**
         * 待预约
         */
        private static final List<Integer> UN_APPOINT = Lists.newArrayList(OrderStateEnum.PAID.getCode());
        /**
         * 待体检
         */
        private static final List<Integer> UN_EXAM = Lists.newArrayList(OrderStateEnum.APPOINT.getCode(),
                OrderStateEnum.APPOINT_SUBMIT.getCode(),
                OrderStateEnum.APPOINT_FAIL.getCode());
        /**
         * 订单完成
         */
        private static final List<Integer> FINISH = Lists.newArrayList(OrderStateEnum.FINISHED.getCode());
        /**
         * 订单关闭
         */
        private static final List<Integer> CLOSED = Lists.newArrayList(OrderStateEnum.CLOSED.getCode());

        /**
         * 订单取消
         */
        private static final List<Integer> CANCEL = Lists.newArrayList(OrderStateEnum.CANCEL.getCode());


        public static class OrderState {
            /**
             * 前端显示的订单状态
             */
            public static final Integer UNPAID = 0;
            public static final Integer UN_APPOINT = 1;
            public static final Integer UN_EXAM = 2;
            public static final Integer FINISH = 3;
            public static final Integer CLOSED = 4;
            public static final Integer CANCEL = 8;
        }

        public static Integer getOrderVoState(Integer orderStates) {
            if (UNPAID.contains(orderStates)) {
                return OrderState.UNPAID;
            }
            if (UN_APPOINT.contains(orderStates)) {
                return OrderState.UN_APPOINT;
            }
            if (UN_EXAM.contains(orderStates)) {
                return OrderState.UN_EXAM;
            }
            if (FINISH.contains(orderStates)) {
                return OrderState.FINISH;
            }
            if (CLOSED.contains(orderStates)) {
                return OrderState.CLOSED;
            }
            if (CANCEL.contains(orderStates)) {
                return OrderState.CANCEL;
            }
            return null;
        }

    }




}
