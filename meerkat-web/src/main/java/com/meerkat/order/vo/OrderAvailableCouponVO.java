package com.meerkat.order.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OrderAvailableCouponVO.java
 * @Description
 */
@ApiModel("OrderAvailableCouponVO")
public class OrderAvailableCouponVO {

    /**
     * 机构id
     */
    @ApiModelProperty("机构id")
    private Long chorgaId;

    /**
     *活动id
     */
    @ApiModelProperty("活动id")
    private Integer activityId;
    /**
     *分销码
     */
    @ApiModelProperty("分销码")
    private String distributeCode;

    @ApiModelProperty("商品id")
    private List<OrderCreateGoodsInfo> goodsInfos;


    public Long getChorgaId() {
        return chorgaId;
    }

    public void setChorgaId(Long chorgaId) {
        this.chorgaId = chorgaId;
    }


    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    public String getDistributeCode() {
        return distributeCode;
    }

    public void setDistributeCode(String distributeCode) {
        this.distributeCode = distributeCode;
    }

    public List<OrderCreateGoodsInfo> getGoodsInfos() {
        return goodsInfos;
    }

    public void setGoodsInfos(List<OrderCreateGoodsInfo> goodsInfos) {
        this.goodsInfos = goodsInfos;
    }
}
