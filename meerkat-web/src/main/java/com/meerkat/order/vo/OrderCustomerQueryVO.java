package com.meerkat.order.vo;

import com.meerkat.common.db.Page;

import java.util.List;

/**
 * @description: 订单查询请求
 * @author: pantaoling
 * @date: 2021/10/20
 */
public class OrderCustomerQueryVO {

    private Page page;

    private List<Integer> mapperStates;

    private Integer type;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public List<Integer> getMapperStates() {
        return mapperStates;
    }

    public void setMapperStates(List<Integer> mapperStates) {
        this.mapperStates = mapperStates;
    }
}
