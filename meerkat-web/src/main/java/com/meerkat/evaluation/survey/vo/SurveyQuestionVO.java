package com.meerkat.evaluation.survey.vo;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/25 14:33
 */
public class SurveyQuestionVO {

    @ApiModelProperty("问卷题目id")
    private Long id;

    /**
     * 问题
     */
    @ApiModelProperty("问题")
    private String content;

    /**
     * 问题类型：1-单选， 2-多选， 3-文本填写， 4-地址获取， 5-位置获取， 6-身份信息， 7-承诺， 8-文本域
     */
    @ApiModelProperty("问题类型")
    private Integer type;

    /**
     * 题目顺序
     */
    @ApiModelProperty("题目顺序")
    private Integer sequence;

    /**
     * 0:非必答，1:必答
     */
    @ApiModelProperty("是否必答")
    private Integer mustAnswer;

    @ApiModelProperty("问题展示前置条件")
    private List<Long> conditionAnswerId;

    @ApiModelProperty("是否默认问题")
    private Integer isDefault;

    private List<SurveyQuestionAnswerVO> surveyQuestionAnswerVOList;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Integer getMustAnswer() {
        return mustAnswer;
    }

    public void setMustAnswer(Integer mustAnswer) {
        this.mustAnswer = mustAnswer;
    }

    public List<SurveyQuestionAnswerVO> getSurveyQuestionAnswerVOList() {
        return surveyQuestionAnswerVOList;
    }

    public void setSurveyQuestionAnswerVOList(List<SurveyQuestionAnswerVO> surveyQuestionAnswerVOList) {
        this.surveyQuestionAnswerVOList = surveyQuestionAnswerVOList;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<Long> getConditionAnswerId() {
        return conditionAnswerId;
    }

    public void setConditionAnswerId(List<Long> conditionAnswerId) {
        this.conditionAnswerId = conditionAnswerId;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }
}
