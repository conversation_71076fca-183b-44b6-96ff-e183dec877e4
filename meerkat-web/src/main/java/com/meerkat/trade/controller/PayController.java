package com.meerkat.trade.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.meerkat.common.api.CommonResult;
import com.meerkat.common.enums.PaymentTypeEnum;
import com.meerkat.common.enums.SystemEnum;
import com.meerkat.common.utils.GenerateNum;
import com.meerkat.order.enums.DiscountEnum;
import com.meerkat.order.enums.OrderBizExceptionEnum;
import com.meerkat.order.enums.OrderSelectEnum;
import com.meerkat.order.enums.OrderStateEnum;
import com.meerkat.order.event.OrderPayEvent;
import com.meerkat.order.event.OrderPayingEvent;
import com.meerkat.order.model.Order;
import com.meerkat.order.model.OrderPayment;
import com.meerkat.order.model.snapshot.GoodsSnapshot;
import com.meerkat.order.service.OrderFsmEngine;
import com.meerkat.order.service.OrderReadService;
import com.meerkat.smart.site.model.Site;
import com.meerkat.trade.gateway.enums.PayUniqueParamEnum;
import com.meerkat.trade.pay.dto.PayRequest;
import com.meerkat.trade.pay.dto.PayResponse;
import com.meerkat.trade.pay.dto.PaymentDetail;
import com.meerkat.trade.pay.enums.PayChannelEnum;
import com.meerkat.trade.pay.enums.TradeStateEnum;
import com.meerkat.trade.pay.model.TradePayRecord;
import com.meerkat.trade.pay.service.UnifyPayService;
import com.meerkat.trade.pay.service.inner.TradePayRecordService;
import com.meerkat.trade.vo.PayRequestVO;
import com.meerkat.web.util.LoginUtil;
import com.meerkat.web.util.SiteThreadLocalUtil;
import io.swagger.annotations.Api;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @description: 支付请求
 * @author: pantaoling
 * @date: 2021/10/11
 */
@RequestMapping("/pay")
@Api(tags = "PayController")
@RestController
public class PayController {
    private static final Logger logger = LoggerFactory.getLogger(PayController.class);

    @Autowired
    private UnifyPayService unifyPayService;

    @Autowired
    private OrderReadService orderReadService;

    @Autowired
    private OrderFsmEngine orderFsmEngine;

    @Autowired
    private TradePayRecordService tradePayRecordService;


    /**
     * 统一支付接口
     *
     * @param payRequestVO 支付请求
     * @return
     * @throws Exception
     */
    @PostMapping("/unifyPay")
    public CommonResult<PayResponse> unifyPay(@RequestBody PayRequestVO payRequestVO) throws Exception {
        Long customerId = LoginUtil.getCurrentUser().getUser().getCustomerId();
        Order order = orderReadService.loadByOrderNum(payRequestVO.getOrderNum(), OrderSelectEnum.GOODS);
        if (order == null) {
            return CommonResult.failed(OrderBizExceptionEnum.ORDER_NOT_EXIST);
        }

        // 只有订单状态在支付中和未支付时可发起支付
        if (!Objects.equals(order.getState(), OrderStateEnum.PAYING.getCode())
                && !Objects.equals(order.getState(), OrderStateEnum.INIT.getCode())) {
            return CommonResult.failed(OrderBizExceptionEnum.ORDER_STATE_ERROR,"请不要重新发起支付");
        }

        Long calculateNeedToPay = calculateNeedToPay(order);
        //只有有优惠券才能支付0元
        if (calculateNeedToPay == 0 && !MapUtil.isEmpty(order.getDiscountAmount())) {
            PayResponse payResponse = new PayResponse();
            payResponse.setSuccess(Boolean.TRUE);
            payResponse.setDone(Boolean.TRUE);
            payResponse.setPayAmount(0L);
            payFinishEvent(customerId, order, payResponse);
            return CommonResult.success(payResponse);
        }
        Site site = SiteThreadLocalUtil.get();
        PayResponse pay = unifyPayService.pay(buildPayRequest(payRequestVO, order, site));

        // 已经支付金额需要同步 支付中
        if (pay.isSuccess() && !pay.isDone()) {
            payingEvent(customerId, order, pay);
        } else if (pay.isSuccess() && pay.isDone()) {
            payFinishEvent(customerId, order, pay);
        }
        return CommonResult.success(pay);
    }

    /**
     * 支付全部结束，无后续流程事件
     *
     * @param customerId
     * @param order
     * @param pay
     * @return void
     * <AUTHOR>
     * @date 2022/3/22 12:00 下午
     */
    private void payFinishEvent(Long customerId, Order order, PayResponse pay) throws Exception {
        OrderPayEvent event = new OrderPayEvent();
        event.setOrderNum(order.getOrderNum());
        event.setPayAmount(pay.getPayAmount());
        event.setPayTime(new Date());
        event.setOperator(customerId);
        event.setSystem(SystemEnum.C.getCode());
        event.setDiscountAmount(pay.getDiscountAmount());
        logger.info("orderPayEvent param:{}", JSONUtil.toJsonStr(event));
        orderFsmEngine.sendEvent(event);
    }

    /**
     * 支付需要后续回调/轮训更正状态事件
     *
     * @param customerId
     * @param order
     * @return void
     * <AUTHOR>
     * @date 2022/3/22 11:58 上午
     */
    private void payingEvent(Long customerId, Order order, PayResponse payResponse) throws Exception {
        OrderPayingEvent event = new OrderPayingEvent();
        event.setOrderNum(order.getOrderNum());
        event.setSystem(SystemEnum.C.getCode());
        event.setOperator(customerId);
        Map<String, Long> newPayDiscountAmount = payResponse.getDiscountAmount();
        event.setDiscountAmount(newPayDiscountAmount);
        logger.info("orderPayingEvent param:{}", JSONUtil.toJsonStr(event));
        orderFsmEngine.sendEvent(event);
    }


    /**
     * 构建支付请求
     *
     * @param payRequestVO 请求vo
     * @param order        当前订单
     * @return 支付请求
     */
    private PayRequest buildPayRequest(PayRequestVO payRequestVO, Order order, Site site) {
        PayRequest payRequest = new PayRequest();
        List<PaymentDetail> paymentDetails = Lists.newArrayList();

        GoodsSnapshot goodsSnapshot = order.getGoodsSnapshot();

        //兼容小程序
        if (CollectionUtil.isEmpty(payRequestVO.getPayInfos())) {
            PaymentDetail paymentDetail = new PaymentDetail();
            paymentDetail.setPaymentType(PaymentTypeEnum.THIRD_PARTY_ONLINE);
            paymentDetail.setPayProduct(payRequestVO.getPayType());
            paymentDetail.setPayChannel(PayChannelEnum.WX.getCode());
            paymentDetail.setPaymentSubAccount(payRequestVO.getOpenid());
            paymentDetail.setPayAmount(calculateNeedToPay(order));
            paymentDetails.add(paymentDetail);
        } else {
            paymentDetails = getPaymentDetails(payRequestVO, order);
        }

        // 构建特定支付参数
        Map<PayUniqueParamEnum, String> payUniqueParamMap = buildPayUniqueParams(payRequestVO);

        // 构建PayRequest
        payRequest.setOrderNum(payRequestVO.getOrderNum())
                .setGoodsName(goodsSnapshot.getName())
                .setGoodsDesc(goodsSnapshot.getDescription())
                .setOrganizationId(order.getOrganizationId())
                .setOperator(LoginUtil.getCurrentUser().getUser().getId())
                .setExtraCommonParam(JSONUtil.toJsonStr(payRequestVO))
                .setPayScene(payRequestVO.getPayScene())
                .setChorgaId(site.getBusinessId())
                .setChorgaType(site.getSiteType())
                .setSystem(SystemEnum.C)
                .setPaymentDetails(paymentDetails)
                .setPayUniqueParam(payUniqueParamMap)
                .setReturnUrl(payRequestVO.getReturnUrl())
        ;

        return payRequest;
    }

    @NotNull
    private Map<PayUniqueParamEnum, String> buildPayUniqueParams(PayRequestVO payRequestVO) {
        Map<PayUniqueParamEnum, String> payUniqueParamMap = new HashMap<>();
        payUniqueParamMap.put(PayUniqueParamEnum.PAY_CLIENT_IP, payRequestVO.getPayClientIp());
        return payUniqueParamMap;
    }

    private List<PaymentDetail> getPaymentDetails(PayRequestVO payRequestVO, Order order) {
        //todo:不应该由前端传参 需要提供统一支付计算服务支付信息这一块需要改
        List<OrderPayment> paymentSnapshotList = order.getPaymentList();

        List<PaymentDetail> paymentDetails = Lists.newArrayList();
        // 提取PaymentDetails
        paymentSnapshotList.forEach(paymentSnapshot -> {

            // 若本次为卡优先特殊处理
            if (Objects.equals(PaymentTypeEnum.PLATFORM_CARD, paymentSnapshot.getPaymentType())) {
                PaymentDetail paymentDetail = cardCheck(order, paymentSnapshot);
                if (paymentDetail != null) {
                    paymentDetails.add(paymentDetail);
                }
                return;
            }

            // 匹配符合的明细
            for (PayRequestVO.PayInfo payInfo : payRequestVO.getPayInfos()) {
                if (Objects.equals(payInfo.getPaymentType(), paymentSnapshot.getPaymentType().getCode())) {
                    PaymentDetail paymentDetail = new PaymentDetail();
                    paymentDetail.setPayProduct(payInfo.getPayProduct());
                    paymentDetail.setPaymentSubAccount(payInfo.getPaymentSubAccount());
                    paymentDetail.setPayChannel(payInfo.getPayChannel());
                    paymentDetail.setPaymentType(paymentSnapshot.getPaymentType());
                    paymentDetail.setPayAmount(paymentSnapshot.getAmount());
                    paymentDetails.add(paymentDetail);
                }
            }
        });
        return paymentDetails;
    }

    /**
     * fixme: 【强制】若为卡的情况下反查流水，临时bug这样改，必须进行修改
     *
     * @param order
     * @param paymentSnapshot
     * @return PaymentDetail
     * <AUTHOR>
     * @date 2022/4/6 3:50 下午
     */
    private PaymentDetail cardCheck(Order order, OrderPayment paymentSnapshot) {

        List<TradePayRecord> tradePayRecords = tradePayRecordService.listByRefOrderNum(order.getOrderNum());

        // 是否需要添加卡的paymentDetail信息
        boolean flag = true;
        for (TradePayRecord tradePayRecord : tradePayRecords) {

            // 若存在卡的流水且状态未成功，则卡一支付，不需要添加卡的paymentdetail
            if (Objects.equals(PaymentTypeEnum.PLATFORM_CARD, tradePayRecord.getPaymentType())
                    || Objects.equals(tradePayRecord.getPayStatus(), TradeStateEnum.SUCCESSFUL)) {
                flag = false;
            }
        }

        if (flag) {
            PaymentDetail paymentDetail = new PaymentDetail();
            paymentDetail.setPaymentSubAccount(paymentSnapshot.getSubAccount());
            paymentDetail.setPayChannel(PayChannelEnum.MEERKAT.getCode());
            paymentDetail.setPaymentType(paymentSnapshot.getPaymentType());
            paymentDetail.setPayAmount(paymentSnapshot.getAmount());
            return paymentDetail;
        }
        return null;
    }


    /**
     * 计算应该支付的金额
     *
     * @param order 订单
     * @return 应付金额
     */
    private Long calculateNeedToPay(Order order) {
        Map<String, Long> discountAmountMap = order.getDiscountAmount();
        if (discountAmountMap == null) {
            return order.getTotalAmount();
        }

        Long discountAmount = 0L;
        for (DiscountEnum discountAmountEnum : DiscountEnum.values()) {
            //优惠抵扣的不计入应付金额
            if (discountAmountEnum.getDiscountType() == 1) {
                continue;
            }
            Long aLong = discountAmountMap.get(discountAmountEnum.name());
            if (aLong != null) {
                discountAmount += aLong;
            }
        }
        return Math.max(order.getTotalAmount() - discountAmount, 0);
    }


    /**
     * 想提供一个单独仅提供支付服务的功能，不和订单系统耦合，
     * 那么后续可以单独做一个聚合支付服务为外部提供服务
     *
     * @param payRequestVO
     * @return
     */
    @PostMapping("/outUnifyPay")
    public CommonResult<PayResponse> outUnifyPay(@RequestBody PayRequestVO payRequestVO) {
        Site site = SiteThreadLocalUtil.get();
        PayRequest payRequest = new PayRequest();
        //这里需要生成订单编号
        payRequest.setOrderNum(GenerateNum.getNum(GenerateNum.tradeSource, "yyMMddHHmmss"))
                .setPayScene(payRequestVO.getPayScene())
                .setChorgaId(site.getBusinessId())
                .setChorgaType(site.getSiteType())
                .setGoodsName(payRequestVO.getGoodsName())
                .setGoodsDesc(payRequestVO.getDescription())
                .setExtraCommonParam(JSONUtil.toJsonStr(payRequestVO))
                .setOrganizationId(site.getBusinessId())
                .setPayUniqueParam(buildPayUniqueParams(payRequestVO))
                .setSystem(SystemEnum.C)
                .setOpenId(payRequestVO.getOpenid())
                .setPaymentDetails(buildPaymentDetails(payRequestVO))
                .setIsOfflineCollection(1)
                .setTradeSource(1)
                .setReturnUrl(payRequestVO.getReturnUrl());
        PayResponse pay = unifyPayService.pay(payRequest);
        return CommonResult.success(pay);
    }

    private List<PaymentDetail> buildPaymentDetails(PayRequestVO payRequestVO) {
        List<PaymentDetail> paymentDetails = Lists.newArrayList();
        // 匹配符合的明细
        for (PayRequestVO.PayInfo payInfo : payRequestVO.getPayInfos()) {
            PaymentDetail paymentDetail = new PaymentDetail();
            paymentDetail.setPayProduct(payInfo.getPayProduct());
            paymentDetail.setPaymentSubAccount(payInfo.getPaymentSubAccount());
            paymentDetail.setPayChannel(payInfo.getPayChannel());
            paymentDetail.setPaymentType(PaymentTypeEnum.THIRD_PARTY_ONLINE);
            paymentDetail.setPayAmount(payRequestVO.getPayPrice());
            paymentDetails.add(paymentDetail);
        }
        return paymentDetails;
    }

}
