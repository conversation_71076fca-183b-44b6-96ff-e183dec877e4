package com.meerkat.task;

import com.meerkat.trade.refund.service.UnifyRefundService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: pantaoling
 * @date: 2021/11/25
 */
 @Component
public class OrderRefundJob {
    private static final Logger LOG = LoggerFactory.getLogger(OrderCloseJob.class);


    @Autowired
    private UnifyRefundService unifyRefundService;


    @XxlJob("OrderRefundJob")
    public void refundJob() {
        LOG.info("订单退款任务开始");
        unifyRefundService.refundJob();
    }

}
