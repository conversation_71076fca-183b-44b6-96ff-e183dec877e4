package com.meerkat.task;

import cn.hutool.core.date.DateUtil;
import com.meerkat.base.enums.OrgaBizTypeEnum;
import com.meerkat.common.db.Page;
import com.meerkat.common.db.PageView;
import com.meerkat.common.enums.ChorgaTypeEnum;
import com.meerkat.fulfillment.fulfillment.model.FulmtGoodsRelationValue;
import com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue;
import com.meerkat.fulfillment.fulfillment.service.FulmtGoodsRelationService;
import com.meerkat.fulfillment.fulfillment.service.OrderFulmtService;
import com.meerkat.order.dto.OrderExt;
import com.meerkat.order.enums.OrderBizCodeEnum;
import com.meerkat.order.enums.OrderSelectEnum;
import com.meerkat.order.enums.OrderStateEnum;
import com.meerkat.order.enums.OrderTagQueryEnum;
import com.meerkat.order.model.Order;
import com.meerkat.order.param.OrderTimeQuery;
import com.meerkat.order.service.OrderOperateService;
import com.meerkat.order.service.OrderReadService;
import com.meerkat.order.service.OrderWriteService;
import com.meerkat.shop.goods.model.GoodsCost;
import com.meerkat.shop.goods.service.GoodsCostService;
import com.meerkat.smart.channel.model.Channel;
import com.meerkat.smart.channel.param.ChannelQuery;
import com.meerkat.smart.channel.service.ChannelService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单打标 <br><br>
 * 打标规则：<br>
 * 1.每日8点半执行定时任务，对订单和履约单打标<br>
 * 2.订单支付金额*（1-渠道扣点）-订单商品关联履约商品最高成本<0，打标亏损预警<br>
 * 3.订单支付金额*（1-渠道扣点）-预约机构商品成本<0，打标亏损<br>
 * 4.订单&履约单导出新增标签，支持导出亏损预警、亏损<br>
 *
 * <AUTHOR>
 * @date 2022/10/25 17:29
 */
@Component
public class OrderMarkLabelJob {

    private static final Logger LOG = LoggerFactory.getLogger(OrderMarkLabelJob.class);

    public static final int PAGE_SIZE = 1000;

    @Resource
    private OrderReadService orderReadService;

    @Resource
    private OrderWriteService orderWriteService;

    @Resource
    private FulmtGoodsRelationService fulmtGoodsRelationService;

    @Resource
    private GoodsCostService goodsCostService;

    @Resource
    private ChannelService channelService;

    @Resource
    private OrderOperateService orderOperateService;

    @Resource
    private OrderFulmtService orderFulmtService;

    @XxlJob("OrderMarkLabel")
    public void action() {
        LOG.info("开始执行订单打标任务");
        //已支付订单
        OrderTimeQuery paidOrderQuery = new OrderTimeQuery();
        paidOrderQuery.setCreateStartTime(DateUtil.parse("2022-09-01"));
        paidOrderQuery.setCreateEndTime(DateUtil.date());
        paidOrderQuery.setChorgaType(ChorgaTypeEnum.CHANNEL.getCode());
        paidOrderQuery.setStates(List.of(OrderStateEnum.PAID.getCode(), OrderStateEnum.APPOINT.getCode(), OrderStateEnum.APPOINT_FAIL.getCode(), OrderStateEnum.APPOINT_SUBMIT.getCode()));
        paidOrderQuery.setBizCodes(List.of(OrderBizCodeEnum.VACCINE.getName()));

        Page page = new Page(1, PAGE_SIZE);
        PageView<Order> pageView = orderReadService.listByTimeQuery(paidOrderQuery, page, OrderSelectEnum.GOODS);
        long num = pageView.getPage().getRowCount() / PAGE_SIZE;
        if (pageView.getPage().getRowCount() % PAGE_SIZE != 0) {
            num++;
        }
        LOG.info("亏损预警标签-数据条数：" + pageView.getPage().getRowCount() + "，数据页数：" + num);
        try {
            for (int i = 1; i <= num; i++) {
                page.setCurrentPage(i);
                if (i != 1) {
                    pageView = orderReadService.listByTimeQuery(paidOrderQuery, page, OrderSelectEnum.GOODS);
                }
                List<Order> records = pageView.getRecords();
                //打标
                markLossWarningLabel(records);
            }
        } catch (Exception e) {
            e.printStackTrace();
            LOG.error("订单预警标签打标失败", e);
        }


        //已预约订单
        OrderTimeQuery appointmentQuery = new OrderTimeQuery();
        appointmentQuery.setCreateStartTime(DateUtil.parse("2022-09-01"));
        appointmentQuery.setCreateEndTime(DateUtil.date());
        appointmentQuery.setStates(List.of(OrderStateEnum.APPOINT.getCode(), OrderStateEnum.APPOINT_FAIL.getCode(), OrderStateEnum.APPOINT_SUBMIT.getCode()));
        appointmentQuery.setBizCodes(List.of(OrderBizCodeEnum.VACCINE.getName()));
        Page page2 = new Page(1, PAGE_SIZE);
        PageView<Order> pageView2 = orderReadService.listByTimeQuery(appointmentQuery, page2, OrderSelectEnum.GOODS);
        long num2 = pageView2.getPage().getRowCount() / PAGE_SIZE;
        if (pageView2.getPage().getRowCount() % PAGE_SIZE != 0) {
            num2++;
        }
        LOG.info("亏损标签-数据条数：" + pageView2.getPage().getRowCount() + "，数据页数：" + num2);
        for (int i = 1; i <= num2; i++) {
            page2.setCurrentPage(i);
            if (i != 1) {
                pageView2 = orderReadService.listByTimeQuery(appointmentQuery, page2, OrderSelectEnum.GOODS);
            }
            List<Order> records = pageView2.getRecords();
            //打标
            markLossLabel(records);
        }

        LOG.info("执行订单打标任务完成");
    }

    private void markLossWarningLabel(List<Order> records) {

        //过滤已有标签的订单
        records = records.stream().filter(
                t -> Objects.isNull(t.getOrderExt())
                        || CollectionUtils.isEmpty(t.getOrderExt().getOrderTags())
                        || !t.getOrderExt().getOrderTags().contains(OrderTagQueryEnum.LOSS_WARNING.getMsg().replaceAll("\"", ""))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        //渠道
        ChannelQuery query = new ChannelQuery();
        query.setBizType(OrgaBizTypeEnum.VACCINE);
        List<Channel> channels = channelService.listByQuery(query);
        Map<Long, Channel> idMapChannel = channels.stream().collect(Collectors.toMap(Channel::getId, Function.identity()));

        //获取标品对应履约品
        Set<Long> goodsId = records.stream().map(t -> t.getGoodsSnapshot().getId()).collect(Collectors.toSet());
        List<FulmtGoodsRelationValue> fulmtGoodsRel = fulmtGoodsRelationService.getListByGoodsIdList(goodsId);
        //成本
        Set<Long> fulmtGoodsId = fulmtGoodsRel.stream().map(FulmtGoodsRelationValue::getFulmtGoodsId).collect(Collectors.toSet());
        List<GoodsCost> goodsCosts = goodsCostService.effectiveCost(new ArrayList<>(fulmtGoodsId), LocalDate.now());

        Map<Long, GoodsCost> goodsIdMapGoodsCost = goodsCosts.stream().collect(Collectors.toMap(GoodsCost::getGoodsId, Function.identity()));
        Map<Long, List<FulmtGoodsRelationValue>> goodsIdMapFulmtGoods = fulmtGoodsRel.stream().collect(Collectors.groupingBy(FulmtGoodsRelationValue::getGoodsId));

        //排序
        for (Map.Entry<Long, List<FulmtGoodsRelationValue>> entry : goodsIdMapFulmtGoods.entrySet()) {
            List<FulmtGoodsRelationValue> value = entry.getValue();
            value.sort(Comparator.comparing(t -> goodsIdMapGoodsCost.containsKey(t.getFulmtGoodsId()) ? goodsIdMapGoodsCost.get(t.getFulmtGoodsId()).getSupplyPrice() : 0L));
        }

        for (Order record : records) {
            List<FulmtGoodsRelationValue> fulmtGoodsRelationValueList = goodsIdMapFulmtGoods.get(record.getGoodsSnapshot().getId());
            if (CollectionUtils.isNotEmpty(fulmtGoodsRelationValueList)) {
                FulmtGoodsRelationValue maxFulmtGoods = fulmtGoodsRelationValueList.get(fulmtGoodsRelationValueList.size() - 1);
                //最高成本
                GoodsCost cost = goodsIdMapGoodsCost.get(maxFulmtGoods.getFulmtGoodsId());
                if (needMarkLabel(record, cost == null ? 0L : cost.getSupplyPrice(), idMapChannel)) {
                    //不包含的情况再更改
                    OrderExt orderExt = new OrderExt();
                    List<String> orderTags;
                    if (Objects.isNull(record.getOrderExt()) || Objects.isNull(record.getOrderExt().getOrderTags())) {
                        orderTags = new ArrayList<>();
                    } else {
                        orderTags = record.getOrderExt().getOrderTags();
                    }

                    orderTags.add(OrderTagQueryEnum.LOSS_WARNING.getMsg().replaceAll("\"", ""));
                    orderExt.setOrderTags(orderTags);
                    orderOperateService.updateOrderExt(record.getOrderNum(), orderExt);
                }
            }
        }
    }

    private void markLossLabel(List<Order> records) {

        //过滤已有标签的订单
//        records = records.stream().filter(t->!t.getOrderExt().getOrderTags().contains(OrderTagQueryEnum.LOSS.getMsg())).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(records)) {
//            return;
//        }

        //渠道
        ChannelQuery query = new ChannelQuery();
        query.setBizType(OrgaBizTypeEnum.VACCINE);
        List<Channel> channels = channelService.listByQuery(query);
        Map<Long, Channel> idMapChannel = channels.stream().collect(Collectors.toMap(Channel::getId, Function.identity()));

        for (Order record : records) {
            boolean flag = false;

            //获取履约商品
            List<Order> childOrders = record.getChildOrders();
            for (Order childOrder : childOrders) {
                OrderFulmtValue orderFulmtValue = orderFulmtService.checkOrder(childOrder.getOrderNum());
                if (orderFulmtValue != null) {
                    //当前履约单是否亏损
                    Long fulmtGoodsId = orderFulmtValue.getFulmtGoodsId();
                    GoodsCost cost = goodsCostService.effectiveCost(fulmtGoodsId, date2LocalDate(orderFulmtValue.getFulmtDate()));
                    if (cost == null) {
                        flag = true;
                    } else if (needMarkLabel(childOrder, cost.getSupplyPrice(), idMapChannel)) {
                        flag = true;
                    }
                }
            }

            String lossMsgStr = OrderTagQueryEnum.LOSS.getMsg().replaceAll("\"", "");
            if (flag) {
                //需要加亏损标签
                if (record.getOrderExt() == null || CollectionUtils.isEmpty(record.getOrderExt().getOrderTags()) || !record.getOrderExt().getOrderTags().contains(lossMsgStr)) {
                    //不包含亏损标签时添加
                    List<String> orderTags;
                    if (record.getOrderExt() == null || CollectionUtils.isEmpty(record.getOrderExt().getOrderTags())) {
                        orderTags = new ArrayList<>();
                    } else {
                        orderTags = record.getOrderExt().getOrderTags();
                    }

                    OrderExt orderExt = new OrderExt();
                    orderTags.add(lossMsgStr);
                    orderExt.setOrderTags(orderTags);
                    orderOperateService.updateOrderExt(record.getOrderNum(), orderExt);
                }
            } else {
                //去除亏损标签
                if (record.getOrderExt() != null && CollectionUtils.isNotEmpty(record.getOrderExt().getOrderTags()) && record.getOrderExt().getOrderTags().contains(lossMsgStr)) {
                    OrderExt orderExt = new OrderExt();
                    List<String> orderTags = record.getOrderExt().getOrderTags();
                    orderTags.remove(lossMsgStr);
                    orderExt.setOrderTags(orderTags);
                    orderOperateService.updateOrderExt(record.getOrderNum(), orderExt);
                }
            }
        }

    }

    private boolean needMarkLabel(Order record, Long supplyPrice, Map<Long, Channel> idMapChannel) {

        supplyPrice = supplyPrice == null ? 0 : supplyPrice;

        //总金额
        Long payAmount = Objects.isNull(record.getPayAmount()) ? 0L : record.getPayAmount();

        //渠道扣点
        Float commission = 0.0F;
        if (record.getChorgaType() == ChorgaTypeEnum.CHANNEL.getCode()) {
            Channel channel = idMapChannel.get(record.getChorgaId());
            commission = channel == null || channel.getCommission() == null ? 0.0F : channel.getCommission();
        }

        //支付金额 - 渠道扣点 >= 最高成本价
        return Double.valueOf(payAmount.doubleValue() - payAmount * commission).compareTo(supplyPrice.doubleValue()) <= 0;
    }

    private LocalDate date2LocalDate(Date date) {
        if (date == null) {
            return LocalDate.now();
        }
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return instant.atZone(zoneId).toLocalDate();
    }
}
