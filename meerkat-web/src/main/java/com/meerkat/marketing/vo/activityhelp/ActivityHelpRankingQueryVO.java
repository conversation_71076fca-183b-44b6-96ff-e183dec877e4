package com.meerkat.marketing.vo.activityhelp;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Since 2022-09-29
 * @Description 助力排行列表查询VO
 */
public class ActivityHelpRankingQueryVO {

    @NotNull(message = "活动id不能为空")
    @ApiModelProperty(value = "活动id",required = true)
    private Long activityId;

    @ApiModelProperty("当前页")
    private Integer currentPage;

    @ApiModelProperty("页大小")
    private Integer pageSize;

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "ActivityHelpUserRankingQueryVO{" +
                "activityId=" + activityId +
                ", currentPage=" + currentPage +
                ", pageSize=" + pageSize +
                '}';
    }
}
