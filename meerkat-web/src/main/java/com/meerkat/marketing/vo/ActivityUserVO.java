package com.meerkat.marketing.vo;

import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 活动参与者模型
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-27
 */
public class ActivityUserVO {

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "票数")
    private Integer tickets;

    @ApiModelProperty(value = "排行")
    private Integer ranking;

    @ApiModelProperty(value = "参与活动用户昵称")
    private String nickname;

    @ApiModelProperty(value = "参与活动用户头像")
    private String icon;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getTickets() {
        return tickets;
    }

    public void setTickets(Integer tickets) {
        this.tickets = tickets;
    }

    public Integer getRanking() {
        return ranking;
    }

    public void setRanking(Integer ranking) {
        this.ranking = ranking;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    @Override
    public String toString() {
        return "ActivityUser{" +
                "userId=" + userId +
                ", tickets=" + tickets +
                ", ranking=" + ranking +
                ", nickname='" + nickname + '\'' +
                ", icon='" + icon + '\'' +
                '}';
    }
}
