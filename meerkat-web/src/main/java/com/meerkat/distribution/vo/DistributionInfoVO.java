package com.meerkat.distribution.vo;

import com.meerkat.distribution.platform.model.DistributionGoods;
import com.meerkat.distribution.platform.model.Distributor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/4 20:43
 */
public class
DistributionInfoVO {

    private Distributor distributor;

    private List<DistributionGoods> distributionGoodsList;

    public DistributionInfoVO(Distributor distributor, List<DistributionGoods> distributionGoodsList) {
        this.distributor = distributor;
        this.distributionGoodsList = distributionGoodsList;
    }

    public Distributor getDistributor() {
        return distributor;
    }

    public void setDistributor(Distributor distributor) {
        this.distributor = distributor;
    }

    public List<DistributionGoods> getDistributionGoodsList() {
        return distributionGoodsList;
    }

    public void setDistributionGoodsList(List<DistributionGoods> distributionGoodsList) {
        this.distributionGoodsList = distributionGoodsList;
    }
}
