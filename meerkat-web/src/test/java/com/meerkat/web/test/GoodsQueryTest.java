package com.meerkat.web.test;

import cn.hutool.json.JSONUtil;
import com.meerkat.MeerkatWebApplication;
import com.meerkat.shop.goods.enums.GoodsSortField;
import com.meerkat.shop.goods.param.GoodsQuery;
import com.meerkat.shop.goods.service.GoodsService;
import com.meerkat.shop.goods.service.GoodsWriteService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2022/1/18 11:40
 */
@SpringBootTest(classes = MeerkatWebApplication.class)
public class GoodsQueryTest {

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private GoodsWriteService goodsWriteService;


    //@Test
    public void listGoodsByGoodsQuery() {
        GoodsQuery goodsQuery = new GoodsQuery();
        goodsQuery.setGoodsSortField(GoodsSortField.PRICE);
        goodsQuery.setDesc(true);
        System.out.println(JSONUtil.toJsonStr(goodsService.listGoodsByGoodsQuery(goodsQuery)));
    }

    @Test
    public void aspectTest() {
        goodsWriteService.updateSaleable(111111111L, 1L);
    }
}
