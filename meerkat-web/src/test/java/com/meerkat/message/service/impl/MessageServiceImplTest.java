package com.meerkat.message.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.meerkat.common.enums.ChorgaTypeEnum;
import com.meerkat.message.constants.SmsTemplateParamConstants;
import com.meerkat.message.enums.SmsTemplateEnum;
import com.meerkat.message.model.Response;
import com.meerkat.message.model.param.CompanyWxParam;
import com.meerkat.message.model.param.FeishuParam;
import com.meerkat.message.model.param.MessageParam;
import com.meerkat.message.model.param.SmsParam;
import com.meerkat.message.service.MessageService;
import com.meerkat.message.service.WxCpServiceProxy;
import com.meerkat.task.CardExpireJob;
import com.meerkat.task.SmsNoticeJob;
import com.meerkat.task.TjsySmsBeforeOneJob;
import com.meerkat.task.TjsySmsBeforeThreeJob;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 测试消息发送service
 * </p>
 *
 * <AUTHOR>
 * @since 2022/4/13 10:54
 */
//@SpringBootTest(classes = MeerkatWebApplication.class)
class MessageServiceImplTest {

    @Autowired(required = false)
    private SmsNoticeJob smsNoticeJob;

    @Autowired(required = false)
    private TjsySmsBeforeOneJob oneJob;

    @Autowired(required = false)
    private TjsySmsBeforeThreeJob threeJob;

    @Autowired(required = false)
    private CardExpireJob cardExpireJob;

    @Autowired
    private MessageService messageService;

    @Autowired
    private WxCpServiceProxy wxCpServiceProxy;

    /**
     * 群机器人钩子方法 后期可作为参数
     */
    private static final String web_hook_url = "https://open.feishu.cn/open-apis/bot/v2/hook/6ad54ec7-4c3b-41e0-8e32-07b976fd5d62";

//    @Test
    void send1() {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put(SmsTemplateParamConstants.AUTH_CODE, "123456");
        MessageParam messageParam = new SmsParam(1L,1,null, SmsTemplateEnum.GENERAL_AUTH_CODE, paramMap, "19525294009");
        Response send = messageService.send(messageParam);
        System.out.println(JSONUtil.toJsonStr(send));
    }

//    @Test
    void send2() {
        Response send = messageService.send(new FeishuParam(web_hook_url,  new String[]{"19525294009","13732278794","17621158739"},"系统测试消息"));
        System.out.println(JSONUtil.toJsonStr(send));
    }

//    @Test
    void send3() {
        CompanyWxParam wxParam = new CompanyWxParam(new String[]{"wmFAxGPQAAymxarluOBHqKps6G8luU3Q"}, "测试企业微信客户通知");
        List<CompanyWxParam.IAttachment> attachments = new ArrayList<>();
        CompanyWxParam.Link link = new CompanyWxParam.Link("百度百科", "https://baidu.com");
        link.setPicurl("https://dss0.bdstatic.com/5aV1bjqh_Q23odCf/static/superman/img/logo/bd_logo1-66368c33f8.png");
        link.setDesc("测试");
        attachments.add(link);
        String png = wxCpServiceProxy.uploadImg("png", FileUtil.getInputStream(FileUtil.file("/home/<USER>/Downloads/tutu.png")));
        CompanyWxParam.Image image = new CompanyWxParam.Image(png);
        attachments.add(image);
        wxParam.setAttachments(attachments);
        messageService.send(wxParam);
    }
//    @Test
    void send4() {
        Map<String, String> param = Maps.newHashMap();
        param.put(SmsTemplateParamConstants.USER_NAME, "123");
        param.put(SmsTemplateParamConstants.DETAIL, "123");
        param.put(SmsTemplateParamConstants.ORGANIZATION_NAME, "123");
        param.put(SmsTemplateParamConstants.GOODS_NAME, "123");
        param.put(SmsTemplateParamConstants.CUSTOMER_TEL, "123");
        param.put(SmsTemplateParamConstants.PERIOD, "123");
        param.put(SmsTemplateParamConstants.ADDRESS, "123");
        MessageParam messageParam = new SmsParam(1L,
                ChorgaTypeEnum.ORGANIZATION.getCode(),
                null,
                SmsTemplateEnum.APPOINTMENT_MSG,
                param,
                "19525294009");
        messageService.send(messageParam);
    }

//    @Test
    public void job() {
            smsNoticeJob.execute();
//            oneJob.execute();
//            threeJob.execute();
//            cardExpireJob.execute();
    }


    /*@Test
    public void test02() {
        String url = "https://open.feishu.cn/open-apis/bot/v2/hook/76f44004-ead8-4f7c-9f0a-61e5fcfbca5c";
        // 体检订单
        StringBuilder sb = new StringBuilder();
        sb.append("\n"+"机构").append("").append("\n");
        sb.append("套餐:").append("").append("\n");
        sb.append("套餐内项目:").append("").append("\n");
        sb.append("加项:").append("").append("\n");
        sb.append("市场价格:").append("").append("\n");
        sb.append("预约日期:").append("").append("\n");
        sb.append("体检人姓名:").append("").append("\n");
        sb.append("性别:").append(1 == 1?"男":"女").append("\n");
        sb.append("婚否:").append("").append("\n");
        sb.append("证件号码:").append("").append("\n");
        sb.append("年龄:").append("").append("\n");
        sb.append("体检人电话:").append("").append("\n");
        Response send = messageService.send(new FeishuParam(url, new String[]{"18790184684"}, sb.toString()));
        System.out.println(JSONUtil.toJsonStr(send));
    }*/
//    @Test
//    void send(){
//        CompanyWxParam wxParam = new CompanyWxParam(new String[]{"wmFAxGPQAAymxarluOBHqKps6G8luU3Q"}, "测试企业微信客户通知");
//        List<CompanyWxParam.IAttachment> attachments = new ArrayList<>();
//        CompanyWxParam.Link link = new CompanyWxParam.Link("百度百科", "https://baidu.com");
//        link.setPicurl("https://dss0.bdstatic.com/5aV1bjqh_Q23odCf/static/superman/img/logo/bd_logo1-66368c33f8.png");
//        link.setDesc("测试");
//        attachments.add(link);
//        wxParam.setAttachments(attachments);
//        messageService.send(wxParam);
//    }
}