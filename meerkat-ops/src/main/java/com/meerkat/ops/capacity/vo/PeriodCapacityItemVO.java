package com.meerkat.ops.capacity.vo;

import com.meerkat.ops.capacity.orga.vo.CapacityVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @program meerkat-origin
 * @description:
 * @author: fan<PERSON><PERSON>
 * @create: 2022/07/22 11:50
 */
@ApiModel("PeriodCapacityItemVO")
public class PeriodCapacityItemVO {

    /**
     * 时段
     */
    @ApiModelProperty("时段")
    private String period;
    /**
     * 容量列表
     */
    @ApiModelProperty("容量")
    private CapacityVO capacityVO;

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public CapacityVO getCapacityVO() {
        return capacityVO;
    }

    public void setCapacityVO(CapacityVO capacityVO) {
        this.capacityVO = capacityVO;
    }
}
