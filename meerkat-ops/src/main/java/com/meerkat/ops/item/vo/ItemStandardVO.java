package com.meerkat.ops.item.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @ClassName ItemStandardVO
 * @description 标准项目视图模型
 * @createTime 2022/5/6 16:08
 */
public class ItemStandardVO {
    /**
     * id
     */
    private Integer id;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String name;

    /**
     * 项目名称拼音
     */
    @ApiModelProperty("项目名称拼音")
    private String pinyin;

    /**
     * 性别: 0-女 1-男 2-通用
     */
    @ApiModelProperty("性别: 0-女 1-男 2-通用")
    private Integer gender;

    /**
     * 婚姻状态: 1- 已婚 2-通用
     */
    @ApiModelProperty("婚姻状态: 1- 已婚 2-通用")
    private Integer marriageStatus;

    /**
     * 详情介绍
     */
    @ApiModelProperty("详情介绍(检查意义)")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sequence;

    @ApiModelProperty("关键词")
    private String keyWord;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getMarriageStatus() {
        return marriageStatus;
    }

    public void setMarriageStatus(Integer marriageStatus) {
        this.marriageStatus = marriageStatus;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public LocalDateTime getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(LocalDateTime gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }
}
