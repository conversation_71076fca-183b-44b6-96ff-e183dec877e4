<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.ops.orderjob.mapper.OrderJobMapper">

    <resultMap id="BaseResultMap" type="com.meerkat.ops.orderjob.mapper.dataobj.OrderJob" >
        <result column="id" property="id" />
        <result column="state" property="state" />
        <result column="request_id" property="requestId" />
        <result column="source" property="source" />
        <result column="message" property="message" />
        <result column="file_name" property="fileName" />
        <result column="operator_id" property="operatorId" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                state,
                request_id,
                source,
                message,
                file_name,
                operator_id,
                is_deleted,
                gmt_created,
                gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.meerkat.ops.orderjob.mapper.dataobj.OrderJob">
        INSERT INTO tb_order_job
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != state and '' != state">
                state,
            </if>
            <if test="null != requestId and '' != requestId">
                request_id,
            </if>
            <if test="null != source and '' != source">
                source,
            </if>
            <if test="null != message and '' != message">
                message,
            </if>
            <if test="null != fileName and '' != fileName">
                file_name,
            </if>
            <if test="null != operatorId ">
                operator_id,
            </if>
            <if test="null != isDeleted and '' != isDeleted">
                is_deleted,
            </if>
            <if test="null != gmtCreated and '' != gmtCreated">
                gmt_created,
            </if>
            <if test="null != gmtModified and '' != gmtModified">
                gmt_modified
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != state and '' != state">
                #{state},
            </if>
            <if test="null != requestId and '' != requestId">
                #{requestId},
            </if>
            <if test="null != source and '' != source">
                #{source},
            </if>
            <if test="null != message and '' != message">
                #{message},
            </if>
            <if test="null != fileName and '' != fileName">
                #{fileName},
            </if>
            <if test="null != operatorId ">
                #{operatorId},
            </if>
            <if test="null != isDeleted and '' != isDeleted">
                #{isDeleted},
            </if>
            <if test="null != gmtCreated and '' != gmtCreated">
                #{gmtCreated},
            </if>
            <if test="null != gmtModified and '' != gmtModified">
                #{gmtModified}
            </if>
        </trim>
    </insert>


    <update id="update" parameterType="com.meerkat.ops.orderjob.mapper.dataobj.OrderJob">
        UPDATE tb_order_job
        <set>
            <if test="null != state and '' != state">state = #{state},</if>
            <if test="null != requestId and '' != requestId">request_id = #{requestId},</if>
            <if test="null != source and '' != source">source = #{source},</if>
            <if test="null != message and '' != message">message = #{message},</if>
            <if test="null != fileName and '' != fileName">file_name = #{fileName},</if>
            <if test="null != isDeleted and '' != isDeleted">is_deleted = #{isDeleted},</if>
            <if test="null != gmtCreated and '' != gmtCreated">gmt_created = #{gmtCreated},</if>
            <if test="null != gmtModified and '' != gmtModified">gmt_modified = #{gmtModified}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="infoList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_order_job
        order by gmt_created desc limit 5
    </select>


</mapper>