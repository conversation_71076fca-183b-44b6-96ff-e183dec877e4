package com.meerkat.ops.workorder.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel
public class WorkOrderReassignmentDealParam {

    /**
     * 工单集合
     */
    @ApiModelProperty("工单号集合")
    @NotEmpty(message = "工单号集合人不能为空")
    private List<String> workOrderNoList;
    /**
     * 工单新的处理人
     */
    @ApiModelProperty("新处理人id")
    @NotNull(message = "新处理人不能为空")
    private Long receiverId;


    public List<String> getWorkOrderNoList() {
        return workOrderNoList;
    }

    public void setWorkOrderNoList(List<String> workOrderNoList) {
        this.workOrderNoList = workOrderNoList;
    }

    public Long getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(Long receiverId) {
        this.receiverId = receiverId;
    }
}
