package com.meerkat.ops.workorder.param;

import com.meerkat.common.db.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel
public class WorkOrderQueryForListParam {
    @ApiModelProperty("工单小类 100待预约工单 101待核销工单 102待改期工单 103二方待核销工单104机构待确认工单 105商品成本待维护工单 106退款待审批工单")
    private Integer workOrderBizType;
    @ApiModelProperty("状态  0待处理 1稍后处理 2处理中 3已处理")
    private Integer status;
    @ApiModelProperty("分页")
    private Page page;

    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("商品城市")
    private String goodCityName;
    @ApiModelProperty("履约城市")
    private String cityName;

    @ApiModelProperty("订单号")
    private String orderNum;
    @ApiModelProperty("渠道订单编码")
    private String outOrderNum;
    @ApiModelProperty("工单小类子类 0默认 10099001待预约工单-预约不匹配工单 10099002待预约工单-企微工单")
    private Integer workOrderBizTypeSon;

    @ApiModelProperty("商品名称")
    private String goodsName;
    @ApiModelProperty("履约机构")
    private String fulmtOrgaName;
    @ApiModelProperty("履约人")
    private String acceptorName;
    @ApiModelProperty("下单时间-开始时间(格式 yyyy-MM-dd)")
    private String orderStateTime;
    @ApiModelProperty("下单时间-结束时间(格式 yyyy-MM-dd)")
    private String orderEndTime;
    @ApiModelProperty("负责人")
    private String receiverName;
    @ApiModelProperty("管理台列表类型 1客服 2运营 3商务(管理台列表必传)")
    private Integer manageRoleType;


    public String getGoodCityName() {
        return goodCityName;
    }

    public void setGoodCityName(String goodCityName) {
        this.goodCityName = goodCityName;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getFulmtOrgaName() {
        return fulmtOrgaName;
    }

    public void setFulmtOrgaName(String fulmtOrgaName) {
        this.fulmtOrgaName = fulmtOrgaName;
    }

    public String getAcceptorName() {
        return acceptorName;
    }

    public void setAcceptorName(String acceptorName) {
        this.acceptorName = acceptorName;
    }

    public String getOrderStateTime() {
        return orderStateTime;
    }

    public void setOrderStateTime(String orderStateTime) {
        this.orderStateTime = orderStateTime;
    }

    public String getOrderEndTime() {
        return orderEndTime;
    }

    public void setOrderEndTime(String orderEndTime) {
        this.orderEndTime = orderEndTime;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public Integer getManageRoleType() {
        return manageRoleType;
    }

    public void setManageRoleType(Integer manageRoleType) {
        this.manageRoleType = manageRoleType;
    }

    public Integer getWorkOrderBizType() {
        return workOrderBizType;
    }

    public void setWorkOrderBizType(Integer workOrderBizType) {
        this.workOrderBizType = workOrderBizType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getOutOrderNum() {
        return outOrderNum;
    }

    public void setOutOrderNum(String outOrderNum) {
        this.outOrderNum = outOrderNum;
    }

    public Integer getWorkOrderBizTypeSon() {
        return workOrderBizTypeSon;
    }

    public void setWorkOrderBizTypeSon(Integer workOrderBizTypeSon) {
        this.workOrderBizTypeSon = workOrderBizTypeSon;
    }
}
