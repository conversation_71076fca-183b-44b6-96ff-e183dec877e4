package com.meerkat.ops.workorder.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;

@ApiModel
public class WorkOrderStatisticsQueryParam {


    @ApiModelProperty("开始时间 yyyy-MM-dd")
    @NotEmpty(message = "开始时间不能为空")
    private String startTime;
    @NotEmpty(message = "结束时间不能为空")
    @ApiModelProperty("结束时间 yyyy-MM-dd")
    private String endTime;


    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
