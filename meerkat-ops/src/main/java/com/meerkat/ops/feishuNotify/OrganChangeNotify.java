package com.meerkat.ops.feishuNotify;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.feishu.service.FeishuService;
import com.meerkat.fulfillment.fulfillment.handler.OrderFulmtStrategyHandler;
import com.meerkat.fulfillment.fulfillment.model.OrderFulmt;
import com.meerkat.fulfillment.fulfillment.model.OrderFulmtCheck;
import com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue;
import com.meerkat.fulfillment.fulfillment.enums.FulmtStateEnum;
import com.meerkat.ops.fulmt.param.OrderFulmtParam;
import com.meerkat.ops.fulmt.service.OrderFulmtStateUpdateService;
import com.meerkat.smart.channel.model.Channel;
import com.meerkat.smart.channel.service.ChannelService;
import com.meerkat.smart.organization.service.OrganizationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrganChangeNotify {

    @Autowired
    private FeishuService feishuService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private OrderFulmtStateUpdateService orderFulmtStateUpdateService;

    @Autowired
    private OrderFulmtStrategyHandler orderFulmtStrategyHandler;

    @Value("${feishu.robot.organChangeWebHook:https://open.feishu.cn/open-apis/bot/v2/hook/42a1437a-b535-47dc-801f-d8365904f5ad}")
    private String organChangeWebHook;

    private static final Logger logger = LoggerFactory.getLogger(OrganChangeNotify.class);


    /**
     * 机构禁用通知
     *
     * @param organId
     */
    public void organDisable(Long organId) throws Exception {
        logger.info("机构禁用通知：{}", organId);
        List<Channel> channelList = channelService.listAllChannel();

        OrderFulmtCheck orderFulmtCheck = new OrderFulmtCheck();
        orderFulmtCheck.setFulmtOrganIdList(Lists.newArrayList(organId));
        orderFulmtCheck.setStateList(Lists.newArrayList(
                FulmtStateEnum.APPOINt.getCode(),
                FulmtStateEnum.AGENCY_CONFIRMED.getCode(),
                FulmtStateEnum.TRILATERA_VERIFICATION.getCode(),
                FulmtStateEnum.VERIFICATION.getCode()
                )
        );
        List<OrderFulmtValue> orderFulmtList = orderFulmtStateUpdateService.getFlumtInfo(orderFulmtCheck);

        List<List<Map>> msgContentList = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(orderFulmtList)) {
            Map<Long, Channel> channelMap = channelList.stream().collect(Collectors.toMap(Channel::getId, Function.identity()));

            List<String> textLine = new ArrayList<>();
            Integer notifyCount = 0;
            for (OrderFulmtValue orderFulmtValue : orderFulmtList) {
                //履约日期为机构禁用日期T+1的发通知
                if (orderFulmtValue.getFulmtDate() == null || orderFulmtValue.getFulmtDate().before(DateUtil.endOfDay(new Date()))) {
                    continue;
                }
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("订单号：");
                stringBuilder.append(orderFulmtValue.getParentOrderNum());
                stringBuilder.append("  姓名：");
                stringBuilder.append(orderFulmtValue.getAcceptorName());
                stringBuilder.append("  手机号：");
                stringBuilder.append(orderFulmtValue.getAcceptorMobile());
                Channel channel = channelMap.get(orderFulmtValue.getChorgaId());
                stringBuilder.append("  渠道:");
                stringBuilder.append(channel.getName());

                //消息提
                Map bodyMap = new HashMap();
                bodyMap.put("tag", "text");
                bodyMap.put("text", stringBuilder.toString());
                msgContentList.add(Lists.newArrayList(bodyMap));

                notifyCount++;
                orderFulmtValue.setState(FulmtStateEnum.TB_RESCHEDULE.getCode());
                OrderFulmt param = CopyUtil.copy(orderFulmtValue, OrderFulmt.class);
                //机构禁用
                param.setType(0);
                orderFulmtStrategyHandler.handler(param, "RESERVED_GENERAL", 99L);
            }
            //消息--最后一行
            Map lastMap = new HashMap();
            lastMap.put("tag", "text");
            lastMap.put("text", "机构已暂停服务，请修改履约机构");
            msgContentList.add(Lists.newArrayList(lastMap));

            //消息--第一行
            Map headMap = new HashMap();
            headMap.put("tag", "text");
            headMap.put("text", orderFulmtList.get(0).getFulmtOrgaName() + "机构已暂停服务，以下" + notifyCount + "笔订单的履约状态已推进至待改期，请修改履约机构：");
            msgContentList.add(0, Lists.newArrayList(headMap));

            feishuService.notifyDataSend(organChangeWebHook, "机构禁用提醒", msgContentList, Lists.newArrayList("15158878631"));
        }

    }

}
