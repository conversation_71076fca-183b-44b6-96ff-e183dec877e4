package com.meerkat.ops.file.controller;

import com.meerkat.base.enums.FileModuleTypeEnum;
import com.meerkat.base.enums.UploadFileTypeEnum;
import com.meerkat.base.service.FileStorageService;
import com.meerkat.common.api.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

/**
 * 文件上传
 *
 * <AUTHOR>
 */
@Api(tags = {"fileUploadController"})
@RestController
@RequestMapping("/file")
public class FileUploadController {

    @Autowired
    private FileStorageService fileStorageService;

    /**
     * @param file
     * @param id         当id不传时，存储至临时文件夹 @see FileStorageService.FILE_TEMP_DIR
     * @param moduleType
     * @param fileType
     * @return
     * @throws Exception
     *
     */
    @ApiOperation(value = "文件上传")
    @PostMapping("/upload")
    public CommonResult upload(@RequestParam("file") MultipartFile file,
                               @RequestParam(value = "fileName", required = false) String fileName,
                               @RequestParam(value = "extension", required = false) String extension,
                               @RequestParam(value = "id", required = false) String id,
                               @RequestParam(required = false) FileModuleTypeEnum moduleType,
                               @RequestParam(required = false) UploadFileTypeEnum fileType) throws Exception {
        if (moduleType == null) {
            moduleType = FileModuleTypeEnum.OTHER;
        }
        if (fileType == null) {
            fileType = UploadFileTypeEnum.OTHER;
        }
        if (ObjectUtils.isEmpty(extension)) {
            String fileNameTemp = file.getOriginalFilename();
            extension = fileNameTemp.substring(fileNameTemp.lastIndexOf('.'));
        }
        String url = fileStorageService.upload(null, extension, moduleType, "/" + moduleType.name() + "/" + fileType.getLowercase(), file.getInputStream());
        return CommonResult.success(url);
    }

    @ApiOperation(value = "文件上传")
    @PostMapping("/uploadByOriginName")
    public CommonResult uploadWithFileName(@RequestParam("file") MultipartFile file,
                               @RequestParam(value = "extension", required = false) String extension,
                               @RequestParam(required = false) FileModuleTypeEnum moduleType,
                               @RequestParam(required = false) UploadFileTypeEnum fileType) throws Exception {
        if (moduleType == null) {
            moduleType = FileModuleTypeEnum.OTHER;
        }
        if (fileType == null) {
            fileType = UploadFileTypeEnum.OTHER;
        }
        if (ObjectUtils.isEmpty(extension)) {
            String fileNameTemp = file.getOriginalFilename();
            extension = fileNameTemp.substring(fileNameTemp.lastIndexOf('.'));
        }
        String originFileName = file.getOriginalFilename();
        String fileName = originFileName.substring(0, originFileName.lastIndexOf(".")) + "-" + System.currentTimeMillis();
        String url = fileStorageService.upload(fileName, extension, moduleType, "/" + moduleType.name() + "/" + fileType.getLowercase(), file.getInputStream());
        return CommonResult.success(url);
    }

    @ApiOperation(value = "文件删除")
    @PostMapping("/del")
    public CommonResult upload(@RequestParam("fileUrl") String url) throws Exception {
        fileStorageService.del(url);
        return CommonResult.success();
    }
}
