package com.meerkat.ops.goods.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.meerkat.ops.tag.vo.TagVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

@ApiModel("GoodsTemplateVO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GoodsTemplateVO implements Serializable {

    private Long id;

    @ApiModelProperty("类型，1:2价；1:4价；3:9价；")
    private Integer goodsType;

//    @ApiModelProperty("描述")
//    private String description;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("主图")
    private String mainImage;

    @ApiModelProperty("标签")
    private List<TagVO> tagVOList;


    @ApiModelProperty("详情图片")
    private String detailImage;

    @ApiModelProperty("是否删除：0-未删除 1-已删除")
    private Integer isDeleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }

//    public String getDescription() {
//        return description;
//    }
//
//    public void setDescription(String description) {
//        this.description = description;
//    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getMainImage() {
        return mainImage;
    }

    public void setMainImage(String mainImage) {
        this.mainImage = mainImage;
    }

    public String getDetailImage() {
        return detailImage;
    }

    public void setDetailImage(String detailImage) {
        this.detailImage = detailImage;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public List<TagVO> getTagVOList() {
        return tagVOList;
    }

    public void setTagVOList(List<TagVO> tagVOList) {
        this.tagVOList = tagVOList;
    }
}
