package com.meerkat.ops.goods.param;

import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2022/8/29 18:07
 */
public class GoodsCostParam {


    /**
     * 商品价格
     */
    @ApiModelProperty("商品价格")
    private Long goodsPrice;

    /**
     * 机构费用
     */
    @ApiModelProperty("机构费用")
    private Long orgaCosts;

    /**
     * 中介费用
     */
    @ApiModelProperty("中介费用")
    private Long intermediationCosts;

    /**
     * 外部销售提成
     */
    @ApiModelProperty("外部销售提成")
    private Long outSalesCommissions;

    /**
     * 激活时间
     */
    @ApiModelProperty("激活时间")
    private LocalDate activeDate;


    public Long getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(Long goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    public Long getOrgaCosts() {
        return orgaCosts;
    }

    public void setOrgaCosts(Long orgaCosts) {
        this.orgaCosts = orgaCosts;
    }

    public Long getIntermediationCosts() {
        return intermediationCosts;
    }

    public void setIntermediationCosts(Long intermediationCosts) {
        this.intermediationCosts = intermediationCosts;
    }

    public Long getOutSalesCommissions() {
        return outSalesCommissions;
    }

    public void setOutSalesCommissions(Long outSalesCommissions) {
        this.outSalesCommissions = outSalesCommissions;
    }

    public LocalDate getActiveDate() {
        return activeDate;
    }

    public void setActiveDate(LocalDate activeDate) {
        this.activeDate = activeDate;
    }
}
