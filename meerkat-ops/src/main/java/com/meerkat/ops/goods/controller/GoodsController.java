package com.meerkat.ops.goods.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.meerkat.auth.annotation.Log;
import com.meerkat.auth.enums.OperationEnum;
import com.meerkat.base.exception.BaseBizEnum;
import com.meerkat.base.model.Address;
import com.meerkat.base.service.AddressService;
import com.meerkat.capacity.model.GoodsCapacity;
import com.meerkat.capacity.service.GoodsCapacityService;
import com.meerkat.common.api.BaseBizCodeEnum;
import com.meerkat.common.api.CommonResult;
import com.meerkat.common.db.PageView;
import com.meerkat.common.exception.BizException;
import com.meerkat.common.utils.AssertUtil;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.ops.goods.enums.exception.OpsGoodsBizExEnum;
import com.meerkat.ops.goods.param.AddGoodsParam;
import com.meerkat.ops.goods.param.CouponGoodsParam;
import com.meerkat.ops.goods.param.OpsGoodsQuery;
import com.meerkat.ops.goods.param.StockParam;
import com.meerkat.ops.goods.vo.*;
import com.meerkat.ops.orga.vo.OrganizationVO;
import com.meerkat.ops.tag.vo.TagVO;
import com.meerkat.shop.goods.enums.*;
import com.meerkat.shop.goods.model.*;
import com.meerkat.shop.goods.param.GoodsQuery;
import com.meerkat.shop.goods.service.*;
import com.meerkat.smart.organization.enums.OrgaStatusEnum;
import com.meerkat.smart.organization.model.Organization;
import com.meerkat.smart.organization.service.OrganizationService;
import com.meerkat.smart.supplier.model.Supplier;
import com.meerkat.smart.supplier.service.SupplierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api(tags = {"goodsController"})
@RestController
@RequestMapping("/goods")
public class GoodsController {

    private static final Logger logger = LoggerFactory.getLogger(GoodsController.class);

    @Resource
    private GoodsService goodsService;

    @Resource
    private GoodsWriteService goodsWriteService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private GoodsCapacityService goodsCapacityService;

    @Resource
    private SupplierService supplierService;

    @Resource
    private AddressService addressService;

    @Resource
    private GoodsCategoryService goodsCategoryService;

    @Resource
    private GoodsCostService goodsCostService;

    @Autowired
    private GoodsTemplateService goodsTemplateService;

    @Autowired
    private CombineGoodsService combineGoodsService;


    /**
     * 商品列表
     */
    @ApiOperation(value = "分页检索商品")
    @PostMapping("/list")
    public CommonResult<PageView<GoodsVO>> pageList(@Valid @RequestBody OpsGoodsQuery queryParam) {

        GoodsQuery query = new GoodsQuery();
        if (StringUtils.isNotBlank(queryParam.getOrgaName())) {
            List<Organization> organizations = organizationService.listByNameLike(queryParam.getOrgaName(), queryParam.getBizType());
            //没有检索到机构，直接返回空
            if (CollectionUtils.isEmpty(organizations)) {
                return CommonResult.success(new PageView<>());
            }
            List<Long> orgaIds = organizations.stream().map(v -> v.getId()).collect(Collectors.toList());
//            queryParam.setOrgaId(orgaIds.get(0));
            query.setOrgaIdList(orgaIds);
        }
        if (Boolean.TRUE.equals(queryParam.getOnlyAvailableOrgan())) {
            //只有少量的无效机构，所以查询无效机构
            List<Organization> organizationList = organizationService.listByStatus(Collections.singletonList(OrgaStatusEnum.UNAVAILABLE.getCode()));
            if (!CollectionUtils.isEmpty(organizationList)) {
                List<Long> organIdList = organizationList.stream().map(Organization::getId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(queryParam.getExcludeOrgaList())) {
                    queryParam.setExcludeOrgaList(organIdList);
                }else {
                    queryParam.getExcludeOrgaList().addAll(organIdList);
                }
            }
        }
        //默认降序
        BeanUtils.copyProperties(queryParam, query);

        //前端不支持传枚举-和订单bizCode同样问题
        if (!CollectionUtils.isEmpty(queryParam.getGoodsTypeList())) {
            List<GoodsTypeEnum> goodsTypeEnums = new ArrayList<>();
            for (Integer i : queryParam.getGoodsTypeList()) {
                GoodsTypeEnum goodsTypeEnum = GoodsTypeEnum.getByCode(i);
                goodsTypeEnums.add(goodsTypeEnum);
                //建管商品目前管理在疫苗菜单下
//                if (GoodsTypeEnum.VACCINE.getCode() == i) {
//                    goodsTypeEnums.add(GoodsTypeEnum.HEALTH_MANAGER);
//                }
            }
            query.setGoodsTypeEnums(goodsTypeEnums);
        }
        if (!CollectionUtils.isEmpty(queryParam.getGoodsBizTypeList())) {
            query.setBizTypeList(queryParam.getGoodsBizTypeList());
        }

        //需要排除的机构商品
        if (!CollectionUtils.isEmpty(queryParam.getExcludeOrgaList())) {
            query.setExcludeOrgaList(queryParam.getExcludeOrgaList());
        }
        if (Objects.isNull(query.getGoodsSortField())) {
            query.setGoodsSortField(GoodsSortField.ID);
        }
        if (Objects.isNull(query.getDesc())) {
            query.setDesc(true);
        }

        PageView<Goods> goodsPageView = goodsService.listGoodsByGoodsQuery(query);
        PageView<GoodsVO> goodsVOPageView = new PageView<>();
        goodsVOPageView.setPage(goodsPageView.getPage());
        goodsVOPageView.setRecords(entity2vo(goodsPageView.getRecords()));

        List<GoodsVO> records = goodsVOPageView.getRecords();

        //机构信息
        Map<Long, OrganizationVO> orgaMap = new HashMap<>();
        List<Long> orgaId = records.stream().map(GoodsVO::getOrganizationId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orgaId)) {
            List<Organization> organizations = organizationService.listByOrgaIds(orgaId, null);
            List<OrganizationVO> organizationVOS = CopyUtil.copyList(organizations, OrganizationVO.class);
            for (OrganizationVO organizationVO : organizationVOS) {
                organizationVO.setAddress(addressService.getAddressById(organizationVO.getAddressId()));
            }
            orgaMap = organizationVOS.stream().collect(Collectors.toMap(OrganizationVO::getId, Function.identity()));
        }

        //供应商信息
        Map<Long, Supplier> supplierMap = new HashMap<>();
        List<Long> supplierIds = records.stream().map(GoodsVO::getSupplierId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(supplierIds)) {
            List<Supplier> suppliers = supplierService.getSupplierByIds(supplierIds);
            supplierMap = suppliers.stream().collect(Collectors.toMap(Supplier::getId, Function.identity()));
        }


        for (GoodsVO record : records) {
            record.setOrganization(orgaMap.get(record.getOrganizationId()));
            record.setSupplier(supplierMap.get(record.getSupplierId()));
        }

        //填充组合商品信息
        fillCombineGoodsInfo(records);

        //填充商品中的地址信息
        fillGoodsAddressInfo(records);

        //计算库存信息
        calculateStock(records);

        //填充商品成本
        fillGoodsCost(records);

        return CommonResult.success(goodsVOPageView);
    }

    @ApiOperation("根据机构id获取商品列表")
    @RequestMapping(value = "/listByOrgaId", method = RequestMethod.GET)
    public CommonResult<List<GoodsVO>> listByOrgaId(@RequestParam(required = true) Long orgaId) {
        List<GoodsVO> goodsVOS = entity2vo(goodsService.listGoodsByOrgaId(orgaId));
        return CommonResult.success(goodsVOS);
    }

    @ApiOperation("根据机构Id和条件查询商品列表")
    @RequestMapping(value = "/listByQuery", method = RequestMethod.POST)
    public CommonResult<List<GoodsVO>> listByQuery(@RequestBody GoodsQueryVO goodsQueryVO) {
        AssertUtil.notNull(goodsQueryVO);
        List<GoodsVO> goodsVOS = entity2vo(
                goodsService.listOrgaGoods(goodsQueryVO.getOrgaIdList(), this.convertToQuery(goodsQueryVO)));
        return CommonResult.success(goodsVOS);
    }

    public GoodsQuery convertToQuery(GoodsQueryVO goodsQueryVO) {
        GoodsQuery goodsQuery = new GoodsQuery();
        // 商品状态
        if (CollectionUtil.isNotEmpty(goodsQueryVO.getGoodsStatusList())) {
            goodsQuery.setGoodsStatusEnums(
                    goodsQueryVO.getGoodsStatusList()
                            .stream()
                            .map(GoodsStatusEnum::of)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList())
            );
        }

        // 可售状态
        if (CollectionUtil.isNotEmpty(goodsQueryVO.getGoodsSaleableList())) {
            goodsQuery.setGoodsSaleableEnums(
                    goodsQueryVO.getGoodsStatusList()
                            .stream()
                            .map(GoodsSaleableEnum::of)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList())
            );
        }
        return goodsQuery;
    }

    /**
     * 添加商品套餐
     *
     * @param goodsParams
     * @return
     */
    @ApiOperation(value = "添加商品")
    @PostMapping("/addOrUpdate")
    @Log(name = "商品信息", operation = OperationEnum.EDIT, template = "商品所属机构编号为{1}", keys = {"organizationId"})
    public CommonResult addOne(@Valid @RequestBody AddGoodsParam goodsParams) {

        logger.info("添加商品入参" + JSONUtil.toJsonStr(goodsParams));

        Goods goods = new Goods();
        BeanUtils.copyProperties(goodsParams, goods);
        goods.setGoodsCost(CopyUtil.copy(goodsParams.getGoodsCost(), GoodsCost.class));

        if (Boolean.TRUE.equals(goodsParams.getIsCombine()) && CollectionUtils.isEmpty(goodsParams.getSubGoodsParamList())) {
            throw new BizException(BaseBizEnum.ILLEGAL_ARGUMENT, "组合商品，商品列表不能为空");
        }
        if (Boolean.TRUE.equals(goodsParams.getIsCombine()) && !CollectionUtils.isEmpty(goodsParams.getSubGoodsParamList())) {
            List<CombineGoods> combineGoodsList = CopyUtil.copyList(goodsParams.getSubGoodsParamList(), CombineGoods.class);
            goods.setCombineGoodsList(combineGoodsList);
        }

        //合并商品主图与商品详情图
        List<GoodsImage> goodsImage = (goodsImage = goodsParams.getGoodsIndexImages()) != null ? goodsImage : new ArrayList<>();
        List<GoodsImage> goodsDetailImage = (goodsDetailImage = goodsParams.getGoodsDetailImages()) != null ? goodsDetailImage : new ArrayList<>();
        goodsImage.addAll(goodsDetailImage);
        goods.setGoodsImages(goodsImage);

        //若前端不传，则默认类目为hpv
        if (Objects.isNull(goods.getCtgId())) {
            GoodsCategory category = goodsCategoryService.getRootGoodsCategory(GoodsCategoryCodeEnum.VACCINE_HPV);
            goods.setCtgId(category.getId());
            goods.setCtgCode(category.getCode());
        }

        //商品分类：默认为通用
        if (Objects.isNull(goods.getClassify())) {
            goods.setClassify(GoodsClassifyEnum.COMMON.getCode());
        }

        Long id = goodsWriteService.addGoods(goods);
        if (Objects.isNull(id)) {
            logger.info("添加商品失败，id为空");
            return CommonResult.failed(OpsGoodsBizExEnum.ADD_GOODS_FAIL);
        }

        logger.info("添加商品响应" + JSONUtil.toJsonStr(id));
        return CommonResult.success(id);
    }

    /**
     * 添加商品套餐
     *
     * @param goodsId 商品id
     * @return saleable 上下架状态
     */
    @ApiOperation(value = "商品上下架")
    @PostMapping("/switchSaleable")
    @Log(name = "商品上下架状态", operation = OperationEnum.SWITCH, template = "商品id为{1}", keys = {"goodsId"})
    public CommonResult switchSaleable(Long goodsId) {
        logger.info("商品上下架" + goodsId);
        int i = goodsWriteService.updateSaleable(goodsId, null);
        return CommonResult.success(i);
    }

    @ApiOperation(value = "商品是否分销")
    @PostMapping("/changeIsDistribution")
    public CommonResult changeIsDistribution(Long goodsId, Integer isDistribution) {
        logger.info("商品是否分销" + goodsId);
        int i = goodsWriteService.changeIsDistribution(goodsId, null, isDistribution);
        return CommonResult.success(i);
    }

    @ApiOperation(value = "入库")
    @PostMapping("/addStock")
    @Log(name = "商品", operation = OperationEnum.STORE, template = "商品id为{1}", keys = {"goodsId"})
    public CommonResult addStock(@RequestBody StockParam stockParam) {

        Boolean result = goodsCapacityService.addStock(stockParam.getGoodsId(), stockParam.getNum(), null);
        if (result) {
            return CommonResult.success();
        } else {
            return CommonResult.failed(BaseBizCodeEnum.FAILED);
        }
    }

    @ApiOperation(value = "出库")
    @PostMapping("/reduceStock")
    @Log(name = "商品", operation = OperationEnum.RESTORE, template = "商品id为{1}", keys = {"goodsId"})
    public CommonResult reduceStock(@RequestBody StockParam stockParam) {

        Boolean result = goodsCapacityService.reduceStock(stockParam.getGoodsId(), stockParam.getNum(), null);
        if (result) {
            return CommonResult.success();
        } else {
            return CommonResult.failed(BaseBizCodeEnum.FAILED);
        }

    }

    @ApiOperation(value = "快速创建模版")
    @GetMapping("/getGoodsTemplate")
    public CommonResult<List<GoodsTemplateVO>> getGoodsTemplate() {

        List<GoodsTemplate> goodsTemplates = goodsTemplateService.listGoodsTemplate();
        List<GoodsTemplateVO> goodsTemplateVOS = new ArrayList<>();
        for (GoodsTemplate goodsTemplate : goodsTemplates) {
            GoodsTemplateVO goodsTemplateVO = CopyUtil.copy(goodsTemplate, GoodsTemplateVO.class);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(goodsTemplate.getGoodsTags())) {
                List<TagVO> tagVOList = CopyUtil.copyList(goodsTemplate.getGoodsTags(), TagVO.class);
                goodsTemplateVO.setTagVOList(tagVOList);
            }
            goodsTemplateVOS.add(goodsTemplateVO);
        }
        return CommonResult.success(goodsTemplateVOS);

    }

    @ApiOperation("查询控制项套餐")
    @GetMapping("/listControlGoods")
    public CommonResult<List<GoodsVO>> listControlGoods(@RequestParam(value = "type",required = false) Integer type,
                                                        @RequestParam(value = "keyword", required = false) String keyword,
                                                        @RequestParam(value = "orgaId") Long orgaId) {

        GoodsQuery query = new GoodsQuery();
        query.setName(keyword);
        query.setGoodsTypeEnums(Lists.newArrayList(GoodsTypeEnum.VACCINE));
        query.setGoodsSaleableEnums(Lists.newArrayList(GoodsSaleableEnum.OPEN_SELL,GoodsSaleableEnum.CLOSE_SELL));
        List<Goods> goodsList = goodsService.listOrgaGoods(Lists.newArrayList(orgaId), query);
        List<GoodsVO> goodsVOS = CopyUtil.copyList(goodsList, GoodsVO.class);
        return CommonResult.success(goodsVOS);
    }

    @ApiOperation(value = "优惠券商品类型列表")
    @PostMapping("/pageListCouponGoods")
    public CommonResult<PageView<GoodsVO>> pageListCouponGoods(@RequestBody CouponGoodsParam couponGoodsParam) {
        GoodsQuery goodsQuery = new GoodsQuery();
        goodsQuery.setName(couponGoodsParam.getName());
        goodsQuery.setPage(couponGoodsParam.getPage());
        goodsQuery.setOrgaId(couponGoodsParam.getOrgaId());
        if (Objects.nonNull(couponGoodsParam.getType())) {
            goodsQuery.setGoodsTypeEnums(List.of((Objects.requireNonNull(GoodsTypeEnum.getByCode(couponGoodsParam.getType())))));
        }
        goodsQuery.setOrgaStatusList(List.of(OrgaStatusEnum.AVAILABLE.getCode()));
        PageView<Goods> goodsPageView = goodsService.listGoodsByGoodsQuery(goodsQuery);
        List<Goods> goodsList = goodsPageView.getRecords();
        List<GoodsVO> goodsVOList = CopyUtil.copyList(goodsList, GoodsVO.class);
        List<Long> orgaIds = goodsList.stream().map(Goods::getOrganizationId).collect(Collectors.toList());
        List<Organization> organizations = organizationService.listByIds(orgaIds);
        if (CollectionUtil.isNotEmpty(organizations)) {
            Map<Long, Organization> organizationMap = organizations.stream().collect(Collectors.toMap(Organization::getId, Function.identity()));
            for (GoodsVO goodsVO : goodsVOList) {
                Organization organization = organizationMap.get(goodsVO.getOrganizationId());
                if (Objects.nonNull(organization)) {
                    goodsVO.setOrganization(CopyUtil.copy(organization, OrganizationVO.class));
                }
            }
        }
        return CommonResult.success(new PageView<>(goodsVOList, goodsPageView.getPage()));
    }

    // ========== entity 2 vo ==========

    private List<GoodsVO> entity2vo(List<Goods> goodsList) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return Collections.emptyList();
        }
        return goodsList.stream().map(this::entity2vo).collect(Collectors.toList());
    }

    private GoodsVO entity2vo(Goods goods) {
        GoodsVO vo = new GoodsVO();
        BeanUtils.copyProperties(goods, vo);
        return vo;
    }

    private GoodsCapacity generateGoodsCapacity(Integer total, Integer remain, Long goodsId, Long orgaId) {
        GoodsCapacity entity = new GoodsCapacity();
        entity.setCapacity(total);
        entity.setRemain(remain);
        entity.setOrganizationId(orgaId);
        entity.setGoodsId(goodsId);

        return entity;
    }


    private void fillCombineGoodsInfo(List<GoodsVO> goodsVOList) {
        //组合商品信息
        Map<Long, List<CombineGoods>> combineGoodsMap = new HashMap<>();

        //组合商品子商品机构ID
        Set<Long> organIds = new HashSet<>();
        Map<Long, Organization> organizationMap = new HashMap<>();

        //用于补充子商品中的名称价格信息
        Map<Long, Goods> simpleGoodsMap = new HashMap<>();
        //获取所有组合商品的ID
        List<Long> combineGoodsIds = goodsVOList.stream().filter(v -> Boolean.TRUE.equals(v.getIsCombine())).map(GoodsVO::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(combineGoodsIds)) {
            //根据组合商品ID，去组合商品表查询管理的子商品信息
            List<CombineGoods> combineGoodsList = combineGoodsService.listByCombineGoodsIds(combineGoodsIds);
            if (!CollectionUtils.isEmpty(combineGoodsList)) {
                //根据组合商品ID分组，组合商品ID，对应他的子商品列表
                combineGoodsMap = combineGoodsList.stream().collect(Collectors.groupingBy(CombineGoods::getCombineGoodsId));
                //获取所有子商品ID列表，查询商品信息
                List<Long> goodsIds = combineGoodsList.stream().map(CombineGoods::getGoodsId).distinct().collect(Collectors.toList());
                List<Goods> simpleGoodsList = goodsService.listGoodsByIds(goodsIds);
                //所有的子商品列表，转成map,方便get
                simpleGoodsMap = simpleGoodsList.stream().collect(Collectors.toMap(Goods::getId, v -> v));
                //补全子商品中的机构名称字段
                organIds = simpleGoodsList.stream().map(v -> v.getOrganizationId()).collect(Collectors.toSet());
            }
        }


        if (!CollectionUtils.isEmpty(organIds)) {
            List<Organization> organizationList = organizationService.listByOrgaIds(new ArrayList<>(organIds), Boolean.FALSE);
            organizationMap = organizationList.stream().collect(Collectors.toMap(Organization::getId, v -> v));
        }

        for (GoodsVO goodsVO : goodsVOList) {
            if (Boolean.TRUE.equals(goodsVO.getIsCombine())) {
                List<CombineGoods> combineGoodsList = combineGoodsMap.get(goodsVO.getId());
                if (!CollectionUtils.isEmpty(combineGoodsList)) {
                    List<CombineGoodsVO> combineGoodsVOList = CopyUtil.copyList(combineGoodsList, CombineGoodsVO.class);
                    for (CombineGoodsVO combineGoodsVO : combineGoodsVOList) {
                        Goods goods = simpleGoodsMap.get(combineGoodsVO.getGoodsId());
                        combineGoodsVO.setGoodsName(goods.getName());
                        combineGoodsVO.setPrice(goods.getPrice());
                        Organization organization = organizationMap.get(goods.getOrganizationId());
                        combineGoodsVO.setOrganName(organization.getName());

                    }
                    goodsVO.setCombineGoodsVOList(combineGoodsVOList);
                }
            }
        }


    }

    private void calculateStock(List<GoodsVO> goodsVOList) {
        Set<Long> goodsIds = new HashSet<>();
        if (CollectionUtils.isEmpty(goodsVOList)) {
            return;
        }
        for (GoodsVO goodsVO : goodsVOList) {
            if (Boolean.TRUE.equals(goodsVO.getIsCombine())) {
                if (!CollectionUtils.isEmpty(goodsVO.getCombineGoodsVOList())) {
                    List<Long> ids = goodsVO.getCombineGoodsVOList().stream().map(CombineGoodsVO::getGoodsId).collect(Collectors.toList());
                    goodsIds.addAll(ids);
                }
            } else {
                goodsIds.add(goodsVO.getId());
            }
        }
        List<GoodsCapacity> goodsCapacityList = goodsCapacityService.getListByGoodIds(new ArrayList<>(goodsIds));
        Map<Long, GoodsCapacity> goodsCapacityMap = goodsCapacityList.stream().collect(Collectors.toMap(GoodsCapacity::getGoodsId, v -> v));
        for (GoodsVO goodsVO : goodsVOList) {

            if (Boolean.TRUE.equals(goodsVO.getIsCombine())) {

                List<CombineGoodsVO> combineGoodsVOList = goodsVO.getCombineGoodsVOList();
                Integer remain = null;
                if (!CollectionUtils.isEmpty(combineGoodsVOList)) {
                    //多个商品，取库存最少的那个商品库存作为组合商品库存
                    for (CombineGoodsVO combineGoodsVO : combineGoodsVOList) {
                        GoodsCapacity goodsCapacity = goodsCapacityMap.get(combineGoodsVO.getGoodsId());
                        //体检商品，库存为null,不参与计算
                        if (goodsCapacity == null) {
                            continue;
                        }
                        if (remain == null) {
                            Double stockNum = Math.floor(goodsCapacity.getRemain() / combineGoodsVO.getNum());
                            remain = stockNum.intValue();
                        } else {
                            GoodsCapacity current = goodsCapacityMap.get(combineGoodsVO.getGoodsId());
                            Double currentStockNum = Math.floor(current.getRemain() / combineGoodsVO.getNum());
                            if (remain > currentStockNum.intValue()) {
                                remain = currentStockNum.intValue();
                            }
                        }
                    }
                    goodsVO.setRemain(remain);
                }
            } else {
                //体检商品，库存为null
                GoodsCapacity goodsCapacity = goodsCapacityMap.get(goodsVO.getId());
                if (goodsCapacity != null) {
                    goodsVO.setRemain(goodsCapacity.getRemain());
                    goodsVO.setCapacity(goodsCapacity.getCapacity());
                }

            }


        }
    }

    private void fillGoodsAddressInfo(List<GoodsVO> goodsVOListParam) {
        if (CollectionUtil.isEmpty(goodsVOListParam)) {
            return;
        }
        List<GoodsVO> goodsVOList = goodsVOListParam.stream().filter(goodsVO -> goodsVO.getAddressId() != null).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(goodsVOList)) {
            return;
        }
        List<Address> addressList = addressService.listAllCity();
        Map<Integer, Address> addressMap = addressList.stream().collect(Collectors.toMap(Address::getId, Function.identity()));
        for (GoodsVO goodsVO : goodsVOList) {
            Integer addressId = goodsVO.getAddressId().intValue();
            Address address;
            if (addressId != null && (address =addressMap.get(addressId)) != null) {
                goodsVO.setAddress(address);
            }
        }
    }

    private void fillGoodsCost(List<GoodsVO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        Map<Long, GoodsCost> goodsCostMap = new HashMap<>();

        List<Long> goodsIdList = records.stream().map(GoodsVO::getId).collect(Collectors.toList());
        List<GoodsCost> goodsCosts = goodsCostService.latest(goodsIdList, LocalDate.now());
        if (!CollectionUtils.isEmpty(goodsCosts)) {
            goodsCostMap = goodsCosts.stream().collect(Collectors.toMap(GoodsCost::getGoodsId, Function.identity()));
        }

        for (GoodsVO record : records) {
            record.setGoodsCostVO(CopyUtil.copy(goodsCostMap.get(record.getId()), GoodsCostVO.class));
        }
    }


}
