package com.meerkat.ops.admin.model.param;

import com.meerkat.common.db.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 帐户查询条件
 * </p>
 *
 * <AUTHOR>
 * @since 2022/6/17 上午11:33
 */
@ApiModel("帐户查询实体类")
public class AdminUserQuery {

    /**
     * 帐户名
     */
    @ApiModelProperty("帐户名")
    private String username;

    @ApiModelProperty("分页参数")
    private Page page;


    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }
}
