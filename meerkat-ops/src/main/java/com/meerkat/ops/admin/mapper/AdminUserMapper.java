package com.meerkat.ops.admin.mapper;

import com.meerkat.ops.admin.mapper.dataobj.AdminUserDO;
import com.meerkat.ops.admin.mapper.dataobj.param.AdminUserDaoQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @description:
 * @author: pantaoling
 * @date: 2021/11/4
 */
@Mapper
public interface AdminUserMapper {

    List<AdminUserDO> load(AdminUserDaoQuery adminUserDaoQuery);

    /**
     * @param adminUser 用户
     * @return
     */
    int insert(AdminUserDO adminUser);

    /**
     * 通过id列表获取用户
     *
     * @param ids
     * @return
     */
    List<AdminUserDO> listByIds(List<Long> ids);

    /**
     * 通过用户名查询
     *
     * @param username
     * @return
     */
    AdminUserDO loadByUserName(String username);

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    AdminUserDO loadById(Long id);

    /**
     * 更新用户
     *
     * @param adminUser
     */
    void update(AdminUserDO adminUser);

    /**
     * 根据主键id删除
     *
     * @param id       主键
     * @param deleteId 删除键
     */
    void delete(Long id, Long deleteId);

    /**
     * 根据昵称返回id
     *
     * @param nickName
     * @return
     */
    List<Long> listIdByNickName(String nickName);
}
