package com.meerkat.ops.trade.offline.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2022/6/18 11:27
 */
@ColumnWidth(35)
public class OfflineCollectionExcelVO {

    @ColumnWidth(15)
    @ExcelProperty(value = "支付平台", converter = PayChannelConverter.class)
    private Integer payChannel;

    @ColumnWidth(25)
    @ExcelProperty(value = "机构名称")
    private String organizationName;

    @ColumnWidth(25)
    @ExcelProperty(value = "商品名称")
    private String goodsName;

    @ColumnWidth(45)
    @ExcelProperty(value = "支付编号")
    private String sn;

    @ColumnWidth(15)
    @ExcelProperty(value = "支付金额", converter = MoneyConverter.class)
    private Long amount;

    @ExcelProperty(value = "支付时间")
    private LocalDateTime payTime;

    @ColumnWidth(15)
    @ExcelProperty(value = "退款金额", converter = MoneyConverter.class)
    private Long refundAmount;

    @ExcelProperty(value = "退款时间")
    private LocalDateTime refundTime;

    @ColumnWidth(15)
    @ExcelProperty(value = "姓名")
    private String collectionName;

    @ExcelProperty(value = "支付备注")
    private String goodsDesc;

    @ExcelProperty(value = "支付状态", converter = PayStatusConverter.class)
    private Integer payStatus;

    public Integer getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(Integer payChannel) {
        this.payChannel = payChannel;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }

    public String getCollectionName() {
        return collectionName;
    }

    public void setCollectionName(String collectionName) {
        this.collectionName = collectionName;
    }

    public String getGoodsDesc() {
        return goodsDesc;
    }

    public void setGoodsDesc(String goodsDesc) {
        this.goodsDesc = goodsDesc;
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public Long getRefundAmount() {
        return refundAmount;
    }

    public OfflineCollectionExcelVO setRefundAmount(Long refundAmount) {
        this.refundAmount = refundAmount;
        return this;
    }

    public LocalDateTime getRefundTime() {
        return refundTime;
    }

    public OfflineCollectionExcelVO setRefundTime(LocalDateTime refundTime) {
        this.refundTime = refundTime;
        return this;
    }

    public static class PayStatusConverter implements Converter<Integer> {

        @Override
        public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            if (value == null) {
                return  new WriteCellData<>("");
            }
            if (value == 1) {
                return  new WriteCellData<>("已完成");
            } else if (value == 2) {
                return  new WriteCellData<>("已退款");
            }

            return  new WriteCellData<>("");
        }
    }

    public static class PayChannelConverter implements Converter<Integer> {

        @Override
        public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            if (value == null) {
                return  new WriteCellData<>("");
            }
            if (value == 0) {
                return  new WriteCellData<>("微信");
            } else if (value == 1) {
                return  new WriteCellData<>("支付宝");
            }

            return  new WriteCellData<>("");
        }
    }

    public static class MoneyConverter implements Converter<Long> {
        @Override
        public WriteCellData<?> convertToExcelData(Long value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            if (value == null) {
                return  new WriteCellData<>("");
            }
            return  new WriteCellData<>(Double.valueOf(value.doubleValue() / 100.0).toString());
        }
    }

}