package com.meerkat.ops.refund.param;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * @author: booker
 * @since: 2022/09/22 14:16
 * Description:
 */
@Data
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
public class OrderApplyExcelVO implements Serializable {

    private static final long serialVersionUID = -8231646213257966890L;

    @ExcelProperty(value = "订单号")
    @ColumnWidth(25)
    private String orderNum;

    @ExcelProperty(value = "订单类型", converter = OrderBizCodeConverter.class)
    private String bizCode;

    @ExcelProperty(value = "履约人")
    private String acceptorName;

    @ExcelProperty(value = "手机号")
    @ColumnWidth(15)
    private String mobile;

    @ExcelProperty(value = "身份证号")
    @ColumnWidth(20)
    private String acceptorIdCard;

    @ExcelProperty(value = "套餐名称")
    @ColumnWidth(20)
    private String goodsName;

    @ExcelProperty(value = "退款编号")
    @ColumnWidth(25)
    private String sn;

    @ExcelProperty(value = "退款进度", converter = RefundStateConverter.class)
    private Integer applyStatus;

    @ExcelProperty(value = "付款金额")
    private BigDecimal totalAmount;

    @ExcelProperty(value = "申请退款金额")
    private BigDecimal applyAmount;

    @ExcelProperty(value = "支付时间")
    @ColumnWidth(20)
    private String paymentTime;

    @ExcelProperty(value = "申请退款时间")
    @ColumnWidth(20)
    private String applyTime;

    @ExcelProperty(value = "审批时间")
    @ColumnWidth(20)
    private String auditTime;

    @ExcelProperty(value = "机构")
    @ColumnWidth(20)
    private String organizationName;


    public static class OrderBizCodeConverter implements Converter<String> {
        @Override
        public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            if (Objects.equals(value, "exam")) {
                return new WriteCellData<>("体检");
            } else if (Objects.equals(value, "vaccine")) {
                return new WriteCellData<>("疫苗");
            } else if (Objects.equals(value, "retail")) {
                return new WriteCellData<>("实物");
            } else {
                return new WriteCellData<>("");
            }
        }
    }

    public static class RefundStateConverter implements Converter<Integer> {
        @Override
        public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            if (Objects.equals(value, 0)) {
                return new WriteCellData<>("同意退款");
            } else if (Objects.equals(value, 1)) {
                return new WriteCellData<>("拒绝退款");
            } else if (Objects.equals(value, 2)) {
                return new WriteCellData<>("审批中");
            } else if (Objects.equals(value, 3)) {
                return new WriteCellData<>("取消审批");
            } else {
                return new WriteCellData<>("");
            }
        }
    }
}
