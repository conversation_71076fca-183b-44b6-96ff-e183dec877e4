package com.meerkat.ops.coupon.param;

import com.meerkat.common.db.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @ClassName CouponQueryParam
 * @description
 * @createTime 2022/10/9 10:10
 */
@ApiModel("CouponQueryParam")
public class CouponQueryParam {

    /**
     * 批次id
     */
    @ApiModelProperty("批次id")
    @NotNull
    private Long templateId;

    /**
     * 用户手机号
     */
    @ApiModelProperty("用户手机号")
    private String mobile;

    @ApiModelProperty("状态:0：未使用、1：已使用、2：已过期、3：已冻结")
    private Integer status;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNum;

    /**
     * 优惠券兑换码
     */
    @ApiModelProperty("兑换码")
    private String couponRedemptionCode;

    @ApiModelProperty("分页信息")
    private Page page;

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getCouponRedemptionCode() {
        return couponRedemptionCode;
    }

    public void setCouponRedemptionCode(String couponRedemptionCode) {
        this.couponRedemptionCode = couponRedemptionCode;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }
}
