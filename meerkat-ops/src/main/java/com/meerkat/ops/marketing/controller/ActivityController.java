package com.meerkat.ops.marketing.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.meerkat.base.exception.BaseBizEnum;
import com.meerkat.common.api.BaseBizCodeEnum;
import com.meerkat.common.api.CommonResult;
import com.meerkat.common.db.PageView;
import com.meerkat.common.enums.ChorgaTypeEnum;
import com.meerkat.common.exception.BizException;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.marketing.enums.ActivityStateEnum;
import com.meerkat.marketing.exception.MarketingBizException;
import com.meerkat.marketing.model.Activity;
import com.meerkat.marketing.model.ActivityAwards;
import com.meerkat.marketing.model.activityhelp.UserRanking;
import com.meerkat.marketing.param.*;
import com.meerkat.marketing.service.ActivityAwardsService;
import com.meerkat.marketing.service.ActivityGoodService;
import com.meerkat.marketing.service.ActivityService;
import com.meerkat.marketing.service.ActivityUserService;
import com.meerkat.ops.marketing.param.*;
import com.meerkat.ops.marketing.vo.*;
import com.meerkat.ops.util.LoginUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/8 下午3:49
 */
@Api(tags = "ActivityController")
@RestController
@RequestMapping("/activity")
public class ActivityController {

    @Autowired
    private ActivityService activityService;

    @Autowired
    private ActivityGoodService activityGoodService;

    @Autowired
    private ActivityUserService activityUserService;

    @Autowired
    private ActivityAwardsService activityAwardsService;

    @ApiOperation("新增或更新活动")
    @PostMapping("/addOrUpdateActivity")
    public CommonResult<Boolean> addOrUpdateActivity(@RequestBody ActivityAddParam activityAddParam) {
        ActivityAdd activityAdd = CopyUtil.copy(activityAddParam, ActivityAdd.class);
        activityAdd.setChorgaId(1L);
        activityAdd.setChorgaType(ChorgaTypeEnum.CHANNEL.getCode());
        activityAdd.setCreatorId(LoginUtil.getCurrentEmployees().getId());
        activityAdd.setType(activityAddParam.getType());
        activityService.addOrUpdate(activityAdd);
        return CommonResult.success(Boolean.TRUE);
    }

    @ApiOperation("活动列表")
    @PostMapping("/listActivity")
    public CommonResult<PageView<ActivityListVO>> listActivity(@RequestBody ActivityPageQueryVO activityPageQueryVO) {
        ActivityPageQuery pageQuery = CopyUtil.copy(activityPageQueryVO, ActivityPageQuery.class);
        PageView<Activity> page = activityService.listActivity(pageQuery);
        List<Activity> activityList = page.getRecords();
        PageView<ActivityListVO> pageView = new PageView<>();
        pageView.setPage(page.getPage());
        List<ActivityListVO> activityVOList = model2VOList(activityList);
        pageView.setRecords(activityVOList);
        return CommonResult.success(pageView);
    }

    @ApiOperation("活动详情")
    @PostMapping("/getActivityById")
    public CommonResult<ActivityVO> getActivityById(@RequestParam("activityId") @ApiParam("活动id") Integer activityId) {
        if (Objects.isNull(activityId)) {
            throw new BizException(BaseBizCodeEnum.ILLEGAL_ARGUMENT, "活动id不可为空");
        }
        Activity activity = activityService.load(activityId);
        ActivityVO activityVO = model2VO(activity);
        return CommonResult.success(activityVO);
    }

    @ApiOperation("批量新增更新活动奖品")
    @PostMapping("/addOrUpdateAward")
    public CommonResult<Void> addOrUpdateAward(@RequestBody ActivityAwardAddListParam activityAwardAddListParam) {
        if (CollectionUtils.isEmpty(activityAwardAddListParam.getActivityAwardAddList())) {
            throw new BizException(BaseBizEnum.ILLEGAL_ARGUMENT, "奖品信息不能为空");
        }
        List<ActivityAwardAdd> activityAwardAdds = chekParamAndTransformModel(activityAwardAddListParam.getActivityAwardAddList());
        activityAwardsService.addOrUpdateAward(activityAwardAdds);
        return CommonResult.success();
    }

    private List<ActivityAwardAdd> chekParamAndTransformModel(List<ActivityAwardAddParam> activityAwardAddList) {
        ArrayList<ActivityAwardAdd> activityAwardAdds = new ArrayList<>();
        for (ActivityAwardAddParam activityAwardAddParam : activityAwardAddList) {
            if (activityAwardAddParam.getActivityId() == null) {
                throw new BizException(BaseBizEnum.ILLEGAL_ARGUMENT, "活动ID不能为空");
            }
            if (activityAwardAddParam.getReceiverType() == null) {
                throw new BizException(BaseBizEnum.ILLEGAL_ARGUMENT, "接收者类型不能为空");
            }
            if (activityAwardAddParam.getReceiverId() == null) {
                throw new BizException(BaseBizEnum.ILLEGAL_ARGUMENT, "接收者ID不能为空");
            }
            if (ObjectUtil.isNull(activityAwardAddParam.getCode())) {
                throw new BizException(BaseBizEnum.ILLEGAL_ARGUMENT, "奖品标识不能为空");
            }
            ActivityAwardAdd activityAwardAdd = CopyUtil.copy(activityAwardAddParam, ActivityAwardAdd.class);
            Long employeeId = LoginUtil.getCurrentEmployees().getId();
            if(ObjectUtil.isNotNull(activityAwardAddParam.getId())){
                activityAwardAdd.setModifiedId(employeeId);
            }else {
                activityAwardAdd.setCreatorId(employeeId);
            }
            activityAwardAdds.add(activityAwardAdd);
        }
        return activityAwardAdds;
    }

    @ApiOperation("活动奖品列表")
    @PostMapping("/getActivityAwardList")
    public CommonResult<List<ActivityAwardsVO>> getActivityAwardList(@RequestParam("activityId") @ApiParam("活动id") Integer activityId) {
        if (Objects.isNull(activityId)) {
            throw new BizException(BaseBizCodeEnum.ILLEGAL_ARGUMENT, "活动id不可为空");
        }
        List<ActivityAwards> awardsList = activityAwardsService.getActivityAwardsList(Long.valueOf(activityId), null);
        List<ActivityAwardsVO> activityAwardsVOS = CopyUtil.copyList(awardsList, ActivityAwardsVO.class);
        return CommonResult.success(activityAwardsVOS);
    }

    @ApiOperation("excel导入活动水军用户")
    @PostMapping("/importActivityUser")
    public CommonResult<String> importItem(HttpServletRequest request) throws IOException {
        MultipartFile file = ((MultipartHttpServletRequest) request).getFile("file");
        int count = activityUserService.importActivityUser(file.getInputStream());
        if (count == 0) {
            return CommonResult.failed("导入活动水军用户失败");
        }
        return CommonResult.success("导入" + count + "条项目");
    }

    @ApiOperation("助力活动增加票数")
    @PostMapping("/increaseUserTickets")
    public CommonResult<Boolean> increaseUserTickets(@RequestBody TicketsAddParam ticketsAdd) {
        if (ticketsAdd.getActivityId() == null) {
            throw new BizException(BaseBizEnum.ILLEGAL_ARGUMENT, "活动id不能为空");
        }
        if (CollectionUtils.isEmpty(ticketsAdd.getIncreaseTicket())) {
            throw new BizException(BaseBizEnum.ILLEGAL_ARGUMENT, "票数变更参数不能为空");
        }
        ActivityUserTicketsAdd userTicketsAdd = CopyUtil.copy(ticketsAdd, ActivityUserTicketsAdd.class);
        List<IncreaseTicketAdd> increaseTicketAdds = CopyUtil.copyList(ticketsAdd.getIncreaseTicket(), IncreaseTicketAdd.class);
        userTicketsAdd.setIncreaseTicket(increaseTicketAdds);
        activityUserService.increaseUserTickets(userTicketsAdd);
        return CommonResult.success(Boolean.TRUE);
    }

    @ApiOperation("助力活动后台排行列表")
    @PostMapping("/listUserRanking")
    public CommonResult<PageView<UserRankingVO>> listUserRanking(@RequestBody UserRankingQueryVO userRankingQueryVO) {
        if (userRankingQueryVO.getActivityId() == null) {
            throw new BizException(BaseBizEnum.ILLEGAL_ARGUMENT, "活动id不能为空");
        }
        ActivityHelpRankingQuery query = CopyUtil.copy(userRankingQueryVO, ActivityHelpRankingQuery.class);
        PageView<UserRanking> page = activityUserService.listActivityUserRankingByCondition(query);
        List<UserRanking> records = page.getRecords();
        PageView<UserRankingVO> activityGoodVOPageView = new PageView<>();
        activityGoodVOPageView.setPage(page.getPage());
        activityGoodVOPageView.setRecords(CopyUtil.copyList(records, UserRankingVO.class));
        return CommonResult.success(activityGoodVOPageView);
    }

    @ApiOperation("发放奖品")
    @PostMapping("/sendAward")
    public CommonResult<Void> sendAward(@RequestBody ActivityAwardsSendVO activityAwardsSendVO) {

        if (activityAwardsSendVO.getActivityId() == null) {
            throw new BizException(BaseBizEnum.ILLEGAL_ARGUMENT, "活动不存在");
        }
        List<ActivityAwardsSendVO.ActivityUserAwardsSendVO> sendVOList = activityAwardsSendVO.getActivityUserAwardsSendVO();
        if (sendVOList == null || sendVOList.isEmpty()) {
            throw new BizException(BaseBizEnum.ILLEGAL_ARGUMENT, "发放奖品为空");
        }
        ActivityAwardsSendParam param = new ActivityAwardsSendParam();
        param.setActivityId(activityAwardsSendVO.getActivityId());
        param.setActivityUserAwardsSendParam(CopyUtil.copyList(sendVOList, ActivityUserAwardsSendParam.class));
        activityAwardsService.sendAward(param);
        return CommonResult.success();
    }

    @ApiOperation("编辑活动商品")
    @PostMapping("/editActivityGoods")
    public CommonResult<Boolean> addActivityGoods(@RequestBody ActivityGoodInit activityGoodInit) {
        activityGoodService.init(activityGoodInit.getActivityId(), activityGoodInit.getGoodIds(), activityGoodInit.getRet());
        return CommonResult.success(Boolean.TRUE);
    }

    private ActivityVO model2VO(Activity activity) {
        ActivityVO activityVO = CopyUtil.copy(activity, ActivityVO.class);
        if(StringUtils.isNotBlank(activity.getExt())){
            JSONObject extJo = JSONUtil.parseObj(activity.getExt());
            String activityRule = extJo.getStr("activityRule");
            String helperAwards = extJo.getStr("helperAwards");
            String imageResources = extJo.getStr("imageResources");
            activityVO.setActivityRule(ObjectUtil.isNotNull(activityRule)?activityRule:null);
            activityVO.setHelperAwards(ObjectUtil.isNotNull(helperAwards)?helperAwards:null);
            activityVO.setImageResources(ObjectUtil.isNotNull(imageResources)?imageResources:null);
        }
        return activityVO;
    }

    private List<ActivityListVO> model2VOList(List<Activity> activityList) {
        Date currentDate = new Date();
        List<ActivityListVO> activityVOList = new ArrayList<>();
        activityList.forEach(activity -> {
            ActivityListVO activityVO = CopyUtil.copy(activity, ActivityListVO.class);
            activityVO.setState(activityService.getActivityState(currentDate,DateUtil.date(activity.getStartTime()),DateUtil.date(activity.getEndTime())));
            activityVOList.add(activityVO);
        });
        return activityVOList;
    }
}
