package com.meerkat.ops.menu.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <p>
 * 菜单页面VO
 * </p>
 *
 * <AUTHOR>
 * @since 2022/6/15 下午4:54
 */
@ApiModel(description = "资源页面VO")
public class MenuVO {

    /**
     * 资源id
     */
    @ApiModelProperty(value = "id", required = true)
    private Long id;

    /**
     * 父级菜单id
     */
    @ApiModelProperty(value = "父级菜单id", required = true)
    private Long parentId;

    /**
     * 菜单名称
     */
    @ApiModelProperty(value = "资源名称", required = true)
    private String name;

    /**
     * 组件路径
     */
    @ApiModelProperty(value = "资源路径", required = true)
    private String component;

    /**
     * 菜单启用状态 0启用，1停用
     */
    @ApiModelProperty(value = "启用状态 0启用，1停用")
    private Integer status;

    /**
     * 简介
     */
    @ApiModelProperty(value = "备注")
    private String description;

    /**
     * 0目录，1菜单，2按钮
     */
    @ApiModelProperty(value = "0目录，1菜单，2按钮")
    private Integer type;

    /**
     * 选中状态：0->未选中；1->已选中
     */
    @ApiModelProperty(value = "选中状态：0->未选中；1->已选中")
    private Integer isChecked;

    @ApiModelProperty(value = "子菜单")
    private List<MenuVO> children;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public List<MenuVO> getChildren() {
        return children;
    }

    public void setChildren(List<MenuVO> children) {
        this.children = children;
    }

    public Integer getIsChecked() {
        return isChecked;
    }

    public void setIsChecked(Integer isChecked) {
        this.isChecked = isChecked;
    }
}
