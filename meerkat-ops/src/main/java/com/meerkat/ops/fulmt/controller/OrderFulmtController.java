package com.meerkat.ops.fulmt.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meerkat.auth.annotation.Log;
import com.meerkat.auth.enums.OperationEnum;
import com.meerkat.common.api.CommonResult;
import com.meerkat.common.db.PageView;
import com.meerkat.common.enums.ChorgaTypeEnum;
import com.meerkat.common.excel.ExcelUtil;
import com.meerkat.common.exception.BizException;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.common.utils.MoneyUtil;
import com.meerkat.fulfillment.fulfillment.enums.FulMtGoodsBizExEnum;
import com.meerkat.fulfillment.fulfillment.handler.OrderFulmtStrategyHandler;
import com.meerkat.fulfillment.fulfillment.model.*;
import com.meerkat.fulfillment.fulfillment.service.OrderFulmtService;
import com.meerkat.ops.admin.model.AdminUser;
import com.meerkat.ops.admin.service.AdminUserService;
import com.meerkat.fulfillment.fulfillment.enums.FulmtRetailStateEnum;
import com.meerkat.fulfillment.fulfillment.enums.FulmtStateEnum;

import com.meerkat.ops.fulmt.param.*;
import com.meerkat.ops.fulmt.service.OrderFulmtStateUpdateService;
import com.meerkat.ops.order.param.OrderTimeQueryParam;
import com.meerkat.fulfillment.fulfillment.service.FulmtOrderOperateRecordService;
import com.meerkat.ops.util.LoginUtil;
import com.meerkat.order.enums.OrderBizCodeEnum;
import com.meerkat.order.enums.OrderRefundStateEnum;
import com.meerkat.order.enums.OrderSelectEnum;
import com.meerkat.order.model.Order;
import com.meerkat.order.model.snapshot.AcceptorSnapshot;
import com.meerkat.order.param.ExamDateParam;
import com.meerkat.order.param.OrderTimeQuery;
import com.meerkat.order.service.OrderReadService;
import com.meerkat.shop.goods.model.Goods;
import com.meerkat.shop.goods.service.GoodsService;
import com.meerkat.smart.channel.model.Channel;
import com.meerkat.smart.channel.service.ChannelService;
import com.meerkat.work.order.enums.WorkOrderBizTypeEnum;
import com.meerkat.work.order.enums.WorkOrderDealStatusEnum;
import com.meerkat.work.order.param.WorkOrderFulmtAddParam;
import com.meerkat.work.order.param.WorkOrderFulmtSysDealParam;
import com.meerkat.work.order.service.WorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 履约订单相关接口
 * @date 2022/4/22 18:03
 */
@RestController
@RequestMapping(value = "/orderFuMt", produces = MediaType.APPLICATION_JSON_VALUE)
@Api("履约订单管理")
public class OrderFulmtController {

    private static final Logger LOG = LoggerFactory.getLogger(OrderFulmtController.class);

    @Autowired
    private OrderFulmtService orderFulmtService;

    @Autowired
    private OrderFulmtStateUpdateService orderFulmtStateUpdateService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private OrderReadService orderReadService;

    @Autowired
    private AdminUserService adminUserService;

    @Autowired
    private OrderFulmtStrategyHandler orderFulmtStrategyHandler;

    @Autowired
    private FulmtOrderOperateRecordService fulmtOrderOperateRecordService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private WorkOrderService workOrderService;

    /**
     * 获取履约记录
     *
     * @return
     */
    @PostMapping("/listOrderFulmt")
    @ApiOperation(value = "获取订单履约列表")
    public CommonResult<PageView<OrderFulmtVo>> listOrderFulmt(@RequestBody OrderFulmtQuery orderFulmtQuery) {
        OrderFulmtCheck copy = CopyUtil.copy(orderFulmtQuery, OrderFulmtCheck.class);
        PageView<OrderFulmtValue> orderFulmtValuePageView = orderFulmtService.listInfoByPage(copy);
        List<OrderFulmtValue> records = orderFulmtValuePageView.getRecords();
        PageView<OrderFulmtVo> goodsVOPageView = new PageView<>();
        goodsVOPageView.setPage(orderFulmtValuePageView.getPage());
        List<OrderFulmtVo> orderFulmtVos = CopyUtil.copyList(records, OrderFulmtVo.class);
        getFulmtOtherInfo(orderFulmtVos);
        goodsVOPageView.setRecords(orderFulmtVos);
        return CommonResult.success(goodsVOPageView);
    }


    /**
     * 修改 履约单
     *
     * <AUTHOR>
     * @date 2022/04/20
     **/
    @ApiOperation("修改-履约单")
    @PostMapping("/updateCancel")
    @Log(name = "履约单预约信息", operation = OperationEnum.UPDATE, template = "订单编号为: {1}", keys = {"orderNum"})
    public CommonResult<Integer> updateCancel(@Valid @RequestBody OrderFulmtParam orderFulmtParam) throws Exception {
        OrderFulmt orderFulmt = CopyUtil.copy(orderFulmtParam, OrderFulmt.class);
        AdminUser currentEmployees = LoginUtil.getCurrentEmployees();
        return CommonResult.success(orderFulmtService.updateFulMtOrder(orderFulmt, currentEmployees.getId()));
    }

    /**
     * 修改 履约单核销 已履约
     *
     * <AUTHOR>
     * @date 2022/04/20
     **/
    @ApiOperation("批量核销")
    @PostMapping("/updateSuccess")
    @Log(name = "疫苗履约单", operation = OperationEnum.VERIFY, template = "履约单id为: {1}", keys = {"ids"})
    public CommonResult<Integer> updateSuccess(@RequestBody FulmtStateCheckParam fulmtStateCheckParam) throws Exception {
        if (fulmtStateCheckParam.getIds().isEmpty()) {
            throw new BizException(FulMtGoodsBizExEnum.FULMT_PARAM_FAIL);
        }
        AdminUser currentEmployees = LoginUtil.getCurrentEmployees();
        //核销流程走事务
        orderFulmtStateUpdateService.updateStateByList(fulmtStateCheckParam, currentEmployees);
        return CommonResult.success(1);
    }

    /**
     * 商务批量同意履约
     *
     * <AUTHOR>
     * @date 2022/07/21
     **/
    @ApiOperation("商务批量同意履约")
    @PostMapping("/businessAgree")
    public CommonResult<Integer> businessAgree(@RequestBody FulmtStateCheckParam fulmtStateCheckParam) throws Exception {
        if (fulmtStateCheckParam.getIds().isEmpty()) {
            throw new BizException(FulMtGoodsBizExEnum.FULMT_PARAM_FAIL);
        }
        //商务批量同意履约
        orderFulmtStateUpdateService.updateAgree(fulmtStateCheckParam);
        return CommonResult.success(fulmtStateCheckParam.getIds().size());
    }

    /**
     * 商务批量拒绝履约
     *
     * <AUTHOR>
     * @date 2022/07/21
     **/
    @ApiOperation("商务批量拒绝履约")
    @PostMapping("/businessRefuse")
    public CommonResult<Integer> businessRefuse(@RequestBody FulmtStateCheckParam fulmtStateCheckParam) throws Exception {
        if (fulmtStateCheckParam.getIds().isEmpty()) {
            throw new BizException(FulMtGoodsBizExEnum.FULMT_PARAM_FAIL);
        }
        //商务批量同意履约
        orderFulmtStateUpdateService.updateRefude(fulmtStateCheckParam);
        return CommonResult.success(fulmtStateCheckParam.getIds().size());
    }


    /**
     * 履约单修改事件
     *
     * <AUTHOR>
     * @date 2022/07/07
     **/
    @ApiOperation("履约单修改事件")
    @PostMapping("/updateEvent")
    public CommonResult<Integer> updateEvent(@RequestBody OrderFulmtParam orderFulmtParam) throws Exception {
        Long id = LoginUtil.getCurrentEmployees().getId();
        OrderFulmt orderFulmt = CopyUtil.copy(orderFulmtParam, OrderFulmt.class);
        orderFulmt.setType(6);
        return CommonResult.success(orderFulmtStrategyHandler.handler(orderFulmt, orderFulmt.getEvent(), id));
    }
    
    /**
     * 数据订正履约商品快照
     *
     * <AUTHOR>
     * @date 2022/04/20
     **/
    @ApiOperation("数据订正履约商品快照")
    @PostMapping("/updateGoodsInfo")
    public CommonResult<Integer> updateGoodsInfo() {
        int i = orderFulmtService.batUpdateByGoodsId();
        return CommonResult.success(i);
    }


    /**
     * 数据订正待履约履约单
     * <AUTHOR>
     * @date 2022/04/20
     **/
    @ApiOperation("数据订正待履约履约单")
    @PostMapping("/updateAppointInfo")
    public CommonResult<Integer> updateAppointInfo() {
        ArrayList<Integer> state = new ArrayList<>();
        state.add(2);
        OrderTimeQueryParam orderTimeQueryParam = new OrderTimeQueryParam();
        orderTimeQueryParam.setStates(state);
        ArrayList<String> strings = new ArrayList<>();
        strings.add("vaccine");
        orderTimeQueryParam.setBizCodes(strings);
        List<Order> orders = queryOrders(orderTimeQueryParam);
        for (Order order : orders) {
            String orderNum = order.getChildOrders().get(0).getOrderNum();
            OrderFulmtParam orderFulmtParam = new OrderFulmtParam();
            orderFulmtParam.setOrderNum(orderNum);
            AdminUser adminUser = new AdminUser();
            adminUser.setId(99L);
            orderFulmtStateUpdateService.creatAppointmentFulmt(orderFulmtParam, adminUser);
            //添加日志
            FulmtOrderOperateRecord fulmtOrderOperateRecord = new FulmtOrderOperateRecord();
            fulmtOrderOperateRecord.setFulmtOrderNum(orderNum);
            fulmtOrderOperateRecord.setFromState(0);
            fulmtOrderOperateRecord.setEventName("疫苗订单支付完成，生成待预约履约单");
            fulmtOrderOperateRecord.setToState(0);
            fulmtOrderOperateRecord.setOperatorId(99);
            fulmtOrderOperateRecordService.insert(fulmtOrderOperateRecord);
        }
        return CommonResult.success(1);
    }


    /**
     * 订正履约商品快照
     *
     * <AUTHOR>
     * @date 2022/04/20
     **/
    @ApiOperation("订正履约商品快照")
    @PostMapping("/updateGoods")
    public CommonResult<Integer> updateGoods() {
        List<OrderFulmtValue> fulmtValues = orderFulmtService.getfulmtInfo();
        for (OrderFulmtValue fulmtValue : fulmtValues) {
            if (fulmtValue.getFulmtGoodsId() != null) {
                String fulmtGoosdInfo = fulmtValue.getFulmtGoosdInfo();
                if (fulmtGoosdInfo != null) {
                    FulmtGoodsRelationValue fulmtGoodsRelationValue = JSONArray.parseObject(fulmtGoosdInfo, FulmtGoodsRelationValue.class);
                    Goods goods = goodsService.getGoods(fulmtValue.getFulmtGoodsId());
                    fulmtGoodsRelationValue.setGoods(goods);
                    fulmtValue.setFulmtGoosdInfo(JSONUtil.toJsonStr(fulmtGoodsRelationValue));
                    OrderFulmt orderFulmt = CopyUtil.copy(fulmtValue, OrderFulmt.class);
                    orderFulmtService.update(orderFulmt);
                }
            }
        }
        return CommonResult.success(1);
    }


    /**
     * 手动新增待预约订单 用于数据订正
     *
     * <AUTHOR>
     * @date 2022/04/20
     **/
    @ApiOperation("手动新增待预约订单 用于数据订正")
    @PostMapping("/addAppointInfo")
    public CommonResult<Integer> addAppointInfo(@RequestBody OrderFulmtParam orderFulmtParam) {
        AdminUser adminUser = new AdminUser();
        adminUser.setId(99L);
        List<String> orderNumList = orderFulmtParam.getOrderNumList();
        if (!CollectionUtils.isEmpty(orderNumList)) {
            for (String s : orderNumList) {
                OrderFulmtParam fulmtParam = new OrderFulmtParam();
                fulmtParam.setOrderNum(s);
                //生成履约单
                orderFulmtStateUpdateService.creatAppointmentFulmt(fulmtParam, adminUser);
                //添加日志
                FulmtOrderOperateRecord fulmtOrderOperateRecord = new FulmtOrderOperateRecord();
                fulmtOrderOperateRecord.setFulmtOrderNum(fulmtParam.getOrderNum());
                fulmtOrderOperateRecord.setFromState(0);
                fulmtOrderOperateRecord.setEventName("疫苗订单支付完成，生成待预约履约单");
                fulmtOrderOperateRecord.setToState(0);
                fulmtOrderOperateRecord.setOperatorId(99);
                fulmtOrderOperateRecordService.insert(fulmtOrderOperateRecord);

                //生成工单
                OrderFulmtValue orderFulmtInfo = orderFulmtService.checkOrder(s);
                WorkOrderFulmtAddParam workOrderFulmtAddParam = new WorkOrderFulmtAddParam();
                workOrderFulmtAddParam.setWorkOrderBizType(WorkOrderBizTypeEnum.SUBSCRIBE.getType());
                workOrderFulmtAddParam.setBizNo(orderFulmtInfo.getFulmtNum());
                workOrderFulmtAddParam.setBizCode(orderFulmtInfo.getBizCode());
                workOrderFulmtAddParam.setChorgaId(orderFulmtInfo.getChorgaId());
                workOrderService.save(workOrderFulmtAddParam);
            }
        }

        return CommonResult.success(1);
    }

    /**
     * 修改同步履约人信息
     *
     * <AUTHOR>
     * @date 2022/04/20
     **/
    @ApiOperation("修改同步履约人信息")
    @PostMapping("/updateAcceptorInfo")
    public CommonResult<Integer> updateAcceptorInfo(@RequestBody OrderFulmtParam orderFulmtParam) {
        OrderFulmt orderFulmt = CopyUtil.copy(orderFulmtParam, OrderFulmt.class);
        orderFulmtService.updateByAcceptorInfo(orderFulmt);
        return CommonResult.success(1);
    }


    /**
     * 修改备注同步
     *
     * <AUTHOR>
     * @date 2022/04/20
     **/
    @ApiOperation("修改备注同步")
    @PostMapping("/updateRemarkInfo")
    public CommonResult<Integer> updateRemarkInfo(@RequestBody OrderFulmtParam orderFulmtParam) throws Exception {
        OrderFulmt orderFulmt = CopyUtil.copy(orderFulmtParam, OrderFulmt.class);
        orderFulmtService.updateByRemarkInfo(orderFulmt);
        return CommonResult.success(1);
    }

    /**
     * 发货并填写物流信息
     *
     * <AUTHOR>
     * @date 2022/05/20
     **/
    @ApiOperation("发货并填写物流信息")
    @PostMapping("/shipped")
    @Log(name = "疫苗履约单", operation = OperationEnum.VERIFY, template = "订单编号为: {1}", keys = {"orderNum"})
    public CommonResult<Integer> shipped(@Valid @RequestBody OrderFulmtParam orderFulmtParam) throws Exception {
        OrderFulmt orderFulmt = CopyUtil.copy(orderFulmtParam, OrderFulmt.class);
        AdminUser currentEmployees = LoginUtil.getCurrentEmployees();
        //核销流程走事务
        return CommonResult.success(orderFulmtStateUpdateService.shipped(orderFulmt, currentEmployees));
    }

    /**
     * 签收
     *
     * <AUTHOR>
     * @date 2022/05/20
     **/
    @ApiOperation("签收")
    @PostMapping("/sign")
    @Log(name = "疫苗履约单", operation = OperationEnum.VERIFY, template = "订单编号为: {1}", keys = {"orderNum"})
    public CommonResult<Integer> sign(@Valid @RequestBody OrderFulmtParam orderFulmtParam) throws Exception {
        OrderFulmt orderFulmt = CopyUtil.copy(orderFulmtParam, OrderFulmt.class);
        AdminUser currentEmployees = LoginUtil.getCurrentEmployees();
        //签收
        return CommonResult.success(orderFulmtStateUpdateService.sign(orderFulmt, currentEmployees));
    }

    /**
     * 添加履约单
     *
     * <AUTHOR>
     * @date 2022/04/20
     **/
    @ApiOperation("代预约")
    @PostMapping("/add")
    @Log(name = "订单信息", operation = OperationEnum.PROXY_APPOINT, template = "订单编号为: {1}", keys = {"orderNum"})
    public CommonResult<Integer> add(@Valid @RequestBody OrderFulmtParam orderFulmtParam) throws Exception {

        AdminUser currentEmployees = LoginUtil.getCurrentEmployees();
        try {
            OrderFulmt orderFulmt = CopyUtil.copy(orderFulmtParam, OrderFulmt.class);
            orderFulmtService.creatOrder(orderFulmt, currentEmployees.getId());
        } catch (Exception e) {
            return CommonResult.failed("履约单创建失败：", e.getMessage());
        }
        //创建日志
        createLog(orderFulmtParam);
        //创建工单
        createWorkOrder(orderFulmtParam, currentEmployees);

        //创建机构订单和履约单
        return CommonResult.success(1);
    }


    /**
     * 履约单根据父子单进行数据订正
     *
     * <AUTHOR>
     * @date 2022/6/8
     **/
    @ApiOperation("履约单根据父子单进行数据订正")
    @PostMapping("/updateFlumtInfo")
    public CommonResult<Integer> updateFlumtInfo() {
        List<OrderFulmtValue> orderFulmtValues = orderFulmtStateUpdateService.creatFlumtInfo();

        List<String> list = orderFulmtValues.stream().map(OrderFulmtValue::getOrderNum).distinct().collect(Collectors.toList());
        List<Order> orderList = orderReadService.listByOutOrderNum(list);

        for (OrderFulmtValue orderFulmtValue : orderFulmtValues) {
            for (Order order : orderList) {
                List<Order> childOrders = order.getChildOrders();
                if (childOrders != null) {
                    for (Order childOrder : childOrders) {
                        if (childOrder.getParentOrderNum().equals(orderFulmtValue.getOrderNum())) {
                            orderFulmtValue.setOrderNum(childOrder.getOrderNum());
                            orderFulmtValue.setOutOrderNum(order.getOutOrderNum());
                            orderFulmtValue.setParentOrderNum(childOrder.getParentOrderNum());
                            OrderFulmt fulmt = CopyUtil.copy(orderFulmtValue, OrderFulmt.class);
                            orderFulmtService.update(fulmt);
                        }
                    }
                }
            }
        }

        //创建机构订单和履约单
        return CommonResult.success(1);
    }


    /**
     * 删除
     *
     * <AUTHOR>
     * @date 2022/04/20
     **/
    @ApiOperation("删除")
    @PostMapping("/delete")
    public CommonResult<Integer> delete(@Valid @RequestBody OrderFulmtParam orderFulmtParam) {
        OrderFulmt copy = CopyUtil.copy(orderFulmtParam, OrderFulmt.class);
        int delete = orderFulmtService.delete(copy);
        return CommonResult.success(delete);
    }

    @ApiOperation("订单核销导入")
    @PostMapping("/import")
    @Log(name = "订单核销信息", operation = OperationEnum.IMPORT)
    public CommonResult<Integer> importExcel(HttpServletRequest request) throws Exception {
        MultipartFile files = ((MultipartHttpServletRequest) request).getFile("file");
        ImportParams params = new ImportParams();
        //获取 导入核销订单数据
        params.setTitleRows(0);
        params.setHeadRows(0);
        params.setSheetNum(1);
        List<ImportFluMtOrderDataDO> list = ExcelImportUtil.importExcel(files.getInputStream(), ImportFluMtOrderDataDO.class, params);
        AdminUser currentEmployees = LoginUtil.getCurrentEmployees();
        return CommonResult.success(orderFulmtStateUpdateService.fulmtImportExcel(list, currentEmployees));
    }


    @ApiOperation("订单核销导入二三针")
    @PostMapping("/importVerificationUpdate")
    @Log(name = "订单核销信息", operation = OperationEnum.IMPORT)
    public CommonResult<Integer> importVerificationUpdate(HttpServletRequest request) throws Exception {
        MultipartFile files = ((MultipartHttpServletRequest) request).getFile("file");
        return CommonResult.success(orderFulmtStateUpdateService.importVerificationUpdate(files));
    }


    @ApiOperation("履约单零售导出")
    @PostMapping("/exportExcel")
    @Log(name = "履约单零售导出", operation = OperationEnum.EXPORT)
    public void exportExcel(@RequestBody OrderFulmtQuery orderFulmtQuery, HttpServletResponse response) {
        OrderFulmtCheck orderFulmtCheck = CopyUtil.copy(orderFulmtQuery, OrderFulmtCheck.class);
        List<OrderFulmtValue> orderFulmtValues = orderFulmtService.listInfo(orderFulmtCheck);
        // 空数据处理
        if (CollectionUtil.isEmpty(orderFulmtValues)) {
            try {
                ExcelUtil.exportExcel(response.getOutputStream(), OrderFulmtExcelVo.class, null, "履约信息");
                return;
            } catch (IOException e) {
                LOG.error("订单导出失败", e);
            }
        }
        //数据处理 填充报表数据
        List<OrderFulmtExcelVo> fulmtExcelVos = CopyUtil.copyList(orderFulmtValues, OrderFulmtExcelVo.class);
        creatFulmtInfo(fulmtExcelVos);
        List<OrderFulmtExcelAllVO> info = CopyUtil.copyList(fulmtExcelVos, OrderFulmtExcelAllVO.class);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=export.xlsx");
        try {
            ExcelUtil.exportExcel(response.getOutputStream(), OrderFulmtExcelAllVO.class, info, "履约信息");
        } catch (IOException e) {
            LOG.error("订单导出失败", e);
        }
    }


    @ApiOperation("订单确认")
    @PostMapping("/orgCheck")
    @Log(name = "履约信息", operation = OperationEnum.CONFIRM)
    public CommonResult<Void> orgCheck(@RequestBody OrgCheckParam orgCheckParam) {

        if (orgCheckParam.getIds().isEmpty() || orgCheckParam.getOrgCheckStatus() == null) {
            throw new BizException(FulMtGoodsBizExEnum.ORG_CHECK_PARAM_FAIL);
        }

        List<Long> ids = orgCheckParam.getIds();
        List<OrderFulmtValue> orderFulmtValueList = orderFulmtService.listByIdList(ids, null);
        if (orderFulmtValueList.size() != ids.size()) {
            throw new BizException(FulMtGoodsBizExEnum.ORG_CHECK_FAIL.getCode(), "履约单数据出现异常");
        }

        AdminUser user = LoginUtil.getCurrentEmployees();
        for (OrderFulmtValue orderFulmtValue : orderFulmtValueList) {
            if (!orderFulmtValue.getState().equals(2) || !orderFulmtValue.getOrgCheckStatus().equals(0)) {
                throw new BizException(FulMtGoodsBizExEnum.ORG_CHECK_FAIL.getCode(), orderFulmtValue.getOrderNum() + "订单状态不对");
            }
        }

        for (OrderFulmtValue orderFulmtValue : orderFulmtValueList) {
            orderFulmtStateUpdateService.updateOrgCheckStatus(orderFulmtValue.getId(), orderFulmtValue.getOrderNum(),
                    orgCheckParam.getOrgCheckStatus(), user.getId());
        }
        return CommonResult.success();
    }

    private Map<Long, AdminUser> getAdminUserMap(List<AdminUser> adminUsers) {
        if (CollectionUtil.isEmpty(adminUsers)) {
            return Maps.newHashMap();
        }
        return adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, Function.identity()));
    }


    private void creatFulmtInfo(List<OrderFulmtExcelVo> fulmtExcelVos) {
        List<Long> longs = fulmtExcelVos.stream().map(OrderFulmtExcelVo::getOperatorId).collect(Collectors.toList());
        //操作人信息
        List<AdminUser> adminUsers = adminUserService.listByUserIds(longs);
        Map<Long, AdminUser> adminUserMap = getAdminUserMap(adminUsers);
        List<Long> goodsIdList = fulmtExcelVos.stream().map(OrderFulmtExcelVo::getFulmtGoodsId).collect(Collectors.toList());
        List<Goods> goodsList = goodsService.listGoodsByIds(goodsIdList);
        Map<Long, Goods> goodsMap = new HashMap<>(16);
        for (Goods goods : goodsList) {
            goodsMap.put(goods.getId(), goods);
        }
        //获取订单信息
        List<String> orderNumList = fulmtExcelVos.stream().map(OrderFulmtExcelVo::getParentOrderNum).collect(Collectors.toList());
        List<Order> orders = orderReadService.listByOutOrderNum(new ArrayList<>(orderNumList), OrderSelectEnum.ACCEPTOR, OrderSelectEnum.GOODS,
                OrderSelectEnum.REFUND_INFO_DETAIL);
        HashMap<String, Order> orderMap = new HashMap<>();
        for (Order order : orders) {
            List<Order> childOrders = order.getChildOrders();
            if (!org.springframework.util.CollectionUtils.isEmpty(childOrders)) {
                for (Order childOrder : childOrders) {
                    orderMap.put(childOrder.getOrderNum(), order);
                }
            }
        }

        for (OrderFulmtExcelVo fulmtExcelVo : fulmtExcelVos) {
            Order order = orderMap.get(fulmtExcelVo.getOrderNum());
            if (order != null) {
                AcceptorSnapshot acceptorSnapshot = order.getAcceptorSnapshot();
                fulmtExcelVo.setOrderNum(order.getParentOrderNum());
                AdminUser adminUser = adminUserMap.get(fulmtExcelVo.getOperatorId());
                if (adminUser != null) {
                    fulmtExcelVo.setOperatorName(adminUser.getUsername());
                }
                //收货人相关信息
                if (acceptorSnapshot != null) {
                    String acceptorName = acceptorSnapshot.getAcceptorName();
                    String acceptorMobile = acceptorSnapshot.getAcceptorMobile();

                    if (acceptorSnapshot.getAcceptorAddress() != null) {
                        String province = acceptorSnapshot.getAcceptorAddress().getProvince();
                        String city = acceptorSnapshot.getAcceptorAddress().getCity();
                        String district = acceptorSnapshot.getAcceptorAddress().getDistrict();
                        String detailsAddress = acceptorSnapshot.getAcceptorAddress().getDetailsAddress();
                        fulmtExcelVo.setConsigneeArea(district);
                        fulmtExcelVo.setConsigneeCity(city);
                        fulmtExcelVo.setConsigneeProvince(province);
                        fulmtExcelVo.setShippingAddress(detailsAddress);
                    }
                    fulmtExcelVo.setConsigneeMobile(acceptorMobile);
                    fulmtExcelVo.setConsigneeName(acceptorName);
                    Goods goods = goodsMap.get(fulmtExcelVo.getFulmtGoodsId());
                    if (goods != null && goods.getName() != null) {
                        String name = goods.getName();
                        fulmtExcelVo.setFulmtGoodsName(name);
                    }

                }
                Date deliveryTime = fulmtExcelVo.getDeliveryTime();
                fulmtExcelVo.setDeliveryTime(deliveryTime);
                String fulmtOtherInfo = fulmtExcelVo.getFulmtOtherInfo();
                FulMtOther fulMtOther = JSONObject.parseObject(fulmtOtherInfo, FulMtOther.class);
                //获取履约 物流信息
                if (fulMtOther != null) {
                    String logisticsCompanies = fulMtOther.getLogisticsCompanies();
                    String logisticsCodes = fulMtOther.getLogisticsCodes();
                    fulmtExcelVo.setLogisticsCompanies(logisticsCompanies);
                    fulmtExcelVo.setLogisticsCodes(logisticsCodes);
                }
                //根据订单编号和 原始商品id 查找商品快照信息
                if (order.getGoodsSnapshot() != null) {
                    Integer purchaseQuantity = order.getGoodsSnapshot().getPurchaseQuantity();
                    if (purchaseQuantity != null) {
                        fulmtExcelVo.setFulmtGoodsCounts(Long.valueOf(purchaseQuantity));
                    }

                }
                FulmtRetailStateEnum byCode = FulmtRetailStateEnum.getByCode(fulmtExcelVo.getState());
                if (byCode != null) {
                    fulmtExcelVo.setStateName(Objects.requireNonNull(FulmtRetailStateEnum.getByCode(fulmtExcelVo.getState())).getName());
                }

                //订单备注
                if (order.getRemark() != null) {
                    fulmtExcelVo.setOrderRemark(order.getRemark().getUserRemark());
                }

                Integer refundState = fulmtExcelVo.getRefundState();
                //退款信息
                if (refundState != null) {
                    fulmtExcelVo.setRefundStateName(Objects.requireNonNull(OrderRefundStateEnum.getByCode(refundState)).getDesc());
                }
                if (fulmtExcelVo.getRefundAmount() != null) {
                    fulmtExcelVo.setRefundAmounts(MoneyUtil.cent2Amount(fulmtExcelVo.getRefundAmount()));
                }
            }
        }
    }


    private void getFulmtOtherInfo(List<OrderFulmtVo> orderFulmtVos) {

        if (CollectionUtil.isEmpty(orderFulmtVos)) {
            return;
        }
        List<String> fulmtNums = orderFulmtVos.stream().map(OrderFulmtVo::getOrderNum).collect(Collectors.toList());
        List<FulmtOrderOperateRecord> info = fulmtOrderOperateRecordService.info(fulmtNums);

        Map<String, List<FulmtOrderOperateRecord>> map = null;

        HashMap<String, Order> orderMap = new HashMap<>();
        if (info != null && info.size() > 0) {
            map = info.stream().collect(Collectors.groupingBy(FulmtOrderOperateRecord::getFulmtOrderNum));
            List<String> list = info.stream().distinct().map(FulmtOrderOperateRecord::getFulmtOrderNum).collect(Collectors.toList());
            List<OrderFulmtValue> fulmtValues = orderFulmtService.listInfoByOrderNum(list);
            List<String> strings = fulmtValues.stream().distinct().map(OrderFulmtValue::getParentOrderNum).collect(Collectors.toList());
            List<Order> orders = orderReadService.listByOutOrderNum(new ArrayList<>(strings));

            for (Order order : orders) {
                List<Order> childOrders = order.getChildOrders();
                if (!org.springframework.util.CollectionUtils.isEmpty(childOrders)) {
                    for (Order childOrder : childOrders) {
                        orderMap.put(childOrder.getOrderNum(), order);
                    }
                }
            }
        }

        for (OrderFulmtVo orderFulmtVo : orderFulmtVos) {
            String fulmtOtherInfo = orderFulmtVo.getFulmtOtherInfo();
            if (!StringUtils.isBlank(fulmtOtherInfo)) {
                FulMtOther fulMtOther = JSONObject.parseObject(fulmtOtherInfo, FulMtOther.class);
                orderFulmtVo.setFulMtOther(fulMtOther);
            }
            if (map != null) {
                List<FulmtOrderOperateRecord> fulmtOrderOperateRecords = map.get(orderFulmtVo.getOrderNum());
                if (!CollectionUtils.isEmpty(fulmtOrderOperateRecords)) {
                    for (FulmtOrderOperateRecord fulmtOrderOperateRecord : fulmtOrderOperateRecords) {
                        Order order = orderMap.get(fulmtOrderOperateRecord.getFulmtOrderNum());
                        if (order != null) {
                            if ("vaccine".equals(order.getBizCode()) && fulmtOrderOperateRecord.getToState() != null) {
                                FulmtStateEnum code = FulmtStateEnum.getByCode(fulmtOrderOperateRecord.getToState());
                                if (code != null) {
                                    fulmtOrderOperateRecord.setToStateName(FulmtStateEnum.getByCode(fulmtOrderOperateRecord.getToState()).getName());
                                }

                                FulmtStateEnum fromCode = FulmtStateEnum.getByCode(fulmtOrderOperateRecord.getFromState());
                                if (fromCode != null) {
                                    fulmtOrderOperateRecord.setFromStateName(FulmtStateEnum.getByCode(fulmtOrderOperateRecord.getFromState()).getName());
                                }
                                //用户操作 取履约手机号 姓名
                                if (fulmtOrderOperateRecord.getType() != null && fulmtOrderOperateRecord.getType() == 1){
                                    fulmtOrderOperateRecord.setNickName(order.getAcceptorMobile());
                                    fulmtOrderOperateRecord.setUsername(order.getAcceptorName());
                                }

                            }

                            if ("retail".equals(order.getBizCode()) && fulmtOrderOperateRecord.getToState() != null) {
                                FulmtRetailStateEnum byCode = FulmtRetailStateEnum.getByCode(fulmtOrderOperateRecord.getToState());
                                if (byCode != null) {
                                    fulmtOrderOperateRecord.setToStateName(FulmtRetailStateEnum.getByCode(fulmtOrderOperateRecord.getToState()).getName());
                                }

                                FulmtRetailStateEnum fromCode = FulmtRetailStateEnum.getByCode(fulmtOrderOperateRecord.getFromState());
                                if (fromCode != null) {
                                    fulmtOrderOperateRecord.setFromStateName(FulmtRetailStateEnum.getByCode(fulmtOrderOperateRecord.getFromState()).getName());
                                }
                            }
                        }

                    }
                }
                orderFulmtVo.setFulmtOrderOperateRecords(fulmtOrderOperateRecords);
            }
        }
    }

    private List<Order> queryOrders(OrderTimeQueryParam orderTimeQueryParam) {
        OrderTimeQuery query = new OrderTimeQuery();
        BeanUtils.copyProperties(orderTimeQueryParam, query);
        query.setBizCodes(Lists.newArrayList(OrderBizCodeEnum.VACCINE.getName()));
        if (query.getBizCodes().contains(OrderBizCodeEnum.VACCINE.getName())) {
            List<Long> list = channelService.listAllChannel().stream().map(Channel::getId).collect(Collectors.toList());
            if (orderTimeQueryParam.getChorgaIds() != null) {
                query.setChorgaIds(CollectionUtils.isEmpty(orderTimeQueryParam.getChorgaIds()) ? list : orderTimeQueryParam.getChorgaIds());
            }
            query.setChorgaType(ChorgaTypeEnum.CHANNEL.getCode());
        }
        if (orderTimeQueryParam.getOrganizationId() != null) {
            query.setOrganizationIds(Lists.newArrayList(orderTimeQueryParam.getOrganizationId()));
        }
        return orderReadService.listByTimeQuery(query,
                OrderSelectEnum.GOODS,
                OrderSelectEnum.EXAM_DATE_SNAPSHOT,
                OrderSelectEnum.ACCEPTOR,
                OrderSelectEnum.REFUND_INFO_DETAIL,
                OrderSelectEnum.OPERATE_LOG);
    }

    @NotNull
    public ExamDateParam getExamDateParam(com.meerkat.fulfillment.fulfillment.model.ExamDateParam dateParam) {
        ExamDateParam examDateParam = new ExamDateParam();
        if (dateParam != null) {
            examDateParam.setPeriod(dateParam.getPeriod());
            examDateParam.setExamDate(dateParam.getExamDate());
            examDateParam.setCapacityId(dateParam.getCapacityId());
        }
        return examDateParam;
    }


    private void createWorkOrder(OrderFulmtParam orderFulmtParam, AdminUser currentEmployees) {
        OrderFulmtValue orderFulmtValue = orderFulmtService.checkOrder(orderFulmtParam.getOrderNum());
        //回调履约单 获取数据
        if (orderFulmtValue == null) {
            throw new BizException(FulMtGoodsBizExEnum.CHECK_FUL_MT_ORDER_FAIL.getCode(), orderFulmtValue.getOrderNum() + "无履约单数据");
        }
        WorkOrderFulmtSysDealParam workOrderFulmtAddParam = new WorkOrderFulmtSysDealParam();
        workOrderFulmtAddParam.setWorkOrderBizType(WorkOrderBizTypeEnum.SUBSCRIBE.getType());
        workOrderFulmtAddParam.setDealStatus(WorkOrderDealStatusEnum.SUBSCRIBE_DEAL.getType());
        workOrderFulmtAddParam.setAdminUserId(currentEmployees.getId());
        workOrderFulmtAddParam.setBizNo(orderFulmtValue.getFulmtNum());
        workOrderFulmtAddParam.setBizCode(orderFulmtValue.getBizCode());
        workOrderService.sysDeal(workOrderFulmtAddParam);
    }

    private void createLog(OrderFulmtParam orderFulmtParam) {
        OrderFulmtValue orderFulmtValue = orderFulmtService.checkOrder(orderFulmtParam.getOrderNum());
        if ("retail".equals(orderFulmtValue.getBizCode())) {
            FulmtOrderOperateRecord fulmtOrderOperate = new FulmtOrderOperateRecord();
            //添加日志
            fulmtOrderOperate.setFulmtOrderNum(orderFulmtParam.getOrderNum());
            fulmtOrderOperate.setFromState(0);
            fulmtOrderOperate.setEventName("零售订单选择供应商");
            fulmtOrderOperate.setToState(1);
            fulmtOrderOperate.setOperatorId(Math.toIntExact(LoginUtil.getCurrentEmployees().getId()));
            fulmtOrderOperateRecordService.insert(fulmtOrderOperate);
        } else {
            //获取排期信息
            ExamDateParam examDateParam = getExamDateParam(orderFulmtParam.getExamDateParam());
            //选择排期时间是当前时间之前的 履约变为待核销
            Date examDate = examDateParam.getExamDate();
            long time = examDate.getTime();
            Date date = new Date();
            DateTime dateTime = DateUtil.beginOfDay(DateUtil.offsetDay(date, 0));
            DateTime endTime = DateUtil.endOfDay(DateUtil.offsetDay(date, +2));
            long nowTime = dateTime.getTime();
            //添加日志
            FulmtOrderOperateRecord fulmtOrderOperateRecord = new FulmtOrderOperateRecord();
            WorkOrderFulmtAddParam workOrderFulmtAddParam = new WorkOrderFulmtAddParam();
            if (time < nowTime) {
                fulmtOrderOperateRecord.setToState(4);
                fulmtOrderOperateRecord.setEventName("疫苗代预约，推进到待核销");

                workOrderFulmtAddParam.setWorkOrderBizType(WorkOrderBizTypeEnum.WRITEOFF.getType());
                //通知工单
                workOrderFulmtAddParam.setBizNo(orderFulmtValue.getFulmtNum());
                workOrderFulmtAddParam.setBizCode(orderFulmtValue.getBizCode());
                workOrderFulmtAddParam.setChorgaId(orderFulmtValue.getChorgaId());
                workOrderService.save(workOrderFulmtAddParam);

            }
            if (time >= nowTime && time <= endTime.getTime()) {
                fulmtOrderOperateRecord.setToState(3);
                fulmtOrderOperateRecord.setEventName("疫苗代预约，推进到待机构确认");

                workOrderFulmtAddParam.setWorkOrderBizType(WorkOrderBizTypeEnum.CONFIRM.getType());
                //通知工单
                workOrderFulmtAddParam.setBizNo(orderFulmtValue.getFulmtNum());
                workOrderFulmtAddParam.setBizCode(orderFulmtValue.getBizCode());
                workOrderFulmtAddParam.setChorgaId(orderFulmtValue.getChorgaId());
                workOrderService.save(workOrderFulmtAddParam);

            }
            if (endTime.getTime() < time) {
                fulmtOrderOperateRecord.setToState(1);
                fulmtOrderOperateRecord.setEventName("疫苗代预约，推进到已预约");

            }


            //记录操作日志
            fulmtOrderOperateRecord.setFulmtOrderNum(orderFulmtParam.getOrderNum());
            fulmtOrderOperateRecord.setFromState(0);
            fulmtOrderOperateRecord.setOperatorId(Math.toIntExact(LoginUtil.getCurrentEmployees().getId()));
            fulmtOrderOperateRecordService.insert(fulmtOrderOperateRecord);
        }
    }
}
