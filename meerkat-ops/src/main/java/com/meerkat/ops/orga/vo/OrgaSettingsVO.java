package com.meerkat.ops.orga.vo;

import com.meerkat.base.enums.OrgaBizTypeEnum;
import com.meerkat.smart.organization.model.OrgaExamSettings;
import com.meerkat.smart.organization.model.OrgaVaccineSettings;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @description:
 * @author: pantaoling
 * @date: 2022/4/21
 */
public class OrgaSettingsVO {
    @ApiModelProperty("机构id")
    private Long orgaId;

    /**
     * 业务类型
     */
    @ApiModelProperty("机构业务类型")
    private List<OrgaBizTypeEnum> orgaBizTypeEnums;


    /**
     * 机构体检业务配置
     */
    @ApiModelProperty("机构体检业务配置")
    private OrgaExamSettings orgaExamSettings;

    /**
     * 机构疫苗业务配置
     */
    @ApiModelProperty("机构疫苗业务配置")
    private OrgaVaccineSettings orgaVaccineSettings;


    public Long getOrgaId() {
        return orgaId;
    }

    public void setOrgaId(Long orgaId) {
        this.orgaId = orgaId;
    }

    public List<OrgaBizTypeEnum> getOrgaBizTypeEnums() {
        return orgaBizTypeEnums;
    }

    public void setOrgaBizTypeEnums(List<OrgaBizTypeEnum> orgaBizTypeEnums) {
        this.orgaBizTypeEnums = orgaBizTypeEnums;
    }

    public OrgaExamSettings getOragaExamSettings() {
        return orgaExamSettings;
    }

    public void setOragaExamSettings(OrgaExamSettings orgaExamSettings) {
        this.orgaExamSettings = orgaExamSettings;
    }

    public OrgaVaccineSettings getOragaVaccineSettings() {
        return orgaVaccineSettings;
    }

    public void setOragaVaccineSettings(OrgaVaccineSettings orgaVaccineSettings) {
        this.orgaVaccineSettings = orgaVaccineSettings;
    }
}
