package com.meerkat.ops.order.service;

import com.meerkat.ops.goods.vo.GoodsImportVO;
import com.meerkat.ops.order.param.vo.OpsOrderVO;
import com.meerkat.ops.order.param.vo.ThirdPartyUpdateVO;
import com.meerkat.order.param.OrderUpdateParam;

import java.io.InputStream;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */

public interface OrderService {

    /**
     * order 数据校验
     *
     * @param OrderDOS OrderDOS
     * @return java.util.List<com.meerkat.ops.order.param.vo.OpsOrderVO>
     * <AUTHOR>
     * @date 2022/4/12 16:44
     */
    List<OpsOrderVO> opsOrderCheck(List<OpsOrderVO> OrderDOS);


    void importExcel(InputStream inputStream, String fileName, String source, Long id) throws Exception;


    void   importRetailExcel(InputStream inputStream, String fileName, String source, Long id) throws Exception;

    void thirdPartyUpdateImport(List<ThirdPartyUpdateVO> goodsImportVOList, Long operateId);

}
