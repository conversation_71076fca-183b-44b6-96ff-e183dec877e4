package com.meerkat.ops.order.param.vo;

import com.meerkat.order.dto.OrderExt;
import com.meerkat.order.enums.OrderRefundReasonEnum;
import com.meerkat.order.model.Order;
import com.meerkat.order.model.OrderOperateRecord;
import com.meerkat.order.model.snapshot.*;
import com.meerkat.trade.refund.dto.RefundApply;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: pantaoling
 * @date: 2021/10/11
 */
@ApiModel("orderVO")
public class OrderVO {


    /**
     * 客户id
     */
    @ApiModelProperty("客户id")
    private Long customerId;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNum;

    /**
     * 订单总金额
     */
    @ApiModelProperty("总金额")
    private Long totalAmount;

    /**
     * 应付金额（实际支付金额）
     */
    @ApiModelProperty("支付金额")
    private Long payAmount;

    /**
     * 订单来源：0 微信小程序
     */
    @ApiModelProperty("订单来源 0 微信小程序 1 crm 2 公众号")
    private Integer sourceType;

    /**
     * 订单状态
     */
    @ApiModelProperty("订单状态")
    private Integer state;

    /**
     * 订单类型：0 服务订单
     */
    @ApiModelProperty("订单类型 0 服务订单 1 实物订单 2 充值订单")
    private Integer orderType;


    /**
     * 订单备注
     */
    @ApiModelProperty("订单备注")
    private String remark;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    private Date paymentTime;

    /**
     * 售卖方
     */
    @ApiModelProperty("机构id")
    private Long organizationId;

    /**
     * 售卖方名称
     */
    @ApiModelProperty("机构名称")
    private String organizationName;

    /**
     * 订单来源渠道
     */
    @ApiModelProperty("订单渠道")
    private Integer channelId;

    @ApiModelProperty("销售渠道")
    private String chorgaName;

    /**
     * 履约人姓名
     */
    @ApiModelProperty("履约人姓名")
    private String acceptorName;

    /**
     * 履约人身份证
     */
    @ApiModelProperty("履约人身份证")
    private String acceptorIdcard;

    /**
     * 履约人手机号
     */
    @ApiModelProperty("履约人手机号")
    private String acceptorMobile;

    @ApiModelProperty("订单分销渠道")
    private String distributionChannel;

    @ApiModelProperty("订单业务类型 exam=体检订单 vaccine=疫苗订单")
    private String bizCode;

    @ApiModelProperty("就诊时间")
    private Date examDate;

    /**
     * 提交时间
     */
    @ApiModelProperty("创建时间")
    private Date gmtCreated;

    /**
     * 更新时间
     */
    @ApiModelProperty("订单更新时间")
    private Date gmtModified;


    /**
     * 商品快照
     */
    @ApiModelProperty("商品快照")
    @Deprecated
    private List<GoodsSnapshot> goodsSnapshot;

    /**
     * 子订单号
     */
    @ApiModelProperty("商品快照")
    private List<Order> childOrders;

    /**
     * 外部订单号 可为空
     */
    private String outOrderNum;


    /**
     * 履约人快照
     */
    @ApiModelProperty("履约人快照")
    private AcceptorSnapshot acceptorSnapshot;

    /**
     * 渠道快照
     */
    @ApiModelProperty("渠道快照")
    private ChannelSnapshot channelSnapshot;

    /**
     * 机构快照
     */
    @ApiModelProperty("机构快照")
    private OrganizationSnapshot organizationSnapshot;

    /**
     * 机构单位快照
     */
    @ApiModelProperty("机构单位快照")
    private OrganizationCompanySnapshot organizationCompanySnapshot;

    /**
     * 渠道单位快照
     */
    @ApiModelProperty("渠道单位快照")
    private ChannelCompanySnapshot channelCompanySnapshot;

    /**
     * 交易单号
     */
    @ApiModelProperty("交易单号")
    private String tradOrderNum;

    @ApiModelProperty("服务时间快照")
    private ExamDateSnapshot examDateSnapshot;

    @ApiModelProperty("支付明细")
    private String payDetail;

    @ApiModelProperty("退款快照")
    private RefundInfoDetailSnapshot refundInfoDetailSnapshot;

    @ApiModelProperty("退款申请明细")
    private List<RefundApply> refundApplies;

    @ApiModelProperty("订单操作记录")
    private List<OrderOperateRecord> operateRecordList;

    @ApiModelProperty("退款时间")
    private Date refundTime;

    @ApiModelProperty("备注")
    private OrderRemarkVo orderRemarkVo;

    @ApiModelProperty("下单人手机号")
    private OperatorSnapshot operatorSnapshot;

    @ApiModelProperty("订单扩展字段")
    private OrderExt orderExt;

    @ApiModelProperty("结算状态 0=未结算 1=已结算")
    private Integer settleState;
    /**
     * 退款状态
     */
    @ApiModelProperty("订单退款状态 1=退款中 2=已退款 3=拒绝退款 4=部分退款")
    private Integer refundState;

    @ApiModelProperty("订单退款审核状态 0=拒绝，1，通过")
    private Integer refundAuditState;

    @ApiModelProperty("城市ID")
    private Long addressId;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("退款原因枚举")
    private OrderRefundReasonEnum refundReasonEnum;

    @ApiModelProperty("是否为预售单；0：否；1是")
    private Boolean isPresale;

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getDistributionChannel() {
        return distributionChannel;
    }

    public void setDistributionChannel(String distributionChannel) {
        this.distributionChannel = distributionChannel;
    }

    public List<OrderOperateRecord> getOperateRecordList() {
        return operateRecordList;
    }

    public void setOperateRecordList(List<OrderOperateRecord> operateRecordList) {
        this.operateRecordList = operateRecordList;
    }

    public List<RefundApply> getRefundApplies() {
        return refundApplies;
    }

    public void setRefundApplies(List<RefundApply> refundApplies) {
        this.refundApplies = refundApplies;
    }

    public String getPayDetail() {
        return payDetail;
    }

    public void setPayDetail(String payDetail) {
        this.payDetail = payDetail;
    }

    public ExamDateSnapshot getExamDateSnapshot() {
        return examDateSnapshot;
    }

    public void setExamDateSnapshot(ExamDateSnapshot examDateSnapshot) {
        this.examDateSnapshot = examDateSnapshot;
    }


    public String getTradOrderNum() {
        return tradOrderNum;
    }

    public void setTradOrderNum(String tradOrderNum) {
        this.tradOrderNum = tradOrderNum;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public Long getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Long totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Long getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(Long payAmount) {
        this.payAmount = payAmount;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(Date paymentTime) {
        this.paymentTime = paymentTime;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public String getAcceptorName() {
        return acceptorName;
    }

    public void setAcceptorName(String acceptorName) {
        this.acceptorName = acceptorName;
    }

    public String getAcceptorIdcard() {
        return acceptorIdcard;
    }

    public void setAcceptorIdcard(String acceptorIdcard) {
        this.acceptorIdcard = acceptorIdcard;
    }

    public String getAcceptorMobile() {
        return acceptorMobile;
    }

    public void setAcceptorMobile(String acceptorMobile) {
        this.acceptorMobile = acceptorMobile;
    }

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public List<GoodsSnapshot> getGoodsSnapshot() {
        return goodsSnapshot;
    }

    public void setGoodsSnapshot(List<GoodsSnapshot> goodsSnapshot) {
        this.goodsSnapshot = goodsSnapshot;
    }

    public AcceptorSnapshot getAcceptorSnapshot() {
        return acceptorSnapshot;
    }

    public void setAcceptorSnapshot(AcceptorSnapshot acceptorSnapshot) {
        this.acceptorSnapshot = acceptorSnapshot;
    }

    public ChannelSnapshot getChannelSnapshot() {
        return channelSnapshot;
    }

    public void setChannelSnapshot(ChannelSnapshot channelSnapshot) {
        this.channelSnapshot = channelSnapshot;
    }

    public OrganizationSnapshot getOrganizationSnapshot() {
        return organizationSnapshot;
    }

    public void setOrganizationSnapshot(OrganizationSnapshot organizationSnapshot) {
        this.organizationSnapshot = organizationSnapshot;
    }

    public OrganizationCompanySnapshot getOrganizationCompanySnapshot() {
        return organizationCompanySnapshot;
    }

    public void setOrganizationCompanySnapshot(OrganizationCompanySnapshot organizationCompanySnapshot) {
        this.organizationCompanySnapshot = organizationCompanySnapshot;
    }

    public ChannelCompanySnapshot getChannelCompanySnapshot() {
        return channelCompanySnapshot;
    }

    public void setChannelCompanySnapshot(ChannelCompanySnapshot channelCompanySnapshot) {
        this.channelCompanySnapshot = channelCompanySnapshot;
    }

    public RefundInfoDetailSnapshot getRefundInfoDetailSnapshot() {
        return refundInfoDetailSnapshot;
    }

    public void setRefundInfoDetailSnapshot(RefundInfoDetailSnapshot refundInfoDetailSnapshot) {
        this.refundInfoDetailSnapshot = refundInfoDetailSnapshot;
    }

    public String getOutOrderNum() {
        return outOrderNum;
    }

    public void setOutOrderNum(String outOrderNum) {
        this.outOrderNum = outOrderNum;
    }

    public Date getExamDate() {
        return examDate;
    }

    public void setExamDate(Date examDate) {
        this.examDate = examDate;
    }

    public Date getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(Date refundTime) {
        this.refundTime = refundTime;
    }

    public String getChorgaName() {
        return chorgaName;
    }

    public void setChorgaName(String chorgaName) {
        this.chorgaName = chorgaName;
    }

    public OrderRemarkVo getOrderRemarkVo() {
        return orderRemarkVo;
    }

    public void setOrderRemarkVo(OrderRemarkVo orderRemarkVo) {
        this.orderRemarkVo = orderRemarkVo;
    }

    public OperatorSnapshot getOperatorSnapshot() {
        return operatorSnapshot;
    }

    public void setOperatorSnapshot(OperatorSnapshot operatorSnapshot) {
        this.operatorSnapshot = operatorSnapshot;
    }

    public OrderExt getOrderExt() {
        return orderExt;
    }

    public void setOrderExt(OrderExt orderExt) {
        this.orderExt = orderExt;
    }

    public Integer getSettleState() {
        return settleState;
    }

    public void setSettleState(Integer settleState) {
        this.settleState = settleState;
    }

    public List<Order> getChildOrders() {
        return childOrders;
    }

    public void setChildOrders(List<Order> childOrders) {
        this.childOrders = childOrders;
    }

    public Integer getRefundState() {
        return refundState;
    }

    public void setRefundState(Integer refundState) {
        this.refundState = refundState;
    }

    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Integer getRefundAuditState() {
        return refundAuditState;
    }

    public void setRefundAuditState(Integer refundAuditState) {
        this.refundAuditState = refundAuditState;
    }

    public OrderRefundReasonEnum getRefundReasonEnum() {
        return refundReasonEnum;
    }

    public void setRefundReasonEnum(OrderRefundReasonEnum refundReasonEnum) {
        this.refundReasonEnum = refundReasonEnum;
    }

    public Boolean getPresale() {
        return isPresale;
    }

    public void setPresale(Boolean presale) {
        isPresale = presale;
    }
}
