package com.meerkat.ops.order.param.vo;

import com.meerkat.common.db.Page;
import com.meerkat.order.enums.OrderRefundReasonEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/11/1 23:14
 */
@ApiModel
public class OrderVaccineSiteOrgaQueryVO {

    @ApiModelProperty("订单创建时间开始")
    private Date createStartTime;

    @ApiModelProperty("订单创建时间结束")
    private Date createEndTime;

    @ApiModelProperty("订单体检时间开始")
    private Date serviceStartTime;

    @ApiModelProperty("订单体检时间结束")
    private Date serviceEndTime;

    @ApiModelProperty("订单状态")
    private List<Integer> states;

    @ApiModelProperty("订单编号")
    private String orderNum;

    @ApiModelProperty("商品标签")
    private List<Long> goodsTags;

    @ApiModelProperty("分页")
    private Page page;

    @ApiModelProperty("分销渠道")
    private String distributionChannel;

    @ApiModelProperty("问诊人姓名")
    private String acceptorName;

    @ApiModelProperty("问诊人手机号")
    private String acceptorMobile;

    @ApiModelProperty("退款时间开始")
    private Date refundTimeStart;

    @ApiModelProperty("退款时间结束")
    private Date refundTimeEnd;

    @ApiModelProperty("订单业务类型 exam=体检订单 vaccine=疫苗订单 health_manage=健康管理订单 retail=零售订单")
    private List<String> bizCodes;

    @ApiModelProperty("结算状态  0=未结算 1=已结算")
    private List<Integer> settleState;

    @ApiModelProperty("二方订单核销状态 0=未核销 1=已核销")
    private Integer secondOrderCheck;

    @ApiModelProperty("订单标签字段筛选")
    private List<String> orderTags;

    /**
     * 订单退款字段筛选
     */
    @ApiModelProperty("订单退款状态筛选 1=退款中 2=已退款 3=拒绝退款 4=部分退款")
    private List<Integer> refundStates;

    @ApiModelProperty("城市ID")
    private Long addressId;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("交易流水号")
    private String tradeSn;

    @ApiModelProperty("支付开始时间")
    private Date paymentTimeStart;

    @ApiModelProperty("支付结束时间")
    private Date paymentTimeEnd;

    @ApiModelProperty("退款原因")
    private OrderRefundReasonEnum refundReasonEnum;

    public Date getServiceStartTime() {
        return serviceStartTime;
    }

    public void setServiceStartTime(Date serviceStartTime) {
        this.serviceStartTime = serviceStartTime;
    }

    public Date getServiceEndTime() {
        return serviceEndTime;
    }

    public void setServiceEndTime(Date serviceEndTime) {
        this.serviceEndTime = serviceEndTime;
    }

    public Integer getSecondOrderCheck() {
        return secondOrderCheck;
    }

    public void setSecondOrderCheck(Integer secondOrderCheck) {
        this.secondOrderCheck = secondOrderCheck;
    }

    public List<String> getBizCodes() {
        return bizCodes;
    }

    public void setBizCodes(List<String> bizCodes) {
        this.bizCodes = bizCodes;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public String getDistributionChannel() {
        return distributionChannel;
    }

    public void setDistributionChannel(String distributionChannel) {
        this.distributionChannel = distributionChannel;
    }

    public Date getCreateStartTime() {
        return createStartTime;
    }

    public void setCreateStartTime(Date createStartTime) {
        this.createStartTime = createStartTime;
    }

    public Date getCreateEndTime() {
        return createEndTime;
    }

    public void setCreateEndTime(Date createEndTime) {
        this.createEndTime = createEndTime;
    }

    public List<Integer> getStates() {
        return states;
    }

    public void setStates(List<Integer> states) {
        this.states = states;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getAcceptorName() {
        return acceptorName;
    }

    public void setAcceptorName(String acceptorName) {
        this.acceptorName = acceptorName;
    }

    public String getAcceptorMobile() {
        return acceptorMobile;
    }

    public void setAcceptorMobile(String acceptorMobile) {
        this.acceptorMobile = acceptorMobile;
    }

    public Date getRefundTimeStart() {
        return refundTimeStart;
    }

    public void setRefundTimeStart(Date refundTimeStart) {
        this.refundTimeStart = refundTimeStart;
    }

    public Date getRefundTimeEnd() {
        return refundTimeEnd;
    }

    public void setRefundTimeEnd(Date refundTimeEnd) {
        this.refundTimeEnd = refundTimeEnd;
    }

    public List<Integer> getSettleState() {
        return settleState;
    }

    public void setSettleState(List<Integer> settleState) {
        this.settleState = settleState;
    }

    public List<String> getOrderTags() {
        return orderTags;
    }

    public void setOrderTags(List<String> orderTags) {
        this.orderTags = orderTags;
    }

    public List<Integer> getRefundStates() {
        return refundStates;
    }

    public void setRefundStates(List<Integer> refundStates) {
        this.refundStates = refundStates;
    }

    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getTradeSn() {
        return tradeSn;
    }

    public void setTradeSn(String tradeSn) {
        this.tradeSn = tradeSn;
    }

    public List<Long> getGoodsTags() {
        return goodsTags;
    }

    public void setGoodsTags(List<Long> goodsTags) {
        this.goodsTags = goodsTags;
    }

    public OrderRefundReasonEnum getRefundReasonEnum() {
        return refundReasonEnum;
    }

    public void setRefundReasonEnum(OrderRefundReasonEnum refundReasonEnum) {
        this.refundReasonEnum = refundReasonEnum;
    }

    public Date getPaymentTimeStart() {
        return paymentTimeStart;
    }

    public void setPaymentTimeStart(Date paymentTimeStart) {
        this.paymentTimeStart = paymentTimeStart;
    }

    public Date getPaymentTimeEnd() {
        return paymentTimeEnd;
    }

    public void setPaymentTimeEnd(Date paymentTimeEnd) {
        this.paymentTimeEnd = paymentTimeEnd;
    }
}
