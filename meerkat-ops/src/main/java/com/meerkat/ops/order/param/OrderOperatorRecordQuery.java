package com.meerkat.ops.order.param;

import com.meerkat.common.db.Page;

import java.util.List;

/**
 * <p>
 * 页面查询参数
 * </p>
 *
 * <AUTHOR>
 * @date 2021/12/6 10:05
 */
public class OrderOperatorRecordQuery {
    /**
     * 对应落单进度
     */
    private List<Integer> states;
    /**
     * 对应订单编号
     */
    private String orderNum;
    /**
     * 对应分页参数
     */
    private Page page;

    /**
     * 分销渠道
     */
    private String distributionChannel;

    public String getDistributionChannel() {
        return distributionChannel;
    }

    public void setDistributionChannel(String distributionChannel) {
        this.distributionChannel = distributionChannel;
    }

    public List<Integer> getStates() {
        return states;
    }

    public void setStates(List<Integer> states) {
        this.states = states;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }
}
