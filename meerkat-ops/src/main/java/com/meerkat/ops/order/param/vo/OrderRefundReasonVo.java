package com.meerkat.ops.order.param.vo;

import com.meerkat.order.enums.OrderRefundReasonEnum;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

public class OrderRefundReasonVo {

    @ApiModelProperty(value = "子订单编号")
    @NotNull(message = "订单号不能为空")
    private String orderNum;

    /**
     * 退款原因
     */
    @ApiModelProperty("退款原因")
    @NotNull(message = "退款原因不能为空")
    @Deprecated
    private String refundReason;

    @ApiModelProperty("退款原因枚举")
    @NotNull(message = "退款原因不能为空")
    private OrderRefundReasonEnum refundReasonEnum;

    /**
     * 退款备注
     */
    @ApiModelProperty("退款备注")
    private String refundRemark;

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getRefundReason() {
        return refundReason;
    }

    public void setRefundReason(String refundReason) {
        this.refundReason = refundReason;
    }

    public String getRefundRemark() {
        return refundRemark;
    }

    public void setRefundRemark(String refundRemark) {
        this.refundRemark = refundRemark;
    }

    public OrderRefundReasonEnum getRefundReasonEnum() {
        return refundReasonEnum;
    }

    public void setRefundReasonEnum(OrderRefundReasonEnum refundReasonEnum) {
        this.refundReasonEnum = refundReasonEnum;
    }
}
