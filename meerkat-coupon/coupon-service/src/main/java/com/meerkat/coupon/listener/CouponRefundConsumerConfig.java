package com.meerkat.coupon.listener;

import cn.hutool.core.net.NetUtil;
import com.meerkat.notify.consumer.RabbitConsumerDailyConfig;
import com.meerkat.notify.listener.AbstractMessageListener;
import com.meerkat.order.enums.MQConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OrderConsumerConfig
 * @description
 * @createTime 2022/09/19 10:08
 */
@Configuration
public class CouponRefundConsumerConfig extends RabbitConsumerDailyConfig {
    @Autowired
    private CouponRefundMessageListener couponFinishMessageListener;

    @Override
    public String queueName() {
        return CouponMqEnum.MIDDLE_PLATFORM_ORDER_COUPON_REFUND_QUEUE.getCode();
    }

    @Override
    public AbstractMessageListener messageListener() {
        return couponFinishMessageListener;
    }

    @Override
    public String topicExchangeName() {
        return MQConstants.EXCHANGE;
    }

    @Override
    public String producerIp() {
        return NetUtil.getLocalhost().getHostAddress();
    }

    @Override
    public List<String> routingKeys() {
        // 监听订单关闭和订单取消
        return List.of(MQConstants.RoutingKey.MIDDLE_PLATFORM_ORDER_CLOSE,
                MQConstants.RoutingKey.MIDDLE_PLATFORM_ORDER_CANCEL);
    }

}
