package com.meerkat.coupon.service.rule;

import com.meerkat.common.utils.DateUtils;
import com.meerkat.coupon.model.Coupon;
import com.meerkat.coupon.model.CouponResult;
import com.meerkat.coupon.model.CouponRuleContext;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @program meerkat-origin
 * @description: 优惠券过期时间
 * @author: fandongdong
 * @create: 2022/10/06 22:41
 */
@Service("couponExpireTimeRuleService")
public class CouponExpireTimeRuleService implements CouponRuleService {
    /**
     * 校验规则
     *
     * @param couponRuleContext
     * @return
     */
    @Override
    public void validate(CouponRuleContext couponRuleContext) {
        List<CouponResult> couponResultLists = couponRuleContext.getAbleListResults();
        if (CollectionUtils.isEmpty(couponResultLists)) {
            return;
        }
        List<CouponResult> availableCouponList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(couponResultLists)) {
            for (CouponResult couponResult : couponResultLists) {
                Coupon coupon = couponResult.getCoupon();
                Date date = new Date();
                long nowTime = date.getTime();
                long validStartTime = coupon.getValidStartTime().getTime();
                long validEndTime = coupon.getValidEndTime().getTime();
                if (nowTime >= validStartTime && nowTime <= validEndTime) {
                    availableCouponList.add(couponResult);
                } else {
                    couponRuleContext.addErrorInfo(coupon.getId(),
                            "优惠券使用时间异常，异常时间为："+ DateUtils.dateYmdFormatString(date));
                }
            }
        }
        couponRuleContext.setAbleListResults(availableCouponList);
    }
}
