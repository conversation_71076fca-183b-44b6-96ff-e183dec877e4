package com.meerkat.coupon.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.meerkat.common.db.Page;
import com.meerkat.common.db.PageView;
import com.meerkat.common.db.TransactionHelper;
import com.meerkat.common.exception.BizException;
import com.meerkat.common.utils.AssertUtil;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.common.utils.DateUtils;
import com.meerkat.coupon.dao.object.CouponDO;
import com.meerkat.coupon.dao.object.CouponTemplateDO;
import com.meerkat.coupon.dto.CouponDTO;
import com.meerkat.coupon.dto.CouponTemplateDTO;
import com.meerkat.coupon.enums.CouponStateEnum;
import com.meerkat.coupon.enums.CouponTemplateStatusEnum;
import com.meerkat.coupon.enums.ExpireTypeEnum;
import com.meerkat.coupon.enums.ReceivedTypeEnum;
import com.meerkat.coupon.exception.CouponBizExEnum;
import com.meerkat.coupon.mapper.CouponMapper;
import com.meerkat.coupon.mapper.CouponTemplateMapper;
import com.meerkat.coupon.model.Coupon;
import com.meerkat.coupon.model.CouponRule;
import com.meerkat.coupon.param.*;
import com.meerkat.coupon.service.CouponService;
import com.meerkat.coupon.service.CouponTemplateService;
import com.meerkat.order.service.OrderReadService;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program meerkat-origin
 * @description:
 * @author: fandongdong
 * @create: 2022/10/08 20:11
 */
@Service
public class CouponServiceImpl implements CouponService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CouponServiceImpl.class);

    @Autowired
    private CouponMapper couponMapper;

    @Autowired
    private CouponTemplateService couponTemplateService;

    @Autowired
    private CouponTemplateMapper couponTemplateMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private OrderReadService orderReadService;

    /**
     * 多条件查询优惠券信息
     *
     * @param couponQuery
     * @return
     */
    @Override
    public List<Coupon> listCouponByQuery(CouponQuery couponQuery) {
        CouponDOQuery doQuery = new CouponDOQuery();
        BeanUtils.copyProperties(couponQuery, doQuery);
        List<CouponDO> couponDOS = couponMapper.selectByQuery(doQuery);
        return CopyUtil.copyList(couponDOS, Coupon.class);
    }

    /**
     * 分页查询优惠券信息
     *
     * @param couponQuery
     * @return
     */
    @Override
    public PageView<Coupon> pageCouponByQuery(CouponQuery couponQuery) {
        CouponDOQuery doQuery = new CouponDOQuery();
        BeanUtils.copyProperties(couponQuery, doQuery);
        Page page = couponQuery.getPage();
        if (page == null) {
            page = new Page(1, 20);
        }
        PageHelper.startPage(page.getCurrentPage(), page.getPageSize());
        List<CouponDO> couponDOS = couponMapper.selectByQuery(doQuery);
        PageInfo pageInfo = new PageInfo(couponDOS);
        page.setRowCount(pageInfo.getTotal());
        List<Coupon> couponList = CopyUtil.copyList(couponDOS, Coupon.class);
        return new PageView<>(couponList, page);
    }

    /**
     * 新增优惠券接口
     *
     * @param coupon
     */
    @Override
    public void addCoupon(Coupon coupon) {
        AssertUtil.notNull(coupon, "新增优惠券对象信息不能为空");
        CouponDO couponDO = new CouponDO();
        BeanUtils.copyProperties(coupon, couponDO);
        couponMapper.insert(couponDO);
    }

    /**
     * 更新优惠券接口
     *
     * @param coupon
     */
    @Override
    public void updateCoupon(Coupon coupon) {
        AssertUtil.notNull(coupon, "更新优惠券对象信息不能为空");
        CouponDO couponDO = couponMapper.selectByPrimaryKey(coupon.getId());
        AssertUtil.notNull(couponDO, "更新优惠券不存在");
        BeanUtils.copyProperties(coupon, couponDO);
        couponMapper.update(couponDO);
    }

    /**
     * 领取优惠券接口
     *
     * @param templateIds
     * @param userId
     */
    @Override
    public void receiveCoupon(List<Long> templateIds, Long userId, String mobile) {
        AssertUtil.notNull(userId, "领取优惠券用户id不能为空");
        AssertUtil.notEmpty(templateIds, "领取优惠券，优惠券ids不能为空");
        CouponTemplateQuery couponTemplateQuery = new CouponTemplateQuery();
        couponTemplateQuery.setCouponTemplateIds(templateIds);
        CouponTemplateSelector selector = new CouponTemplateSelector();
        selector.setShowRule(true);
        List<CouponTemplateDTO> list = couponTemplateService.listCouponTemplate(couponTemplateQuery, selector);
        if (CollectionUtils.isEmpty(list)) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "需要领取的优惠券信息不存在");
        }
        CouponQuery query = new CouponQuery();
        query.setUserId(userId);
        query.setTemplateIds(templateIds);
        List<Coupon> couponList = listCouponByQuery(query);
        //获取用户领取优惠券信息
        Map<Long, List<Coupon>> couponTemplateMap = couponList.stream().collect(Collectors.groupingBy(Coupon::getTemplateId));
        String templateStr = templateIds.stream().map(String::valueOf).collect(Collectors.joining("_"));
        RLock rLock = null;
        try {
            rLock = tryLockCoupon(userId, templateStr);
            // 校验优惠券可领取数量
            // 校验用户领取优惠券规则
            List<CouponTemplateDTO> addCouponList = Lists.newArrayList();
            if (list.size() == 1) {
                CouponTemplateDTO couponTemplateDTO = list.get(0);
                CouponRule couponRule = couponTemplateDTO.getCouponRule();
                List<Coupon> receiveCouponList = couponTemplateMap.get(couponTemplateDTO.getId());
                if (couponTemplateDTO.getTotalCount() == 0 || !Objects.equals(couponTemplateDTO.getStatus(),
                        CouponTemplateStatusEnum.ON.getCode())) {
                    throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "优惠券已领完");
                }
                if (CollectionUtils.isNotEmpty(receiveCouponList)) {
                    Integer userLimitNum = couponRule.getUserLimitNum();
                    if (userLimitNum == null || receiveCouponList.size() < userLimitNum) {
                        addCouponList.add(couponTemplateDTO);
                    } else {
                        throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "领取数量已达上限");
                    }
                } else {
                    Integer userLimitNum = couponRule.getUserLimitNum();
                    if (userLimitNum != null && userLimitNum == 0) {
                        throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "优惠券已领完");
                    }
                    addCouponList.add(couponTemplateDTO);
                }
            } else {

                for (CouponTemplateDTO couponTemplateDTO : list) {
                    CouponRule couponRule = couponTemplateDTO.getCouponRule();
                    Integer userLimitNum = couponRule.getUserLimitNum();
                    List<Coupon> receiveCouponList = couponTemplateMap.get(couponTemplateDTO.getId());
                    //优惠券可领取总数大于0
                    if (couponTemplateDTO.getTotalCount() > 0
                            //优惠券在上架状态
                            && Objects.equals(couponTemplateDTO.getStatus(), CouponTemplateStatusEnum.ON.getCode())) {
                        //校验个人领取数量
                        //单个用户不限数量
                        if (userLimitNum == null) {
                            addCouponList.add(couponTemplateDTO);
                        } else {
                            if (CollectionUtils.isNotEmpty(receiveCouponList)) {
                                if (receiveCouponList.size() < userLimitNum) {
                                    addCouponList.add(couponTemplateDTO);
                                }
                            } else {
                                if (userLimitNum > 0) {
                                    addCouponList.add(couponTemplateDTO);
                                }
                            }
                        }
                    }
                }
                if (CollectionUtils.isEmpty(addCouponList)) {
                    throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "已全部领取优惠券");
                }
            }

            List<CouponDO> couponDOList = Lists.newArrayList();
            for (CouponTemplateDTO couponTemplateDTO : addCouponList) {
                CouponRule couponRule = couponTemplateDTO.getCouponRule();
                Integer expireType = couponRule.getExpireType();
                CouponDO couponDO = new CouponDO();
                couponDO.setUserId(userId);
                couponDO.setMobile(mobile);
                couponDO.setTemplateId(couponTemplateDTO.getId());
                couponDO.setStatus(CouponStateEnum.NOT_USED.getCode());
                couponDO.setReceivedType(ReceivedTypeEnum.SELF_PICKUP.getCode());
                couponDO.setReceivedTime(new Date());
                if (Objects.equals(expireType, ExpireTypeEnum.FIXED_TIME.getCode())) {
                    Date expireEndTime = couponRule.getExpireEndTime();
                    //当前时间在结束时间之后，设置领取的优惠券信息为已过期
                    if (new Date().after(expireEndTime)) {
                        couponDO.setStatus(CouponStateEnum.EXPIRED.getCode());
                    }
                    couponDO.setValidStartTime(couponRule.getExpireStartTime());
                    couponDO.setValidEndTime(expireEndTime);
                } else {
                    couponDO.setValidStartTime(new Date());
                    couponDO.setValidEndTime(DateUtils.addDay(DateUtils.getEndDate(new Date()),
                            couponRule.getExpireStartPeriodDay()));
                }
                couponDOList.add(couponDO);
            }
            addCouponList(couponDOList);
        } catch (Exception e) {
            LOGGER.error("优惠券领取失败 userId:{},templateIds:{}", userId, JSONUtil.toJsonStr(templateIds), e);
            throw e;
        } finally {
            if (rLock != null && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }


    }

    private void addCouponList(List<CouponDO> couponDOList) {
        if (CollectionUtils.isNotEmpty(couponDOList)) {
            TransactionStatus status = transactionHelper.beginTransaction();
            try {
                couponMapper.batchInsert(couponDOList);
                List<Long> addTemplateIds =
                        couponDOList.stream().map(CouponDO::getTemplateId).collect(Collectors.toList());
                int i = couponTemplateMapper.batchUpdateReceiveNum(addTemplateIds);
                if (i != addTemplateIds.size()) {
                    throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "更新优惠券数量失败");
                }
                transactionHelper.commit(status);
            } catch (Exception e) {
                LOGGER.error("用户领取优惠券失败，数据库操作失败", e);
                transactionHelper.rollback(status);
            }
        }
    }

    private RLock tryLockCoupon(Long userId, String templateStr) {
        String distributedLockName = "COUPON_" + userId + "_" + templateStr;
        RLock lock = redissonClient.getLock(distributedLockName);
        boolean tryLock = false;
        try {
            tryLock = lock.tryLock(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            LOGGER.info("分布式所上锁失败 lockName:{}, StackTraces", distributedLockName, e);
        }
        if (!tryLock) {
            LOGGER.info("分布式所上锁失败(出现并发) lockName:{}, StackTraces:{}",
                    distributedLockName, Thread.getAllStackTraces());
            throw new BizException(CouponBizExEnum.COUPON_REDIS_LOCK_ERROR);
        }
        return lock;
    }

    /**
     * 兑换码兑换优惠券
     *
     * @param couponRedemptionCode
     * @param userId
     * @param mobile
     */
    @Override
    public void redeemCoupon(String couponRedemptionCode, Long userId, String mobile) {
        AssertUtil.notEmpty(couponRedemptionCode, "兑换码不能为空");
        AssertUtil.notNull(userId, "兑换码用户id不能为空");
        AssertUtil.notEmpty(mobile, "兑换码用户手机号不能为空");
        CouponDOQuery couponDOQuery = new CouponDOQuery();
        couponDOQuery.setCouponRedemptionCode(couponRedemptionCode);
        List<CouponDO> couponDOS = couponMapper.selectByQuery(couponDOQuery);
        if (CollectionUtils.isEmpty(couponDOS)) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "兑换码关联优惠券不存在");
        }
        Optional<CouponDO> optional = couponDOS.stream().filter(couponDO -> Objects.nonNull(couponDO.getUserId())).findFirst();
        if (optional.isPresent()) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "兑换码已使用,请勿重复兑换!");
        }
        CouponDO couponDO = couponDOS.get(0);
        Long templateId = couponDO.getTemplateId();
        CouponTemplateQuery templateQuery = new CouponTemplateQuery();
        templateQuery.setCouponTemplateId(templateId);
        templateQuery.setStatusList(Lists.newArrayList(CouponTemplateStatusEnum.ON.getCode()));
        //    优惠券下架状态不可以领取
        CouponTemplateSelector selector = new CouponTemplateSelector();
        selector.setShowRule(true);
        List<CouponTemplateDTO> couponTemplateDTOS = couponTemplateService.listCouponTemplate(templateQuery, selector);
        if (CollectionUtils.isEmpty(couponTemplateDTOS)) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "优惠券已失效，领取失败");
        }
        CouponTemplateDTO couponTemplateDTO = couponTemplateDTOS.get(0);
        //     优惠券时间过期不可以领取
        CouponRule couponRule = couponTemplateDTO.getCouponRule();
        if (couponRule == null) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "兑换优惠券，优惠券规则不存在");
        }
        //校验用户领取优惠券数量限制
        CouponDOQuery query = new CouponDOQuery();
        query.setUserId(userId);
        query.setTemplateId(couponTemplateDTO.getId());
        List<CouponDO> couponDOList = couponMapper.selectByQuery(query);
        Integer userLimitNum = couponRule.getUserLimitNum();
        //校验优惠券领取数量限制
        if (userLimitNum != null && userLimitNum <= couponDOList.size()) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "领取优惠券到上限！");
        }

        Integer expireType = couponRule.getExpireType();
        //    兑换优惠券
        CouponDO updateCoupon = new CouponDO();
        updateCoupon.setId(couponDO.getId());
        updateCoupon.setUserId(userId);
        updateCoupon.setMobile(mobile);
        updateCoupon.setCouponRedemptionCode(couponRedemptionCode);
        updateCoupon.setReceivedType(ReceivedTypeEnum.SELF_PICKUP.getCode());
        updateCoupon.setReceivedTime(new Date());
        if (Objects.equals(expireType, ExpireTypeEnum.FIXED_TIME.getCode())) {
            //
            if (new Date().after(couponRule.getExpireEndTime())) {
                throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "优惠券已过期，领取失败");
            }

            updateCoupon.setValidStartTime(couponRule.getExpireStartTime());
            updateCoupon.setValidEndTime(couponRule.getExpireEndTime());
        } else {
            updateCoupon.setValidStartTime(new Date());
            updateCoupon.setValidEndTime(DateUtils.addDay(DateUtils.getEndDate(new Date()),
                couponRule.getExpireStartPeriodDay()));
        }
        couponMapper.update(updateCoupon);
    }

    @Override
    public Coupon queryCouponById(Long couponId) {
        AssertUtil.notNull(couponId, "优惠券id不能为空");
        CouponDO couponDO = couponMapper.selectByPrimaryKey(couponId);
        if (couponDO != null) {
            Coupon coupon = new Coupon();
            BeanUtils.copyProperties(couponDO, coupon);
            return coupon;
        }
        return null;
    }

    @Override
    public int batchUpdate(List<Coupon> couponList) {
        if (CollectionUtil.isNotEmpty(couponList)) {
            return couponMapper.batchUpdate(CopyUtil.copyList(couponList, CouponDO.class));
        }
        return 0;
    }

    @Override
    public int getReceivedCount(Long templateId) {
        return couponMapper.selectReceivedCount(templateId);
    }

    @Override
    public List<CouponDTO> getCouponDTOByUserId(Long userId) {
        AssertUtil.notNull(userId, "查询用户可用优惠券列表，userId不能为空");
        CouponDOQuery query = new CouponDOQuery();
        query.setUserId(userId);
        query.setStatusList(Lists.newArrayList(CouponStateEnum.NOT_USED.getCode()));
        query.setValidStartTime(new Date());
        List<CouponDO> couponDOList = couponMapper.selectByQuery(query);
        List<CouponDTO> resultList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(couponDOList)) {
            List<Long> templateIds = couponDOList.stream().map(CouponDO::getTemplateId).collect(Collectors.toList());
            CouponTemplateQuery couponTemplateQuery = new CouponTemplateQuery();
            couponTemplateQuery.setCouponTemplateIds(templateIds);
            CouponTemplateSelector selector = new CouponTemplateSelector();
            selector.setShowRule(true);
            List<CouponTemplateDTO> list = couponTemplateService.listCouponTemplate(couponTemplateQuery, selector);
            Map<Long, CouponTemplateDTO> templateDTOMap = list.stream().collect(Collectors.toMap(CouponTemplateDTO::getId, Function.identity()));
            for (CouponDO couponDO : couponDOList) {
                CouponDTO couponDTO = new CouponDTO();
                Coupon coupon = CopyUtil.copy(couponDO, Coupon.class);
                couponDTO.setCoupon(coupon);
                CouponTemplateDTO couponTemplateDTO = templateDTOMap.get(couponDO.getTemplateId());
                if (couponTemplateDTO != null) {
                    couponDTO.setCouponTemplateDTO(couponTemplateDTO);
                }
                resultList.add(couponDTO);
            }
        }
        return resultList;
    }

    /**
     * 锁定优惠券接口
     *
     * @param couponId
     * @return
     */
    @Override
    public void lockCoupon(Long couponId) {
        AssertUtil.notNull(couponId, "锁定优惠券id不能为空");
        CouponDO couponDO = couponMapper.selectByPrimaryKey(couponId);
        if (couponDO == null) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "锁定优惠券信息不存在");
        }
        if (!Objects.equals(couponDO.getStatus(), CouponStateEnum.NOT_USED.getCode())) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "锁定优惠券状态异常");
        }
        CouponDO updateCouponDO = new CouponDO();
        updateCouponDO.setId(couponId);
        updateCouponDO.setStatus(CouponStateEnum.FROZEN.getCode());
        couponMapper.update(updateCouponDO);
    }

    /**
     * 核销优惠券接口
     *
     * @param couponId
     * @param orderNum
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void couponFinish(Long couponId, String orderNum) {
        AssertUtil.notNull(couponId, "核销优惠券id不能为空");
        AssertUtil.notEmpty(orderNum, "核销优惠券订单号不能为空");
        CouponDO couponDO = couponMapper.selectByPrimaryKey(couponId);
        if (couponDO == null) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "核销优惠券信息不存在");
        }
        if (!Objects.equals(couponDO.getStatus(), CouponStateEnum.FROZEN.getCode())) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "核销优惠券为非冻结状态");
        }
        //更新优惠券领券信息
        CouponDO updateCoupon = new CouponDO();
        updateCoupon.setId(couponId);
        updateCoupon.setStatus(CouponStateEnum.USED.getCode());
        updateCoupon.setOrderNum(orderNum);
        updateCoupon.setUsedTime(new Date());
        couponMapper.update(updateCoupon);
        Long templateId = couponDO.getTemplateId();
        AssertUtil.notNull(templateId, "优惠券模板信息不存在");
        CouponTemplateDO couponTemplateDO = couponTemplateMapper.selectByPrimaryKey(templateId);
        if (couponTemplateDO == null) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "优惠券关联的模板信息不存在");
        }
        //    更新优惠券模板已使用数量
        couponTemplateMapper.batchUpdateFinishNum(Lists.newArrayList(templateId));
    }

    /**
     * 优惠券退还接口
     *
     * @param orderNum
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void couponRefund(String orderNum) {
        AssertUtil.notEmpty(orderNum, "核销订单号不能为空");
        //更新优惠券领券信息
        //根据优惠券过期时间修改优惠券状态
        //1、如果当前时间小于优惠券的截止时间，优惠券变为可用状态
        //2、如果当前时间大于优惠券的截止时间，优惠券变为已过期
        CouponDOQuery query = new CouponDOQuery();
        query.setOrderNum(orderNum);
        List<CouponDO> couponDOList = couponMapper.selectByQuery(query);
        if (CollectionUtils.isEmpty(couponDOList)) {
            LOGGER.warn("订单号为：【{}】,退还优惠券信息不存在", orderNum);
            return;
        }
        List<CouponDO> updateCouponDOS = Lists.newArrayList();
        List<Long> templateIds = Lists.newArrayList();
        for (CouponDO couponDO : couponDOList) {
            CouponDO updateCouponDO = new CouponDO();
            Integer status;
            if (new Date().before(couponDO.getValidEndTime())) {
                status = CouponStateEnum.NOT_USED.getCode();
            } else {
                status = CouponStateEnum.EXPIRED.getCode();
            }
            updateCouponDO.setId(couponDO.getId());
            updateCouponDO.setStatus(status);
            updateCouponDOS.add(updateCouponDO);
            templateIds.add(couponDO.getTemplateId());
        }
        couponMapper.couponRefund(updateCouponDOS);
        AssertUtil.notEmpty(templateIds, "优惠券模板信息不存在");
        CouponTemplateDOQuery templateDOQuery = new CouponTemplateDOQuery();
        templateDOQuery.setCouponTemplateIds(templateIds);
        List<CouponTemplateDO> couponTemplateDOS = couponTemplateMapper.selectByQuery(templateDOQuery);
        if (CollectionUtils.isEmpty(couponTemplateDOS)) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "优惠券关联的模板信息不存在");
        }
        //    退还模板已使用数量
        couponTemplateMapper.batchUpdateRefundNum(templateIds);
    }

    @Override
    public List<Long> listOverTimeCoupon(Integer status, Date time) {
        return couponMapper.selectOverTimeCoupon(status, time);
    }

    @Override
    public int batchUpdateStatus(Integer status, List<Long> couponIds) {
        if (CollectionUtil.isEmpty(couponIds)) {
            return 0;
        }
        return couponMapper.batchUpdateStatus(status, couponIds);
    }
}
