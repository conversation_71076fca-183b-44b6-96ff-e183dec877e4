package com.meerkat.coupon.param;

import com.meerkat.coupon.enums.CouponTemplateSortField;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName CouponTemplateDOQuery
 * @description
 * @createTime 2022/10/9 11:32
 */
public class CouponTemplateDOQuery {

    /**
     * 优惠券模板id
     */
    private Long couponTemplateId;

    /**
     * 优惠券模板id集合
     */
    private List<Long> couponTemplateIds;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 状态：0未开始，1进行中，2已结束
     */
    private List<Integer> statusList;

    /**
     * 优惠券类型：1：满减，2满折，3邮费券
     */
    private List<Integer> couponTypeList;

    /**
     * 机构或渠道id
     */
    private Long chorgaId;

    /**
     * 渠道&机构类型：1-机构 2-渠道
     */
    private Integer chorgaType;

    /**
     * 来源：1、ops，2、saas
     */
    private Integer source;

    /**
     * 是否降序
     */
    private Boolean desc;

    /**
     * 排序字段
     */
    private String sortField;

    public Long getCouponTemplateId() {
        return couponTemplateId;
    }

    public void setCouponTemplateId(Long couponTemplateId) {
        this.couponTemplateId = couponTemplateId;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public List<Long> getCouponTemplateIds() {
        return couponTemplateIds;
    }

    public void setCouponTemplateIds(List<Long> couponTemplateIds) {
        this.couponTemplateIds = couponTemplateIds;
    }

    public Long getChorgaId() {
        return chorgaId;
    }

    public void setChorgaId(Long chorgaId) {
        this.chorgaId = chorgaId;
    }

    public Integer getChorgaType() {
        return chorgaType;
    }

    public void setChorgaType(Integer chorgaType) {
        this.chorgaType = chorgaType;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public List<Integer> getCouponTypeList() {
        return couponTypeList;
    }

    public void setCouponTypeList(List<Integer> couponTypeList) {
        this.couponTypeList = couponTypeList;
    }

    public Boolean getDesc() {
        return desc;
    }

    public void setDesc(Boolean desc) {
        this.desc = desc;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }
}
