package com.meerkat.coupon.service.rule;

import com.meerkat.coupon.dto.CouponTemplateDTO;
import com.meerkat.coupon.model.CouponResult;
import com.meerkat.coupon.model.CouponRuleContext;
import com.meerkat.smart.channel.service.ChannelService;
import com.meerkat.smart.site.enums.SiteTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @program meerkat-origin
 * @description: 站点信息校验
 * @author: fandongdong
 * @create: 2022/10/06 22:41
 */
@Service("couponSiteRuleService")
public class CouponSiteRuleService implements CouponRuleService {

    @Autowired
    private ChannelService channelService;

    /**
     * 校验规则
     * 判断传参过来的机构id是否满足优惠券使用条件
     * 1、优惠券为平台券，需要知道当前机构是否上架了小程序，满足条件可用
     * 2、商家券，需要满足发券机构和校验机构一致
     *
     * @param couponRuleContext
     * @return
     */
    @Override
    public void validate(CouponRuleContext couponRuleContext) {
        List<CouponResult> couponListResults = couponRuleContext.getAbleListResults();
        if (CollectionUtils.isEmpty(couponListResults)) {
            return;
        }
        List<CouponResult> availableCouponList = new ArrayList<>();
        Long userId = couponRuleContext.getUserId();
        Long chorgaId = couponRuleContext.getChorgaId();
        Integer chorgaType = couponRuleContext.getChorgaType();
        Long orgaId = couponRuleContext.getOrgaId();
        //如果传参进来的机构类型是渠道，判断渠道下面的
        Boolean exist = channelService.orgaPresentInChannel(chorgaId, orgaId);
        for (CouponResult couponListResult : couponListResults) {
            CouponTemplateDTO couponTemplateDTO = couponListResult.getCouponTemplateDTO();
            Long couponChorgaId = couponTemplateDTO.getChorgaId();
            //需要判断请求来源是H5还是小程序
            //本期默认走渠道校验逻辑
            if (Objects.equals(SiteTypeEnum.CHANNEL.getCode(), chorgaType)) {
                if (exist != null && exist && Objects.equals(chorgaId, couponChorgaId)) {
                    availableCouponList.add(couponListResult);
                } else {
                    couponRuleContext.addErrorInfo(couponTemplateDTO.getId(), "该优惠券不满足机构使用条件");
                }
            } else {
                //如果是h5
                if (Objects.equals(chorgaId, couponChorgaId)) {
                    availableCouponList.add(couponListResult);
                } else {
                    couponRuleContext.addErrorInfo(couponTemplateDTO.getId(), "该优惠券不满足机构使用条件");
                }
            }
        }
        couponRuleContext.setAbleListResults(availableCouponList);
    }
}
