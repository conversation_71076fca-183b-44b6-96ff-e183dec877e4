package com.meerkat.coupon.service.impl;
import com.google.common.collect.Lists;

import com.meerkat.common.exception.BizException;
import com.meerkat.coupon.dto.CouponTemplateDTO;
import com.meerkat.coupon.enums.CouponRuleServiceEnum;
import com.meerkat.coupon.exception.CouponBizExEnum;
import com.meerkat.coupon.model.Coupon;
import com.meerkat.coupon.model.CouponResult;
import com.meerkat.coupon.model.CouponRuleContext;
import com.meerkat.coupon.param.*;
import com.meerkat.coupon.service.CouponService;
import com.meerkat.coupon.service.CouponTemplateService;
import com.meerkat.coupon.service.CouponValidateService;
import com.meerkat.coupon.service.rule.CouponRuleService;
import com.meerkat.coupon.validate.CouponValidateResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program meerkat-origin
 * @description:
 * @author: fandongdong
 * @create: 2022/10/10 20:38
 */
@Service
public class CouponValidateServiceImpl implements CouponValidateService, ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Autowired
    private CouponService couponService;

    @Autowired
    private CouponTemplateService couponTemplateService;

    /**
     * 优惠券使用规则校验
     *
     * @param couponValidateParam
     * @return
     */
    @Override
    public CouponValidateResult validateCouponUse(CouponValidateParam couponValidateParam) {
        List<Long> couponIds = couponValidateParam.getCouponIds();
        CouponQuery couponQuery = new CouponQuery();
        couponQuery.setIds(couponIds);
        List<Coupon> couponList = couponService.listCouponByQuery(couponQuery);
        if (CollectionUtils.isEmpty(couponList)) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "查询优惠券不存在");
        }
        CouponTemplateQuery query = new CouponTemplateQuery();
        query.setCouponTemplateIds(couponList.stream().map(Coupon::getTemplateId).distinct().collect(Collectors.toList()));
        CouponTemplateSelector selector = new CouponTemplateSelector();
        selector.setShowRule(true);
        List<CouponTemplateDTO> list = couponTemplateService.listCouponTemplate(query, selector);
        if (CollectionUtils.isEmpty(list)) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "优惠券模板信息不存在");
        }
        Map<Long, CouponTemplateDTO> templateDTOMap = list.stream().collect(Collectors.toMap(CouponTemplateDTO::getId, Function.identity()));
        List<CouponResult> couponResults = Lists.newArrayList();
        for (Coupon coupon : couponList) {
            CouponResult result = new CouponResult();
            result.setCoupon(coupon);
            CouponTemplateDTO couponTemplateDTO = templateDTOMap.get(coupon.getTemplateId());
            result.setCouponTemplateDTO(couponTemplateDTO);
            couponResults.add(result);
        }
        CouponRuleContext couponRuleContext = new CouponRuleContext();
        couponRuleContext.setCouponResultLists(couponResults);
        couponRuleContext.setActivityId(couponValidateParam.getActivityId());
        couponRuleContext.setDistributeCode(couponValidateParam.getDistributeCode());
        couponRuleContext.setChorgaId(couponValidateParam.getChorgaId());
        couponRuleContext.setChorgaType(couponValidateParam.getChorgaType());
        couponRuleContext.setOrderPrice(couponValidateParam.getBaseOrderPrice());
        couponRuleContext.setGoodsId(couponValidateParam.getGoodsId());
        couponRuleContext.setUserId(couponValidateParam.getUserId());
        couponRuleContext.setAbleListResults(couponResults);
        couponRuleContext.setOrgaId(couponValidateParam.getOrgaId());
        List<CouponRuleServiceEnum> ruleServiceEnums = Lists.newArrayList(
                CouponRuleServiceEnum.EXCLUDE,
                CouponRuleServiceEnum.EXPIRE_TIME,
                CouponRuleServiceEnum.STATE,
                CouponRuleServiceEnum.FULL_MINUS,
                CouponRuleServiceEnum.SCOPE,
                CouponRuleServiceEnum.SITE,
                CouponRuleServiceEnum.USER
        );
        for (CouponRuleServiceEnum ruleServiceEnum : ruleServiceEnums){
            CouponRuleService couponRuleService = (CouponRuleService) applicationContext.getBean(ruleServiceEnum.getRuleServiceName());
             couponRuleService.validate(couponRuleContext);
        }
        return couponRuleContext.getCouponValidateResult();
    }


    /**
     * 优惠券领取规则校验
     *
     * @param couponValidateParam
     * @return
     */
    @Override
    public CouponValidateResult validateCouponReceive(CouponReceiveValidateParam receiveValidateParam) {
        List<Long> templateIds = receiveValidateParam.getTemplateIds();
        CouponQuery couponQuery = new CouponQuery();
        CouponTemplateQuery query = new CouponTemplateQuery();
        query.setCouponTemplateIds(templateIds);
        CouponTemplateSelector selector = new CouponTemplateSelector();
        selector.setShowRule(true);
        List<CouponTemplateDTO> list = couponTemplateService.listCouponTemplate(query, selector);
        if (CollectionUtils.isEmpty(list)) {
            throw new BizException(CouponBizExEnum.GENERAL_EXCEPTION, "优惠券模板信息不存在");
        }
        List<CouponResult> couponResults = Lists.newArrayList();
        for (CouponTemplateDTO couponTemplateDTO : list) {
            CouponResult result = new CouponResult();
            result.setCouponTemplateDTO(couponTemplateDTO);
            couponResults.add(result);
        }

        CouponRuleContext couponRuleContext = new CouponRuleContext();
        couponRuleContext.setCouponResultLists(couponResults);
        couponRuleContext.setChorgaId(receiveValidateParam.getChorgaId());
        couponRuleContext.setChorgaType(receiveValidateParam.getChorgaType());
        couponRuleContext.setGoodsId(receiveValidateParam.getGoodsId());
        couponRuleContext.setOrgaId(receiveValidateParam.getOrgaId());
        couponRuleContext.setAbleListResults(couponResults);
        List<CouponRuleServiceEnum> ruleServiceEnums = Lists.newArrayList(
                CouponRuleServiceEnum.SCOPE,
                CouponRuleServiceEnum.SITE,
                CouponRuleServiceEnum.BATCH_NUM,
                CouponRuleServiceEnum.BATCH_STATE
        );
        for (CouponRuleServiceEnum ruleServiceEnum : ruleServiceEnums){
            CouponRuleService couponRuleService = (CouponRuleService) applicationContext.getBean(ruleServiceEnum.getRuleServiceName());
            couponRuleService.validate(couponRuleContext);
        }
        return couponRuleContext.getCouponValidateResult();

    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
