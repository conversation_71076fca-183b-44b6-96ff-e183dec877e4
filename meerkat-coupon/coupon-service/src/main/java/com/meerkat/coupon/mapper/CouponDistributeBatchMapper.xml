<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.meerkat.coupon.mapper.CouponDistributeBatchMapper">

    <resultMap id="BaseResultMap" type="com.meerkat.coupon.dao.object.CouponDistributeBatchDO" >
        <result column="id" property="id" />
        <result column="distribute_method" property="distributeMethod" />
        <result column="code_num" property="codeNum" />
        <result column="user_scope" property="userScope" />
        <result column="subgroup_user_type" property="subgroupUserType" />
        <result column="distribute_num" property="distributeNum" />
        <result column="distribute_time_type" property="distributeTimeType" />
        <result column="timed_distribute_time" property="timedDistributeTime" />
        <result column="status" property="status" />
        <result column="operate_id" property="operateId" />
        <result column="operate_name" property="operateName" />
        <result column="ext" property="ext" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="file_url" property="fileUrl" />
        <result column="excel_users" property="excelUsers" />
    </resultMap>

    <sql id="Insert_Column_List">
        distribute_method,
        code_num,
        user_scope,
        subgroup_user_type,
        distribute_num,
        distribute_time_type,
        timed_distribute_time,
        status,
        operate_id,
        operate_name,
        ext,
        file_url,
        excel_users
    </sql>

    <sql id="Base_Column_List">
        id,
        <include refid="Insert_Column_List" />,
        is_deleted,
        gmt_created,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.meerkat.coupon.dao.object.CouponDistributeBatchDO">
        INSERT INTO tb_coupon_distribute_batch(<include refid="Insert_Column_List" />)
        values (
        #{distributeMethod},
        #{codeNum},
        #{userScope},
        #{subgroupUserType},
        #{distributeNum},
        #{distributeTimeType},
        #{timedDistributeTime},
        #{status},
        #{operateId},
        #{operateName},
        #{ext},
        #{fileUrl},
        #{excelUsers}
        )

    </insert>

    <update id="logicDel" >
        update tb_coupon_distribute_batch
        set is_deleted = 1
        where id = #{id}
    </update>

    <update id="update" parameterType="com.meerkat.coupon.dao.object.CouponDistributeBatchDO">
        UPDATE tb_coupon_distribute_batch
        <set>
            <if test="null != distributeMethod">distribute_method = #{distributeMethod},</if>
            <if test="null != codeNum">code_num = #{codeNum},</if>
            <if test="null != userScope">user_scope = #{userScope},</if>
            <if test="null != subgroupUserType">subgroup_user_type = #{subgroupUserType},</if>
            <if test="null != distributeNum">distribute_num = #{distributeNum},</if>
            <if test="null != distributeTimeType">distribute_time_type = #{distributeTimeType},</if>
            <if test="null != timedDistributeTime">timed_distribute_time = #{timedDistributeTime},</if>
            <if test="null != status">status = #{status},</if>
            <if test="null != operateId">operate_id = #{operateId},</if>
            <if test="null != operateName and '' != operateName">operate_name = #{operateName},</if>
            <if test="null != ext and '' != ext">ext = #{ext},</if>
            <if test="null != fileUrl and '' != fileUrl">file_url = #{fileUrl},</if>
            <if test="null != excelUsers and '' != excelUsers">excel_users = #{excelUsers}</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="couponBatchDetail" resultType="com.meerkat.coupon.vo.CouponBatchDetailVO">
        SELECT
            tb.id,
            tb.excel_users mobilesData,
            tb.distribute_num couponCount,
            tb.distribute_method grantType,
            tb.user_scope userScope,
            tb.distribute_time_type distributeTimeType,
            tbr.coupon_template_id templateId,
            tt.template_name templateName
        FROM
            tb_coupon_distribute_batch tb
                LEFT JOIN tb_coupon_distribute_batch_template_relation tbr ON tb.id = tbr.distribute_batch_id
                LEFT JOIN tb_coupon_template tt ON tbr.coupon_template_id = tt.id
        WHERE
            tb.id = #{batchId}
        ORDER BY
            tb.gmt_created DESC
    </select>

    <select id="getExchangeCodeByBatchId" resultType="java.lang.String">
        select coupon_redemption_code from tb_coupon where distribute_batch_id =#{batchId}
    </select>

</mapper>