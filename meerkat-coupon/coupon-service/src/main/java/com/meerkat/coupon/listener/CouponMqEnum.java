package com.meerkat.coupon.listener;

/**
 * <AUTHOR>
 * @EnumName OrderMqEnum
 * @description
 * @createTime 2022/09/19 10:18
 */
public enum CouponMqEnum {

    /**
     * 优惠券核销队列
     */
    MIDDLE_PLATFORM_ORDER_COUPON_FINISH_QUEUE("middlePlatform.order.coupon.finish.queue", "优惠券核销队列"),

    /**
     * 优惠券退还队列
     */
    MIDDLE_PLATFORM_ORDER_COUPON_REFUND_QUEUE("middlePlatform.order.coupon.refund.queue", "优惠券退还队列"),
    ;

    CouponMqEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
