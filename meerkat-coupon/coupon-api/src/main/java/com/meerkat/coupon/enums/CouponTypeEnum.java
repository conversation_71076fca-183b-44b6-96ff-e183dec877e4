package com.meerkat.coupon.enums;

/**
 * <AUTHOR>
 * @EnumName CouponTypeEnum
 * @description 优惠券类型枚举
 * @createTime 2022/10/11 11:48
 */
public enum CouponTypeEnum {

    FULL_REDUCE(1, "满减"),
    FULL_DISCOUNT(2, "满折"),
    STAMP(3, "邮费券")
    ;

    private final Integer code;

    private final String name;

    CouponTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
