<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.message.mapper.SmsSignMapper">

    <resultMap id="BaseResultMap" type="com.meerkat.message.mapper.dataobj.SmsSignDO">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="chorgaId" column="chorga_id" jdbcType="BIGINT"/>
            <result property="chorgaType" column="chorga_type" jdbcType="TINYINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="isDeleted" column="is_deleted" jdbcType="TINYINT"/>
            <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="SELECT">
        SELECT `id`,
               `chorga_id`,
               `chorga_type`,
               `name`,
               `status`,
               `is_deleted`,
               `gmt_created`,
               `gmt_modified`
        FROM tb_sms_sign
    </sql>
    <insert id="insert">
        INSERT INTO tb_sms_sign (`chorga_id`, `chorga_type`, `name`, `status`)
        VALUES (#{chorgaId}, #{chorgaType}, #{name}, #{status})
    </insert>
    <update id="updateByChorga">
        UPDATE tb_sms_sign
        SET `name` = #{name}
        WHERE chorga_id = #{chorgaId}
          AND chorga_type = #{chorgaType}
    </update>
    <select id="selectOne" resultType="com.meerkat.message.mapper.dataobj.SmsSignDO">
        <include refid="SELECT"/>
        <where>
            chorga_id = #{chorgaId}
            AND chorga_type = #{chorgaType}
            AND is_deleted = 0
        </where>
        LIMIT 1
    </select>
</mapper>
