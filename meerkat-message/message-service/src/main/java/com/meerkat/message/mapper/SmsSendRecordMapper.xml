<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.message.mapper.SmsSendRecordMapper">
    <resultMap type="com.meerkat.message.mapper.dataobj.SmsSendRecordDO" id="smsSendRecordResultMap">
        <result column="id" property="id" />
        <result column="sign_id" property="signId"/>
        <result column="chorga_id" property="chorgaId" />
        <result column="chorga_type" property="chorgaType" />
        <result column="status" property="status" />
        <result column="mobile" property="mobile" />
        <result column="content" property="content" />
        <result column="template_id" property="templateId"/>
        <result column="sign" property="sign" />
        <result column="serial_no" property="serialNo" />
        <result column="nation_code" property="nationCode" />
        <result column="fee" property="fee" />
        <result column="description" property="description" />
        <result column="facilitator" property="facilitator" />
        <result column="user_receive_time" property="userReceiveTime" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_created" property="gmtCreated" />
    </resultMap>

    <sql id="sms_send_record_selectField">
        select id,
               sign_id,
               chorga_id,
               chorga_type,
               template_id,
               sign,
               serial_no,
               nation_code,
               mobile,
               fee,
               content,
               status,
               description,
               facilitator,
               user_receive_time,
               gmt_created,
               gmt_modified
        from tb_sms_send_record
    </sql>

    <insert id="insert" parameterType="com.meerkat.message.mapper.dataobj.SmsSendRecordDO">
        INSERT INTO tb_sms_send_record(
            sign_id,
            <if test="chorgaId != null">
            chorga_id,
            </if>
            <if test="chorgaType != null">
            chorga_type,
            </if>
            status,
            mobile,
            content,
            template_id,
            sign,
            <if test="serialNo != null">
                serial_no,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="facilitator != null">
                facilitator,
            </if>
            <if test="userReceiveTime != null">
                user_receive_time,
            </if>
            nation_code,
            fee
        )
        VALUES
        (
            #{signId},
            <if test="chorgaId != null">
            #{chorgaId},
            </if>
            <if test="chorgaType != null">
            #{chorgaType},
            </if>
            #{status},
            #{mobile},
            #{content},
            #{templateId},
            #{sign},
            <if test="serialNo != null">
                #{serialNo},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="facilitator != null">
                #{facilitator},
            </if>
            <if test="userReceiveTime != null">
                #{userReceiveTime},
            </if>
            #{nationCode},
            #{fee}
        )
    </insert>

    <update id="update" parameterType="com.meerkat.message.mapper.dataobj.SmsSendRecordDO">
        UPDATE tb_sms_send_record
        <set>
            `status` = #{status}
            ,`description` = #{description}
            <if test="facilitator != null">
                ,facilitator = #{facilitator}
            </if>
            <if test="userReceiveTime != null">
                ,user_receive_time = #{userReceiveTime}
            </if>
        </set>
        WHERE serial_no = #{serialNo}
    </update>

    <select id="list" resultMap="smsSendRecordResultMap">
        <include refid="sms_send_record_selectField"/>
        <where>
            <if test="status != null">
                and `status` = #{status}
            </if>
        </where>
    </select>
</mapper>