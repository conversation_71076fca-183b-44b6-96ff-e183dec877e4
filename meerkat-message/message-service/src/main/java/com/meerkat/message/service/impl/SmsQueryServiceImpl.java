package com.meerkat.message.service.impl;

import com.meerkat.message.model.SmsQueryResponse;
import com.meerkat.message.service.SmsQueryService;
import com.meerkat.message.service.client.impl.SmsTencentClientImpl;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.sms.v20190711.models.PullSmsSendStatus;
import com.tencentcloudapi.sms.v20190711.models.PullSmsSendStatusByPhoneNumberResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 短信状态查询接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022/2/10 18:04
 */
//@Service
public class SmsQueryServiceImpl implements SmsQueryService {

    private static final String SUCCESS = "SUCCESS";
    private static final String FAIL = "FAIL";


    private final Logger logger = LoggerFactory.getLogger(MessageServiceImpl.class);

    @Autowired
    private SmsTencentClientImpl smsTencentClient;

    @Override
    public SmsQueryResponse queryStatus(Date sendDate, String mobile, String serialNo) {
        try {
            PullSmsSendStatusByPhoneNumberResponse response = smsTencentClient.pullSmsStatus(sendDate, mobile);
            PullSmsSendStatus[] pullSmsSendStatusSet = response.getPullSmsSendStatusSet();
            if (pullSmsSendStatusSet.length > 0) {
                List<PullSmsSendStatus> collect = Arrays.stream(pullSmsSendStatusSet)
                        .filter(e -> e.getSerialNo().equalsIgnoreCase(serialNo))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)) {
                    PullSmsSendStatus pullSmsSendStatus = collect.get(0);
                    String description = pullSmsSendStatus.getDescription();
                    SmsQueryResponse smsQueryResponse = new SmsQueryResponse();
                    smsQueryResponse.setDescription(description);
                    smsQueryResponse.setSendStatus(pullSmsSendStatus.getReportStatus());
                    if (SUCCESS.equals(pullSmsSendStatus.getReportStatus())) {
                        smsQueryResponse.setUserReceiveTime(pullSmsSendStatus.getUserReceiveTime());
                    }
                    return smsQueryResponse;
                }
            }
        } catch (TencentCloudSDKException e) {
            logger.error("拉取短信状态失败,流水号:{}", serialNo, e);
        }
        return null;
    }
}
