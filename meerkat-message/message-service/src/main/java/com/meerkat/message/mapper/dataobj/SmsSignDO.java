package com.meerkat.message.mapper.dataobj;

import java.util.Date;

/**
 * 短信签名表
 * @TableName tb_sms_sign
 */
public class SmsSignDO{

    private Long id;

    /**
     * 发卡机构/渠道
     */
    private Long chorgaId;

    /**
     * 1机构 2渠道
     */
    private Integer chorgaType;

    /**
     * 名称
     */
    private String name;

    /**
     * 审核状态：0->审核中 1->已通过 2->未通过
     */
    private Integer status;

    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 发卡机构/渠道
     */
    public Long getChorgaId() {
        return chorgaId;
    }

    /**
     * 发卡机构/渠道
     */
    public void setChorgaId(Long chorgaId) {
        this.chorgaId = chorgaId;
    }

    /**
     * 1机构 2渠道
     */
    public Integer getChorgaType() {
        return chorgaType;
    }

    /**
     * 1机构 2渠道
     */
    public void setChorgaType(Integer chorgaType) {
        this.chorgaType = chorgaType;
    }

    /**
     * 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 审核状态：0->审核中 1->已通过 2->未通过
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 审核状态：0->审核中 1->已通过 2->未通过
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * 创建时间
     */
    public Date getGmtCreated() {
        return gmtCreated;
    }

    /**
     * 创建时间
     */
    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    /**
     * 修改时间
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * 修改时间
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
}