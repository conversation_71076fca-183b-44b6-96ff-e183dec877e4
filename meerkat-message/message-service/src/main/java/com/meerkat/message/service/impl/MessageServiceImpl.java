package com.meerkat.message.service.impl;

import com.meerkat.message.model.Response;
import com.meerkat.message.model.Result;
import com.meerkat.message.model.param.MessageParam;
import com.meerkat.message.service.MessageService;
import com.meerkat.message.service.registry.MessageSenderRegistry;
import com.meerkat.message.service.sender.MessageSender;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 消息服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022/2/8 9:53
 */
@Service
public class MessageServiceImpl implements MessageService {

    @Autowired
    private MessageSenderRegistry messageSenderRegistry;

    @Override
    public Response send(MessageParam messageParam) {
        Response response = new Response();
        MessageSender messageSender = messageSenderRegistry.getMessageSender(messageParam.getMessageType());
        Result result = messageSender.sendMessage(messageParam);
        if (result.getSuccess()){
            response.setDefaultSuccess();
        }else {
            response.setError(result.getRemark());
        }
        return response;
    }
}
