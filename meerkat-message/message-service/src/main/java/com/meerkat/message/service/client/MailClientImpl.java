package com.meerkat.message.service.client;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <p>
 * 邮件发送客户端类型
 * </p>
 *
 * <AUTHOR>
 * @since 2022/3/26 15:47
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface MailClientImpl {
    /**
     * 权重，权重越大 越优先使用
     */
    int rank();
}
