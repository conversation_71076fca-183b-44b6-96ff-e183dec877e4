package com.meerkat.message.mapper;

import com.meerkat.message.mapper.dataobj.SmsTemplateParamDO;
import com.meerkat.message.mapper.query.SmsTemplateParamDaoQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface SmsTemplateParamMapper {

    /**
     * 根据id查询单条
     */
    SmsTemplateParamDO selectOneById(@Param("id") Long id);

    /**
     * 根据id列表查询多条
     */
    List<SmsTemplateParamDO> selectByIds(List<Long> ids);

    /**
     * 条件查询多条
     */
    List<SmsTemplateParamDO> selectByCondition(SmsTemplateParamDaoQuery query);

    /**
     * 新增一条
     */
    int insert(SmsTemplateParamDO smsTemplateParam);

    /**
     * 根据主键id更新
     */
    void updateById(SmsTemplateParamDO smsTemplateParam);
}