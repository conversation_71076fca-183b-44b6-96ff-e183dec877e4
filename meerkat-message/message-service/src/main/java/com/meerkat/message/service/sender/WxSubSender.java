package com.meerkat.message.service.sender;

import cn.hutool.json.JSONUtil;
import com.meerkat.common.exception.BizException;
import com.meerkat.message.annotation.MessageType;
import com.meerkat.message.enums.MessageTypeEnum;
import com.meerkat.message.enums.WxSubStatusEnum;
import com.meerkat.message.exception.WxSubException;
import com.meerkat.message.mapper.SendWxRecordMapper;
import com.meerkat.message.mapper.dataobj.SendWxRecordDO;
import com.meerkat.message.model.Result;
import com.meerkat.message.model.param.MessageParam;
import com.meerkat.message.model.param.WxSubParam;
import com.meerkat.user.model.UserWxBind;
import com.meerkat.user.service.UserWxBindService;
import com.meerkat.wx.mp.model.MpConfig;
import com.meerkat.wx.mp.param.WxParam;
import com.meerkat.wx.mp.service.WxConfigService;
import com.meerkat.wx.mp.service.WxMpProxyService;
import me.chanjar.weixin.common.bean.subscribemsg.TemplateInfo;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpSubscribeMsgService;
import me.chanjar.weixin.mp.bean.subscribe.WxMpSubscribeMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 订阅通知发送
 * </p>
 *
 * <AUTHOR>
 * @since 2022/4/13 17:58
 */
@Component
@MessageType(type = MessageTypeEnum.WX_SUB)
public class WxSubSender extends MessageSender{
    private final Logger LOG = LoggerFactory.getLogger(WxSubSender.class);
    @Autowired(required = false)
    private WxMpProxyService wxMpProxyService;
    @Autowired(required = false)
    private WxConfigService wxMpConfigService;
    @Autowired(required = false)
    private UserWxBindService userWxBindService;
    @Autowired
    private SendWxRecordMapper sendWxRecordMapper;

    @Override
    protected void prepare(MessageParam messageParam) {
        super.prepare(messageParam);
        WxSubParam wxSubParam = (WxSubParam) messageParam;
        //获取站点appId
        MpConfig configByOrganization = wxMpConfigService.getConfigByOrganization(wxSubParam.getChorgaId(), wxSubParam.getChorgaType());
        if (Objects.isNull(configByOrganization)) {
            LOG.info(WxSubException.SITE_NOT_FOUND.getMessage());
            throw new BizException(WxSubException.SITE_NOT_FOUND);
        }
        String appId = configByOrganization.getAppId();
        //获取用户openId
        UserWxBind userWxBind = userWxBindService.loadByUserIdAndAppKey(wxSubParam.getUserId(), configByOrganization.getAppId());
        if (Objects.isNull(userWxBind)) {
            LOG.info(WxSubException.OPEN_ID_NOT_FOUND.getMessage());
            throw new BizException(WxSubException.OPEN_ID_NOT_FOUND);
        }
        String openId = userWxBind.getOpenId();
        wxSubParam.setAppId(appId);
        wxSubParam.setOpenId(openId);
    }

    @Override
    protected Result send(MessageParam messageParam) {
        Result result;
        WxSubParam wxSubParam = (WxSubParam)messageParam;
        //微信消息发送service
        WxMpSubscribeMsgService subscribeMsgService = wxMpProxyService.getSubscribeMsgService(new WxParam(wxSubParam.getTrusted(), wxSubParam.getAppId()));
        try {
            //获取私有模板列表，根据模板title来获取模板id
            List<TemplateInfo> templateList = subscribeMsgService.getTemplateList();
            if (!CollectionUtils.isEmpty(templateList)) {
                //根据title筛选需要的模板
                List<TemplateInfo> collect = templateList.stream()
                        .filter(e -> wxSubParam.getWxSubTemplateEnum().getTitle().equals(e.getTitle()))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)) {
                    TemplateInfo templateInfo = collect.get(0);
                    //构建从微信获取的参数
                    subscribeMsgService.send(buildWxMpSubscribeMessage(wxSubParam, templateInfo));
                    LOG.info("提交订阅消息成功");
                    result = Result.success();
                    result.setTemplateInfo(templateInfo);
                } else {
                    LOG.info(WxSubException.TEMPLATE_NOT_FOUND.getMessage());
                    result = Result.failed(WxSubException.TEMPLATE_NOT_FOUND.getMessage());
                }
            } else {
                LOG.info(WxSubException.TEMPLATE_NULL.getMessage());
                result = Result.failed(WxSubException.TEMPLATE_NULL.getMessage());
            }
        } catch (WxErrorException e) {
            LOG.error(e.getError().getErrorCode() + e.getError().getErrorMsg());
            result = Result.failed(e.getError().getErrorCode() + e.getError().getErrorMsg());
        }
        return result;
    }

    @Override
    protected void save(MessageParam messageParam, Result result) {
        super.save(messageParam, result);
        SendWxRecordDO sendWxRecordDO = new SendWxRecordDO();
        WxSubParam wxSubParam = (WxSubParam) messageParam;
        Map<String, String> param = wxSubParam.getParam().build();
        sendWxRecordDO.setUserId(wxSubParam.getUserId());
        sendWxRecordDO.setChorgaId(wxSubParam.getChorgaId());
        sendWxRecordDO.setChorgaType(wxSubParam.getChorgaType());
        sendWxRecordDO.setTemplateTitle(wxSubParam.getWxSubTemplateEnum().getTitle());
        sendWxRecordDO.setMsgParam(JSONUtil.toJsonStr(param));
        //可能为空的参数
        sendWxRecordDO.setAppId(wxSubParam.getAppId());
        sendWxRecordDO.setReceiverOpenId(wxSubParam.getOpenId());
        TemplateInfo templateInfo = result.getTemplateInfo();
        if (!Objects.isNull(templateInfo)) {
            sendWxRecordDO.setContent(templateInfo.getContent());
            sendWxRecordDO.setTemplateType(templateInfo.getType());
        }
        if (result.getSuccess()){
            sendWxRecordDO.setReason("提交成功");
            sendWxRecordDO.setStatus(WxSubStatusEnum.SUCCESS.getCode());
        }else {
            sendWxRecordDO.setReason(result.getRemark());
            sendWxRecordDO.setStatus(WxSubStatusEnum.FAIL.getCode());
        }
        sendWxRecordMapper.insert(sendWxRecordDO);
    }

    /**
     * @param wxSubParam 请求参数
     * @param templateInfo 模板信息
     * @return WxMpSubscribeMessage
     */
    private WxMpSubscribeMessage buildWxMpSubscribeMessage(WxSubParam wxSubParam, TemplateInfo templateInfo) {
        WxMpSubscribeMessage message = new WxMpSubscribeMessage();
        message.setTitle(wxSubParam.getWxSubTemplateEnum().getTitle());
        message.setToUser(wxSubParam.getOpenId());
        message.setDataMap(wxSubParam.getParam().build());
        message.setTemplateId(templateInfo.getPriTmplId());
        message.setPage(wxSubParam.getParam().getPage());
        return message;
    }
}
