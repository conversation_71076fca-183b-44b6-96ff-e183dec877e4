package com.meerkat.message.mapper.query;

import java.io.Serializable;
import java.util.Set;

/**
 * <p>
 * smsTemplateParam, 短信模板参数字段表
 * </p>
 *
 * <AUTHOR>
 * @since 2022/07/27 17:42:49
 */
public class SmsTemplateParamDaoQuery implements Serializable{

    private static final long serialVersionUID = 1552228001611370496L;
    /**
     * 参数名
     */
    private String key;

    private Set<String> keys;

    /**
     * 获取参数名
     */
    public String getKey() {
        return key;
    }
    /**
     * 写入参数名
     */
    public void setKey(String key) {
        this.key = key;
    }

    public Set<String> getKeys() {
        return keys;
    }

    public void setKeys(Set<String> keys) {
        this.keys = keys;
    }
}