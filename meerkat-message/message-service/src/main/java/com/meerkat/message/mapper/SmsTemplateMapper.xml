<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.message.mapper.SmsTemplateMapper">

    <resultMap id="BaseResultMap" type="com.meerkat.message.mapper.dataobj.SmsTemplateDO">
        <result column="id" property="id"/>
        <result column="organization_id" property="organizationId"/>
        <result column="biz_code" property="bizCode"/>
        <result column="company_id" property="companyId"/>
        <result column="template_id" property="templateId"/>
        <result column="template_code" property="templateCode"/>
        <result column="title" property="title"/>
        <result column="type" property="type"/>
        <result column="content" property="content"/>
        <result column="param" property="param"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="reason" property="reason"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        organization_id,
        biz_code,
        company_id,
        template_id,
        template_code,
        title,
        type,
        content,
        param,
        remark,
        status,
        reason,
        is_deleted,
        gmt_created,
        gmt_modified
    </sql>

    <!--根据id查询单条-->
    <select id="selectByIds" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM tb_sms_template WHERE id = #{id} AND is_deleted = 0
    </select>

    <!--根据ids查询多条-->
    <select id="selectOneById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM tb_sms_template WHERE id IN (
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ) AND is_deleted = 0
    </select>

    <!--条件查询多条-->
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM tb_sms_template
        <where>
            is_deleted = 0
            <if test="null != organizationId">
                AND organization_id = #{organizationId}
            </if>
            <if test="null != bizCode">
                AND biz_code = #{bizCode}
            </if>
            <if test="null != companyId">
                AND company_id = #{companyId}
            </if>
            <if test="null != templateCode">
                AND template_code = #{templateCode}
            </if>
            <if test="null != bizCodes and bizCodes.size()>0">
                AND biz_code IN
                <foreach collection="bizCodes" item="bizCode" open="(" close=")" index="index" separator=",">
                    #{bizCode}
                </foreach>
            </if>
            <if test="null != templateCodes and templateCodes.size()>0">
                AND template_code IN
                <foreach collection="templateCodes" item="code" open="(" close=")" index="index" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test="null != organizationIds and organizationIds.size()>0">
                AND organization_id IN
                <foreach collection="organizationIds" item="orgaId" open="(" close=")" index="index" separator=",">
                    #{orgaId}
                </foreach>
            </if>
            <if test="null != status">
                AND `status`=#{status}
            </if>
        </where>
    </select>

    <!--新增一条-->
    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.meerkat.message.mapper.dataobj.SmsTemplateDO">
        INSERT INTO tb_sms_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != organizationId">
                organization_id,
            </if>
            <if test="null != bizCode and '' != bizCode">
                biz_code,
            </if>
            <if test="null != companyId">
                company_id,
            </if>
            <if test="null != templateId and '' != templateId">
                template_id,
            </if>
            <if test="null != templateCode and '' != templateCode">
                template_code,
            </if>
            <if test="null != title and '' != title">
                title,
            </if>
            <if test="null != type">
                type,
            </if>
            <if test="null != content and '' != content">
                content,
            </if>
            <if test="null != param and '' != param">
                param,
            </if>
            <if test="null != remark and '' != remark">
                remark,
            </if>
            <if test="null != status">
                status,
            </if>
            <if test="null != reason and '' != reason">
                reason,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != organizationId">
                #{organizationId},
            </if>
            <if test="null != bizCode and '' != bizCode">
                #{bizCode},
            </if>
            <if test="null != companyId">
                #{companyId},
            </if>
            <if test="null != templateId and '' != templateId">
                #{templateId},
            </if>
            <if test="null != templateCode and '' != templateCode">
                #{templateCode},
            </if>
            <if test="null != title and '' != title">
                #{title},
            </if>
            <if test="null != type">
                #{type},
            </if>
            <if test="null != content and '' != content">
                #{content},
            </if>
            <if test="null != param and '' != param">
                #{param},
            </if>
            <if test="null != remark and '' != remark">
                #{remark},
            </if>
            <if test="null != status">
                #{status},
            </if>
            <if test="null != reason and '' != reason">
                #{reason},
            </if>
        </trim>
    </insert>

    <!--根据主键id更新-->
    <update id="updateById">
        update tb_sms_template
        <set>
            <trim suffixOverrides=",">
            <if test="null != companyId">
                company_id = #{companyId},
            </if>
            <if test="null != templateId and '' != templateId">
                template_id = #{templateId},
            </if>
            <if test="null != type">
                type = #{type},
            </if>
            <if test="null != content and '' != content">
                content = #{content},
            </if>
            <if test="null != param and '' != param">
                param = #{param},
            </if>
            <if test="null != remark and '' != remark">
                remark = #{remark},
            </if>
            <if test="null != status">
                status = #{status},
            </if>
            <if test="null != reason and '' != reason">
                reason = #{reason},
            </if>
            <if test="null != isDeleted">
                is_deleted = #{isDeleted},
            </if>
            </trim>
        </set>
        <where>
            id = #{id}
        </where>
    </update>
</mapper>