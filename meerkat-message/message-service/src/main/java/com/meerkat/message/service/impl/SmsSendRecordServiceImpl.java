package com.meerkat.message.service.impl;

import cn.hutool.json.JSONUtil;
import com.meerkat.message.enums.SmsSendStatusEnum;
import com.meerkat.message.mapper.SmsSendRecordMapper;
import com.meerkat.message.mapper.dataobj.SmsSendRecordDO;
import com.meerkat.message.model.Response;
import com.meerkat.message.model.SmsSendRecord;
import com.meerkat.message.model.param.SmsSendRecordParam;
import com.meerkat.message.model.param.SmsSendRecordQuery;
import com.meerkat.message.service.SmsSendRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 短信记录处理实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2021/11/9 13:27
 */
@Service
public class SmsSendRecordServiceImpl implements SmsSendRecordService {

    /**
     * 拉取短信结果
     */
    private static final String SUCCESS = "SUCCESS";

    private final Logger logger = LoggerFactory.getLogger(SmsSendRecordServiceImpl.class);

    @Autowired
    private SmsSendRecordMapper smsSendRecordMapper;

    @Override
    public Integer insert(SmsSendRecord smsSendRecord) {
        SmsSendRecordDO sendRecordDO = new SmsSendRecordDO();
        BeanUtils.copyProperties(smsSendRecord, sendRecordDO);
        return smsSendRecordMapper.insert(sendRecordDO);
    }

    @Override
    public Response smsCallBackUpdate(List<SmsSendRecordParam> sendRecordReqList) {
        if (CollectionUtils.isEmpty(sendRecordReqList)){
            return Response.newSuccess();
        }

        for (SmsSendRecordParam sendRecordReq : sendRecordReqList) {
            SmsSendRecordDO recordDO = new SmsSendRecordDO();
            recordDO.setSerialNo(sendRecordReq.getSid());
            String errMsg = sendRecordReq.getErrMsg();
            errMsg = errMsg == null ? "" : errMsg;
            recordDO.setDescription( errMsg + sendRecordReq.getDescription());
            if (SUCCESS.equals(sendRecordReq.getReportStatus())) {
                recordDO.setStatus(SmsSendStatusEnum.SUCCESS.getCode());
                recordDO.setUserReceiveTime(sendRecordReq.getUserReceiveTime());
            } else {
                recordDO.setStatus(SmsSendStatusEnum.FAIL.getCode());
                logger.error("用户未收到短信：{}", JSONUtil.toJsonStr(sendRecordReq));
            }
            smsSendRecordMapper.update(recordDO);
        }
        return Response.newSuccess();
    }

    @Override
    public List<SmsSendRecord> listByStatus(SmsSendStatusEnum statusEnum) {
        SmsSendRecordQuery sendRecordQuery = new SmsSendRecordQuery();
        sendRecordQuery.setStatus(statusEnum.getCode());
        return dos2Model(smsSendRecordMapper.list(sendRecordQuery));
    }


    /**
     * 模型转换
     */
    private List<SmsSendRecord> dos2Model(List<SmsSendRecordDO> smsSendRecordDOS) {
        ArrayList<SmsSendRecord> smsSendRecords = new ArrayList<>();
        if (CollectionUtils.isEmpty(smsSendRecordDOS)) {
            return smsSendRecords;
        }
        smsSendRecordDOS.forEach(e -> {
            SmsSendRecord smsSendRecord = new SmsSendRecord();
            BeanUtils.copyProperties(e,smsSendRecord);
            smsSendRecords.add(smsSendRecord);
        });
        return smsSendRecords;
    }
}
