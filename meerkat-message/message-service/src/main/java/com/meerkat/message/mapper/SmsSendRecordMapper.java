package com.meerkat.message.mapper;

import com.meerkat.message.mapper.dataobj.SmsSendRecordDO;
import com.meerkat.message.model.param.SmsSendRecordQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SmsSendRecordMapper {

    Integer insert(SmsSendRecordDO smsSendRecordDO);

    void update(SmsSendRecordDO smsSendRecordDO);

    List<SmsSendRecordDO> list(SmsSendRecordQuery sendRecordQuery);
}