package com.meerkat.deprecated.service.common.validate;


import com.google.common.collect.Maps;
import com.meerkat.message.model.Response;
import com.meerkat.deprecated.service.client.util.MessageUtil;
import com.meerkat.deprecated.service.exceptions.GotoneExceptionEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by pantaoling on 2021/10/20.
 */
public class Validator {
    private Logger logger = LoggerFactory.getLogger(Validator.class);

    private static final Validator instance = new Validator();

    public static final Validator builder() {
        return instance;
    }

    public <T> Validator validateParam(T t, Response response) {
        if (!response.isSuccess()) {
            return this;
        }

//        ValidResult valid = ValidatorHelper.validator(t);
//        if (valid.hasError()) {
//            response.setError(valid.getErrorString());
//            return this;
//        }
        return this;
    }

    public Validator validateTemplate(String template, Response response) {
        if (!response.isSuccess()) {
            return this;
        }
        try {
            MessageUtil.getMessageByTemplateString(template, Maps.newHashMap());
        } catch (Exception e) {
            logger.error("template content is error, template:{}", template, e);
            response.setError(GotoneExceptionEnum.SMS_TEMPLATE_ERROR.getMessage());
            return this;
        }
        return this;
    }
}
