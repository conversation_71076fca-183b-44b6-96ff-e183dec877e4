package com.meerkat.deprecated.service.query;

import java.util.Date;

/**
 * Created by pantaoling on 2021/10/20.
 */
public class SmsSendQuery {

    private Integer id;
    private Integer status;
    private String mobile;
    private Integer hospitalId;
    private Date createTime;
    private String templateCode;
    private Integer maxSendCount;
    private Integer isIdDesc;
    private String businessCode;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getHospitalId() {
        return hospitalId;
    }

    public void setHospitalId(Integer hospitalId) {
        this.hospitalId = hospitalId;
    }

    public Integer getMaxSendCount() {
        return maxSendCount;
    }

    public void setMaxSendCount(Integer maxSendCount) {
        this.maxSendCount = maxSendCount;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getIsIdDesc() {
        return isIdDesc;
    }

    public void setIsIdDesc(Integer isIdDesc) {
        this.isIdDesc = isIdDesc;
    }

	public String getBusinessCode() {
		return businessCode;
	}

	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}
    
}
