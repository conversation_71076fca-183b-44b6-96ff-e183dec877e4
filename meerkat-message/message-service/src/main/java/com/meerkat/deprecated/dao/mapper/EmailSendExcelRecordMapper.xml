<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.sms.dao.mapper.EmailSendExcelRecordMapper">
    <resultMap type="com.meerkat.deprecated.dao.entity.EmailSendExcelRecordDO" id="emailSendExcelRecordResultMap">
        <result column="id" property="id" />
        <result column="hospital_id" property="hospitalId" />
        <result column="file_path" property="filePath" />
        <result column="statistics_date" property="statisticsDate" />
        <result column="content" property="content" />
        <result column="sender" property="sender" />
        <result column="receiver" property="receiver" />
        <result column="notify_type" property="notifyType" />
        <result column="subject" property="subject" />
        <result column="from_user_name" property="fromUserName" />
        <result column="hospital_name" property="hospitalName" />
        <result column="attach_type" property="attachType" />
        <result column="file_name" property="fileName" />
        <result column="send_status" property="sendStatus" />
        <result column="send_time" property="sendTime" />
        <result property="createTime" column="create_time"  />
        <result property="updateTime" column="update_time"  />
    </resultMap>

    <sql id="email_send_excel_record_columns">
    id,hospital_id,file_path,statistics_date,content,sender,receiver,notify_type,subject,from_user_name,
    hospital_name,file_name,attach_type,send_status,send_time,create_time,update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_email_send_excel_record(hospital_id,file_path,statistics_date,content,sender,receiver,notify_type,subject,from_user_name,hospital_name,file_name,attach_type,send_status,importance,create_time,update_time)
        VALUES (#{hospitalId}, #{filePath}, #{statisticsDate}, #{content},#{sender},#{receiver},#{notifyType},#{subject},#{fromUserName},#{hospitalName},#{fileName},#{attachType},#{sendStatus},#{importance},  now(), now())
    </insert>

    <select id="selectById" resultMap="emailSendExcelRecordResultMap" parameterType="java.lang.Integer">
        select  <include refid="email_send_excel_record_columns"/>
        from tb_email_send_excel_record where   id=#{id} and is_deleted=0
    </select>

    <select id="selectForPage" resultMap="emailSendExcelRecordResultMap">
        select
        <include refid="email_send_excel_record_columns"/>
        from tb_email_send_excel_record
        where 1 = 1
        <if test="!delete" >
            and is_deleted=0
        </if>
        <if test="recordId != null">
            and id = #{recordId}
        </if>
        <if test="hospitalName != null and hospitalName!= ''" >
            and hospital_name like CONCAT(#{hospitalName},'%')
        </if>
        <if test="receiver != null and receiver!= ''" >
            and receiver like  CONCAT('%',#{receiver},'%')
        </if>
        <if test="notifyType != null and notifyType!= ''" >
            and notify_type=#{notifyType}
        </if>
        <if test="sendTime != null">
            and send_time = #{sendTime}
        </if>
        <if test="sendTimeStart != null">
            and send_time <![CDATA[ >= ]]> #{sendTimeStart}
        </if>
        <if test="sendTimeEnd != null">
            and send_time <![CDATA[ <= ]]> #{sendTimeEnd}
        </if>
        <if test="sendStatus != null and sendStatus!= ''" >
            and send_status=#{sendStatus}
        </if>
        <if test="importance != null">
            and importance=#{importance}
        </if>
        <if test="isIdDesc !=null and isIdDesc ==1">
            order by id desc
        </if>
    </select>

    <select id="selectFailRecordForPage" resultMap="emailSendExcelRecordResultMap">
        select
        <include refid="email_send_excel_record_columns"/>
        from tb_email_send_excel_record
        where 1 = 1
        <if test="!delete" >
            and is_deleted=0
        </if>
        <if test="recordId != null">
            and id = #{recordId}
        </if>
        <if test="hospitalName != null and hospitalName!= ''" >
            and hospital_name like CONCAT(#{hospitalName},'%')
        </if>
        <if test="receiver != null and receiver!= ''" >
            and receiver like  CONCAT('%',#{receiver},'%')
        </if>
        <if test="notifyType != null and notifyType!= ''" >
            and notify_type=#{notifyType}
        </if>
        <if test="sendTime != null">
             and send_time = #{sendTime}
        </if>
        <if test="sendStatus != null and sendStatus!= ''" >
            and send_status=#{sendStatus}
        </if>
        <if test="importance != null">
            and importance=#{importance}
        </if>
        AND receiver IS NOT NULL
        AND sender IS NOT NULL
        AND subject IS NOT NULL
        and send_count <![CDATA[ <= ]]> #{sendCount}
        AND update_time <![CDATA[ <= ]]> #{failTime}
    </select>

    <update id="updateEmailSendResultById">
        update tb_email_send_excel_record
        set
        update_time=now()
        ,send_status=#{sendStatus}
        ,send_count=send_count + 1
        where  id=#{id}
    </update>
</mapper>