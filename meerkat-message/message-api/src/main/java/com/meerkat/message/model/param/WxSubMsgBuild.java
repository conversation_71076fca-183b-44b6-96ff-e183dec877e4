package com.meerkat.message.model.param;

import java.util.Map;

/**
 * <p>
 * 构建订阅消息请求参数
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/21 10:41
 */
public interface WxSubMsgBuild {

    /**
     * 构建请求参数
     * @return 构建结果
     */
    Map<String, String> build();

    /**
     * 获取page
     */
    String getPage();

    /**
     * 获取订阅通知模板id，模板id固定时推荐使用
     */
    default String getTemplateId(){
        return null;
    }
}
