package com.meerkat.message.model.param;

import com.meerkat.message.enums.MessageTypeEnum;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 飞书消息发送接口(发送到指定群，后续如果有更多业务场景将webhook提取到参数中来)
 * </p>
 *
 * <AUTHOR>
 * @since 2022/4/29 15:46
 */
public class FeishuParam extends MessageParam{

    /**
     * 需要传到的群机器人
     */
    @NotBlank
    private final String webHookUrl;

    /**
     * 消息类型
     */
    private final String contentType = "text";

    /**
     * 内容
     */
    private final FeishuContent feishuContent;

    /**
     * 文本内容，校验用
     */
    @NotBlank
    private final String text;

    /**
     * 发送@到人的消息，如果不存在则转为群内容通知
     * @param mobiles 手机号 传all@所有人
     * @param text 消息内容
     */
    public FeishuParam(String webHookUrl, String[] mobiles, String text) {
        //默认使用獴小妹平台，无实际效用
        super(1L, 2, MessageTypeEnum.FEISHU);
        FeishuContent feishuContentParam = new FeishuContent();
        feishuContentParam.setText(text);
        feishuContentParam.setMobile(mobiles);
        this.webHookUrl = webHookUrl;
        this.feishuContent = feishuContentParam;
        this.text = text;
    }

    /**
     * 发送群内通知
     * @param text 消息内容
     */
    public FeishuParam(String webHookUrl, String text) {
        //默认使用獴小妹平台，无实际效用
        super(1L, 2, MessageTypeEnum.FEISHU);
        FeishuContent feishuContentParam = new FeishuContent();
        feishuContentParam.setText(text);
        this.webHookUrl = webHookUrl;
        this.feishuContent = feishuContentParam;
        this.text = text;
    }

    public String getContentType() {
        return contentType;
    }

    public String getWebHookUrl() {
        return webHookUrl;
    }

    public FeishuContent getFeishuContent() {
        return feishuContent;
    }

    public String getText() {
        return text;
    }

    public static class FeishuContent {

        /**
         * 消息体
         */
        private String text;

        private String[] mobiles;

        public void setText(String text){
            this.text = text;
        }

        public String getText() {
            return text;
        }

        public String[] getMobiles() {
            return mobiles;
        }

        public void setMobile(String[] mobiles) {
            this.mobiles = mobiles;
        }
    }

    /**
     * 解析后的内容
     */
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
