package com.meerkat.message.model.param;

import com.meerkat.message.enums.MessageTypeEnum;
import com.meerkat.message.enums.WxSubTemplateEnum;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 订阅通知参数
 * </p>
 *
 * <AUTHOR>
 * @since 2022/2/8 14:42
 */
public class WxSubParam extends MessageParam{
    /**
     * 订阅消息模板枚举
     */
    @NotNull
    private final WxSubTemplateEnum wxSubTemplateEnum;

    /**
     * 接收方userId
     */
    @NotNull
    private final Long userId;

    /**
     * 是否为代运营的公众号
     */
    @NotNull
    private final Boolean trusted;

    /**
     * 请求参数
     */
    @NotNull
    private final WxSubMsgBuild param;

    /**
     *
     * @param chorgaId 站点id
     * @param chorgaType 站点类型
     * @param trusted  是否为代运营的公众号
     * @param wxSubTemplateEnum 模板枚举
     * @param userId 接收消息用户id
     * @param param 请求参数
     */
    public WxSubParam(Long chorgaId,
                      Integer chorgaType,
                      Boolean trusted,
                      WxSubTemplateEnum wxSubTemplateEnum,
                      Long userId,
                      WxSubMsgBuild param) {
        super(chorgaId, chorgaType, MessageTypeEnum.WX_SUB);
        this.wxSubTemplateEnum = wxSubTemplateEnum;
        this.userId = userId;
        this.param = param;
        this.trusted = trusted;
    }

    /**
     * 公众号应用id
     */
    private String appId;

    /**
     * 用户openId
     */
    private String openId;

    public WxSubTemplateEnum getWxSubTemplateEnum() {
        return wxSubTemplateEnum;
    }

    public Long getUserId() {
        return userId;
    }

    public WxSubMsgBuild getParam() {
        return param;
    }

    public Boolean getTrusted() {
        return trusted;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }
}
