package com.meerkat.message.model.param.wxMaSub;

import com.meerkat.message.model.param.WxSubMsgBuild;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 服务开始（一次性订阅通知）
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/19 下午6:11
 */
public class ServiceStartNoticeParam implements WxSubMsgBuild {

    private final static String CODE = "08_371FaAuxyeC1T0OuArhCTGJ9g7B87uwuNZ4jirdg";

    private final Map<String, String> build;

    private String page;

    /**
     *
     * @param thing 服务名称
     * @param date 服务时间
     */
    public ServiceStartNoticeParam(String thing, String date) {
        build = new HashMap<>();
        build.put("thing3", thing);
        build.put("thing7", date);
    }

    @Override
    public Map<String, String> build() {
        return build;
    }

    @Override
    public String getPage() {
        return page;
    }

    @Override
    public String getTemplateId() {
        return CODE;
    }
}
