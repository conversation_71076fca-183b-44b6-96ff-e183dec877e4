package com.meerkat.message.model.param.wxSub;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Maps;
import com.meerkat.common.utils.EnvironmentUtil;
import com.meerkat.message.model.param.WxSubMsgBuild;

import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 改期成功一次性订阅通知参数
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/21 10:47
 */
public class ChangeDateSuccessParam implements WxSubMsgBuild {

    /**
     * 体检人姓名
     */
    private final String customer;

    /**
     * 机构地址
     */
    private final String organizationAddress;

    /**
     * 机构名称
     */
    private final String organization;

    /**
     * 预约日期
     */
    private final Date examDate;

    /**
     * 公众号跳转页面
     */
    private String page;

    /**
     * @param customer            体检人姓名
     * @param organizationAddress 机构地址
     * @param organization        机构名称
     * @param examDate            预约日期
     * @param domain              域名信
     * @param secondDomain        二级域名
     */
    public ChangeDateSuccessParam(String customer, String organizationAddress, String organization, Date examDate, String orderNum, String domain, String secondDomain) {
        this.customer = customer;
        this.organizationAddress = organizationAddress;
        this.organization = organization;
        this.examDate = examDate;
        if (EnvironmentUtil.isTest()) {
            this.page = "https://" + domain + "." + secondDomain + ".menggejk.com/order/detail?orderNo=" + orderNum;
        }
        if (EnvironmentUtil.isProd()) {
            this.page = "https://" + domain + ".saas-h5.menggejk.com/order/detail?orderNo=" + orderNum;
        }
    }

    @Override
    public String getPage() {
        return this.page;
    }

    @Override
    public Map<String, String> build() {
        Map<String, String> map = Maps.newHashMap();
        //订单状态
        map.put("phrase1", "改期成功");
        map.put("time7", DateUtil.format(this.examDate, "yyyy年MM月dd日 HH:mm"));
        map.put("thing4", this.organization);
        map.put("thing6", this.organizationAddress);
        map.put("thing2", this.customer);
        return map;
    }

}
