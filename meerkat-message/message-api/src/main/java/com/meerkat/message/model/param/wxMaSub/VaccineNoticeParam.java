package com.meerkat.message.model.param.wxMaSub;

import com.meerkat.message.model.param.WxSubMsgBuild;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 小程序中的疫苗接种通知模板
 * </p>
 *
 * <AUTHOR>
 * @since 2022/8/30 下午9:58
 */
public class VaccineNoticeParam implements WxSubMsgBuild {

    /**
     * 四个参数的模板
     */
    private static final String templateId1 = "ZbWUoscDB8kE2VbpR0xykkiDutSnUX5cwiim2ZILKCs";

    /**
     * 三个参数的模板
     */
    private static final String templateId2 = "ZbWUoscDB8kE2VbpR0xyksP0wzX5ofLohm_IYOgqoh4";

    /**
     * 两个参数的模板
     */
    private static final String templateId3 = "ZbWUoscDB8kE2VbpR0xyktgtMlf9O8uPAhvxMApSzlw";


    /**
     * 实际的templateId
     */
    private final String templateId;

    private final Map<String, String> build;

    private String page;

    /**
     * @param customer    接种人
     * @param time        接种时间
     * @param vaccineType 疫苗种类
     * @param remark      温馨提示
     */
    public VaccineNoticeParam(String customer, String time, String vaccineType, String remark) {
        this.templateId = templateId1;
        build = new HashMap<>();
        build.put("name8", customer);
        build.put("thing9", vaccineType);
        build.put("date5", time);
        build.put("thing7", remark);
    }


    /**
     * @param customer    接种人
     * @param vaccineType 疫苗种类
     * @param remark      温馨提示
     */
    public VaccineNoticeParam(String customer, String vaccineType, String remark) {
        this.templateId = templateId2;
        build = new HashMap<>();
        build.put("name8", customer);
        build.put("thing9", vaccineType);
        build.put("thing7", remark);
    }

    /**
     * @param vaccineType 疫苗种类
     * @param remark      温馨提示
     */
    public VaccineNoticeParam(String vaccineType, String remark) {
        this.templateId = templateId3;
        build = new HashMap<>();
        build.put("thing9", vaccineType);
        build.put("thing7", remark);
    }

    @Override
    public String getTemplateId() {
        return templateId;
    }

    @Override
    public Map<String, String> build() {
        return build;
    }

    @Override
    public String getPage() {
        return page;
    }

    public void setPage(String page) {
        this.page = page;
    }
}
