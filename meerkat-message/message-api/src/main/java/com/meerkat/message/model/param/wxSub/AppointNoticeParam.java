package com.meerkat.message.model.param.wxSub;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Maps;
import com.meerkat.common.utils.EnvironmentUtil;
import com.meerkat.message.model.param.WxSubMsgBuild;

import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 待预约提醒一次性订阅通知参数
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/21 10:37
 */
public class AppointNoticeParam implements WxSubMsgBuild {
    /**
     * 套餐名称
     */
    private final String good;

    /**
     * 订单支付时间
     */
    private final Date createTime;

    /**
     * 提醒内容
     */
    private final String noticeContent;

    /**
     * 温馨提示
     */
    private final String desc;

    /**
     * 公众号跳转页面
     */
    private String page;

    /**
     * @param good       套餐名称
     * @param createTime 订单支付时间
     * @param orderNum   订单编号
     * @param domain     域名信息
     * @param secondDomain 二级域名
     */
    public AppointNoticeParam(String good, Date createTime, String orderNum, String domain, String secondDomain) {
        this.good = good;
        this.createTime = createTime;
        this.noticeContent = "您的订单已支付，可进行体检预约";
        this.desc = "医院每日体检名额有限，建议您提前预约";
        if (EnvironmentUtil.isTest()) {
            this.page = "https://" + domain + "." + secondDomain + ".menggejk.com/order/detail?orderNo=" + orderNum;
        }
        if (EnvironmentUtil.isProd()) {
            this.page = "https://" + domain + ".saas-h5.menggejk.com/order/detail?orderNo=" + orderNum;
        }
    }

    @Override
    public Map<String, String> build() {
        Map<String, String> map = Maps.newHashMap();
        map.put("thing5", this.good);
        map.put("date2", DateUtil.format(this.createTime, "yyyy年MM月dd日"));
        map.put("thing7", this.noticeContent);
        map.put("thing3", this.desc);
        return map;
    }

    @Override
    public String getPage() {
        return this.page;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public String getNoticeContent() {
        return noticeContent;
    }

    public String getGood() {
        return good;
    }

    public String getDesc() {
        return desc;
    }
}
