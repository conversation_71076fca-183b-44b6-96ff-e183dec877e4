package com.meerkat.message.service;

import com.meerkat.message.model.SmsQueryResponse;

import java.util.Date;

/**
 * <p>
 * 短信发送状态查询服务
 * </p>
 *
 * <AUTHOR>
 * @since 2022/2/10 18:01
 */
public interface SmsQueryService {

    /**
     * 查询短信状态
     * @param sendDate 发送日期
     * @param mobile 手机号
     * @param serialNo 流水号
     * @return Response
     */
    SmsQueryResponse queryStatus(Date sendDate, String mobile, String serialNo);
}
