package com.meerkat.message.exception;

import com.meerkat.common.api.BizCode;

/**
 * <p>
 * 短信模板异常
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/21 10:14
 */
public enum SmsTemplateException implements BizCode {

    /**
     * 未找到模板
     */
    TEMPLATE_NOT_FOUND("EX_SMS_TEMPLATE_01_001", "未找到模板"),
    /**
     * 参数不合法
     */
    ILLEGAL_ARGUMENT("EX_SMS_TEMPLATE_01_002", "参数不合法{0}"),
    /**
     * 模板解析失败
     */
    RESOLVER_FAIL("EX_SMS_TEMPLATE_01_003", "模板解析失败")
    ;

    SmsTemplateException(String code, String message) {
        this.code = code;
        this.message = message;
    }

    private String code;
    private String message;

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
