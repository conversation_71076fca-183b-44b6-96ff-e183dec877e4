package com.meerkat.message.model.param.wxSub;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Maps;
import com.meerkat.common.utils.EnvironmentUtil;
import com.meerkat.message.model.param.WxSubMsgBuild;

import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 预约成功一次性订阅通知参数
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/21 10:38
 */
public class AppointSuccessParam implements WxSubMsgBuild {

    /**
     * 预约日期
     */
    private final Date examDate;

    /**
     * 预约套餐
     */
    private final String good;

    /**
     * 机构地址
     */
    private final String organizationAddress;

    /**
     * 温馨提示
     */
    private final String desc;

    /**
     * 公众号跳转页面
     */
    private String page;

    /**
     * @param examDate            预约时间
     * @param good                套餐名称
     * @param organizationAddress 机构地址
     * @param orderNum            订单编号
     * @param domain              域名信息
     * @param secondDomain        二级域名
     */
    public AppointSuccessParam(Date examDate, String good, String organizationAddress, String orderNum, String domain, String secondDomain) {
        this.examDate = examDate;
        this.good = good;
        this.organizationAddress = organizationAddress;
        this.desc = "请按预约时间前往登记";
        if (EnvironmentUtil.isTest()) {
            this.page = "https://" + domain + "." + secondDomain + ".menggejk.com/order/detail?orderNo=" + orderNum;
        }
        if (EnvironmentUtil.isProd()) {
            this.page = "https://" + domain + ".saas-h5.menggejk.com/order/detail?orderNo=" + orderNum;
        }
    }

    @Override
    public String getPage() {
        return this.page;
    }

    @Override
    public Map<String, String> build() {
        Map<String, String> map = Maps.newHashMap();
        map.put("date2", DateUtil.format(this.examDate, "yyyy年MM月dd日"));
        map.put("thing5", this.good);
        map.put("thing3", this.organizationAddress);
        map.put("thing4", this.desc);
        return map;
    }

    public Date getExamDate() {
        return examDate;
    }

    public String getGood() {
        return good;
    }

    public String getOrganizationAddress() {
        return organizationAddress;
    }

    public String getDesc() {
        return desc;
    }
}
