package com.meerkat.message.model.param;

import cn.hutool.core.util.IdUtil;
import com.meerkat.message.enums.MessageTypeEnum;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 消息请求参数抽象类，它的子类是不同的消息参数，例如SmsParam,EmailParam,WxSubParam...
 * </p>
 *
 * <AUTHOR>
 * @since 2022/2/7 15:16
 */
public abstract class MessageParam {
    /**
     * 流水号
     */
    private final String messageNum = IdUtil.getSnowflake().nextIdStr();
    /**
     * 站点id
     */
    @NotNull
    private final Long chorgaId;
    /**
     * 站点类型 1、机构 2、渠道
     */
    @NotNull
    private final Integer chorgaType;
    /**
     * 消息类型
     */
    @NotNull
    private final MessageTypeEnum messageType;

    /**
     * @param chorgaId 机构/渠道id
     * @param chorgaType 机构/渠道类型
     * @param messageType 消息类型
     */
    public MessageParam(Long chorgaId, Integer chorgaType, MessageTypeEnum messageType) {
        this.chorgaId = chorgaId;
        this.chorgaType = chorgaType;
        this.messageType = messageType;
    }

    public Long getChorgaId() {
        return chorgaId;
    }

    public Integer getChorgaType() {
        return chorgaType;
    }

    public MessageTypeEnum getMessageType() {
        return messageType;
    }

    public String getMessageNum() {
        return messageNum;
    }
}
