package com.meerkat.deprecated.api.model.enums;

/**
 * Created by pantaoling on 2021/10/20.
 */
public enum InternalMsgStatusEnum {

    unread(0,"未读"),
    read(1,"已读"),

    ;

    private int value;
    private String comment;

    public int getValue() {
        return value;
    }

    public String getComment() {
        return comment;
    }

    InternalMsgStatusEnum(int val,String comment){
        value=val;
        this.comment=comment;
    }

    public static InternalMsgStatusEnum getByValue(Integer value){
        if(value == null){
            return null;
        }
        InternalMsgStatusEnum[] values = InternalMsgStatusEnum.values();
        for(InternalMsgStatusEnum internalMsgStatusEnum:values){
            if(internalMsgStatusEnum.getValue() == value.intValue()){
                return internalMsgStatusEnum;
            }
        }
        return null;
    }
}
