package com.meerkat.deprecated.api.model.beans;

import com.meerkat.deprecated.api.model.enums.SmsStatusEnum;

import java.util.Date;

/**
 * Created by pantaoling on 2021-10-20.
 */
@Deprecated
public class SmsRecord implements java.io.Serializable{

    private static final long serialVersionUID = -8019026185289597966L;
    private Integer id;
    private String mobile;
    private String content;
    private Date createTime;
    private Date sendTime;
    private Integer sendCount;
    private SmsStatusEnum smsStatusEnum;
    private String unifyBusinessTypeEnum;
    private Integer businessId;
    private Integer hospitalId;
    private Integer fromSite;
    private String operateName;

    public String getOperateName() {
        return operateName;
    }

    public void setOperateName(String operateName) {
        this.operateName = operateName;
    }

    public Integer getHospitalId() {
		return hospitalId;
	}

	public void setHospitalId(Integer hospitalId) {
		this.hospitalId = hospitalId;
	}


	public Integer getFromSite() {
		return fromSite;
	}

	public void setFromSite(Integer fromSite) {
		this.fromSite = fromSite;
	}

	public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public Integer getSendCount() {
        return sendCount;
    }

    public void setSendCount(Integer sendCount) {
        this.sendCount = sendCount;
    }

    public SmsStatusEnum getSmsStatusEnum() {
        return smsStatusEnum;
    }

    public void setSmsStatusEnum(SmsStatusEnum smsStatusEnum) {
        this.smsStatusEnum = smsStatusEnum;
    }

	public String getUnifyBusinessTypeEnum() {
		return unifyBusinessTypeEnum;
	}

	public void setUnifyBusinessTypeEnum(String unifyBusinessTypeEnum) {
		this.unifyBusinessTypeEnum = unifyBusinessTypeEnum;
	}
	
	public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("SmsRecord{");
        sb.append("id=").append(id);
        sb.append(", mobile='").append(mobile).append('\'');
        sb.append(", content='").append(content).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append(", sendTime=").append(sendTime);
        sb.append(", sendCount=").append(sendCount);
        sb.append(", smsStatusEnum=").append(smsStatusEnum);
        sb.append(", unifyBusinessTypeEnum=").append(unifyBusinessTypeEnum);
        sb.append(", businessId=").append(businessId);
        sb.append('}');
        return sb.toString();
    }
}
