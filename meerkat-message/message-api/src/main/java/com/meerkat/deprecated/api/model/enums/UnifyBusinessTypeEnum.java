package com.meerkat.deprecated.api.model.enums;

/**
 * <AUTHOR> 2018/3/8 19:33
 */
public enum UnifyBusinessTypeEnum {
	
    //订单支付
    CHANGE_EXAM_DATE_MSG("changeexamdate","修改体检日期"),
    ORDER_RECEIVED_MSG("orderreceived","订单支付成功"),
    ORDER_SUCCESS_MSG("ordersuccess","预约成功"),
    PROXY_ORDER("proxyOrder", "代预约下单"),
    CHANNEL_PROXY_ORDER("channelproxyordersuccess", "渠道代预约下单"),
    ORDER_SUCCESS_SELECT_EXAMDATE_BY_SELF_MSG("ordersuccess_selectExamdateBySelf","预约成功-自选日期"),
    REFUND_MSG("refund_msg","退款通知"),
    COMPANY_EXCLUSIVE_EXAM("companyexclusiveexam", "单位体检专属管家"),
    ORDER_APPIONTMENT_FAIL("order_appiontment_fail","订单预约失败"),
    ORDER_CHANGEDATE_FAIL("order_changedate_fail", "订单改期失败"),
    ORDER_REVOKE_FAIL("order_revoke_fail", "订单撤销失败"),



    //订单撤销
    ORDER_CLOSED_MSG("orderclosed","订单关闭"),
    REVOKE_ORDER_MSG("revokeorder","撤单"),
    
    //账号
    @Deprecated
    RESET_PWD_MSG("resetpwd","找回密码"),
    UPDATE_PWD_MSG("updatepwd","修改密码"),
    CRM_RESET_PWD_MSG("crmresetpwd","crm重置密码"),
    RESET_RANDOM_PWD_MSG("resetrandompwd","重置随机密码"),
    NO_LOGIN_REGISTER_MSG("nologinregister","免登录注册时发送初始密码"),
    VERIFICATION_CODE_MSG("verificationcode","验证码"),
    HEALTH_MANAGE_VERIFICATION_CODE_MSG("healthManageVerificationcode","健康管理验证码"),
    CRM_CUSTOM_MSG("opscustompwd","crm自定义短信"),
    
    //检前
    BEFORE_EXAM_SMS_MSG("beforeExamSms","检前短信"),
    
    //发卡
    DISTRIBUTE_CARD_MSG("distributecard","发卡短信"),
    CHANNEL_DISTRIBUTE_CARD_MSG("channeldistributecard", "渠道发卡短信"),
    //检后
    EXAM_REPORT_WARN_MSG("remindexamreport","体检报告提醒"),
    EXAM_REPORT_EXEPTION_ADVICE_MSG("remindexamreportabnormal","体检报告异常建议"),
    FOLLOW_UP_TASK_REMIND("followUpTaskRemind.vm", "随访健康任务提醒"),

    //ops操作
    OPS_RESET_PWD_MSG("opsresetpwd","ops重置密码"),
    OPS_ORDER_CUSTOM_MSG("opscustompwd","ops自定义短信"),
    
    //预留
    CHARGE_ORDER_MSG("chargeBack","退单"),
    SUCC_WITHDRAW_MSG("successWithdraw","提现成功"),
    FAIL_WITHDRAW_MSG("failWithdraw","提现失败"),
    AUTO_BOOKING_FAIL_MSG("autobookingfail","自动预约失败短信"),
    SMS_TEMPLATE_COED_ROOM_CHANGE_MSG("roomchange","roomchange"),
    SMS_TEMPLATE_COED_STATUS_CHANGE_MSG("examstatuschange","examstatuschange"),
    UNPAY_ALERT_MSG("unpay","订单改项后未支付"),

    DOCTOR_REPLY_MSG("doctorReplyMsg","医生回复问诊订单消息"),
    EXAM_REPORT_EXCEPTION_ADVICE("examReportExceptionAdvice","体检报告异常提醒"),
    CARD_EXPIRE_REMAIND("cardExpireRemind", "卡即将过期短信"),
    HIS_CUSTOM_MSG("hisCustomMsg", "his自定义短信"),
    // 健康管家
    INVITE_CONVERSATION("inviteConversation", "邀请对话"),
    HEALTH_ASSESSMENT("healthAssessment", "健康评估"),
    HEALTH_ASSESSMENT_VIRTUAL_SERVICE("healthAssessmentVirtualService.vm", "健康评估虚拟服务"),
    FOLLOW_UP("followUp", "康评随访"),
    WRITE_OFF("writeOff.vm","核销码短信通知" ),
    SEND_CARD_CODE("sendCardCode","卡号卡密短信通知"),
    SEND_ALLOCATION_CARD_CODE("sendAllcationCardCode","分配卡号卡密短信通知"),

    ;

    private String value;

    private String comment;

    public String getValue() {
        return value;
    }

    public String getComment() {
        return comment;
    }

    private UnifyBusinessTypeEnum(String value, String comment) {
        this.value = value;
        this.comment = comment;
    }

    public static UnifyBusinessTypeEnum getByValue(String value){
        if(value == null){
            return null;
        }
        UnifyBusinessTypeEnum[] values = UnifyBusinessTypeEnum.values();
        for(UnifyBusinessTypeEnum unifyBusinessTypeEnum :values){
            if(unifyBusinessTypeEnum.value.equals(value)){
                return unifyBusinessTypeEnum;
            }
        }
        return null;
    }

    public static UnifyBusinessTypeEnum getByComment(String comment){
        if(comment == null){
            return null;
        }
        UnifyBusinessTypeEnum[] comments = UnifyBusinessTypeEnum.values();
        for(UnifyBusinessTypeEnum unifyBusinessTypeEnum :comments){
            if(unifyBusinessTypeEnum.comment.equals(comment)){
                return unifyBusinessTypeEnum;
            }
        }
        return null;
    }
}
