package com.meerkat.deprecated.api.model;

import com.meerkat.deprecated.api.model.enums.UnifyBusinessTypeEnum;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.Map;

/**
 * Created by pantaoling on 2021-10-20.
 */
public class SmsWithContentReq implements java.io.Serializable{
	private static final long serialVersionUID = 5687634116780011355L;

	@NotBlank
    @Pattern(regexp = "^1[3456789]\\d{9}$")
    private String mobile;
    /**
     *  content = template + paramMap
     */
    @Length(max = 500)
    private String content;
    @Length(max = 500)
    private String template;
    private Map<String, Object> paramMap;

    private UnifyBusinessTypeEnum unifyBusinessTypeEnum;

    private Integer businessId;
    
    private Integer hopitalId;
    
    private Integer fromSite;

    /**
     * 签名
     * 不指定默认 【体检预约】
     */
    private String signature;
    private String outerId;
    private String operateName;

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public Map<String, Object> getParamMap() {
        return paramMap;
    }

    public void setParamMap(Map<String, Object> paramMap) {
        this.paramMap = paramMap;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public String getOperateName() {
        return operateName;
    }

    public void setOperateName(String operateName) {
        this.operateName = operateName;
    }

    public Integer getFromSite() {
		return fromSite;
	}

	public void setFromSite(Integer fromSite) {
		this.fromSite = fromSite;
	}

	public Integer getHopitalId() {
		return hopitalId;
	}

	public void setHopitalId(Integer hopitalId) {
		this.hopitalId = hopitalId;
	}


	public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public UnifyBusinessTypeEnum getUnifyBusinessTypeEnum() {
		return unifyBusinessTypeEnum;
	}

	public void setUnifyBusinessTypeEnum(UnifyBusinessTypeEnum unifyBusinessTypeEnum) {
		this.unifyBusinessTypeEnum = unifyBusinessTypeEnum;
	}

	public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }
}
