package com.meerkat.crm.orga.vo;

import com.meerkat.smart.organization.enums.OrgaAdvertisementJumpTypeEnum;
import com.meerkat.smart.organization.enums.OrgaAdvertisementShowTypeEnum;
import com.meerkat.smart.organization.enums.OrgaAdvertisementTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/5/31 13:48
 */
@ApiModel
public class OrgaAdvertisementVO {

    /**
     * id
     */
    private Long id;

    /**
     * 机构id
     */
    private Long organizationId;

    /**
     * @see com.meerkat.smart.organization.enums.OrgaAdvertisementTypeEnum
     * 广告位类型：1-banner广告位，2-固定位广告位，3-弹窗广告位
     */
    @ApiModelProperty("广告位类型：1-banner广告位，2-固定位广告位，3-弹窗广告位")
    private Integer adType;

    /**
     * 开始激活时间
     */
    @ApiModelProperty("开始激活时间")
    private LocalDateTime activeStartTime;

    /**
     * 结束激活时间（若为空，则表示永久激活）
     */
    @ApiModelProperty("结束激活时间（若为空，则表示永久激活）")
    private LocalDateTime activeEndTime;

    /**
     * @see OrgaAdvertisementJumpTypeEnum
     * 跳转类型
     */
    @ApiModelProperty("跳转类型：1-商品、2-链接")
    private Integer jumpType;

    /**
     * 跳转值
     */
    @ApiModelProperty("跳转值")
    private String jumpValue;

    /**
     * 展示位置
     */
    @ApiModelProperty("展示位置：adType为3-弹窗广告时使用")
    private Integer showPosition;

    /**
     * @see OrgaAdvertisementShowTypeEnum
     * 展示类型（ad_type为3时使用）
     */
    @ApiModelProperty("1-文本、2-图片链接")
    private Integer showType;

    /**
     * 展示内容（ad_type为3时使用）
     */
    @ApiModelProperty("文本或链接")
    private String showContext;

    /**
     * 状态
     */
    @ApiModelProperty("可用状态（1-可用、0-不可用）")
    private Integer status;

    @ApiModelProperty("排序")
    private Integer sequence;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public Integer getAdType() {
        return adType;
    }

    public void setAdType(Integer adType) {
        this.adType = adType;
    }

    public LocalDateTime getActiveStartTime() {
        return activeStartTime;
    }

    public void setActiveStartTime(LocalDateTime activeStartTime) {
        this.activeStartTime = activeStartTime;
    }

    public LocalDateTime getActiveEndTime() {
        return activeEndTime;
    }

    public void setActiveEndTime(LocalDateTime activeEndTime) {
        this.activeEndTime = activeEndTime;
    }

    public Integer getJumpType() {
        return jumpType;
    }

    public void setJumpType(Integer jumpType) {
        this.jumpType = jumpType;
    }

    public String getJumpValue() {
        return jumpValue;
    }

    public void setJumpValue(String jumpValue) {
        this.jumpValue = jumpValue;
    }

    public Integer getShowType() {
        return showType;
    }

    public void setShowType(Integer showType) {
        this.showType = showType;
    }

    public String getShowContext() {
        return showContext;
    }

    public void setShowContext(String showContext) {
        this.showContext = showContext;
    }

    public LocalDateTime getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(LocalDateTime gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getShowPosition() {
        return showPosition;
    }

    public void setShowPosition(Integer showPosition) {
        this.showPosition = showPosition;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Integer getRunStatus() {
        if (this.adType == OrgaAdvertisementTypeEnum.FIXED.getCode()) {
            //固定广告不限时间
            return null;
        }

        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(this.activeStartTime)) {
            //未开始
            return 0;
        }
        if (Objects.nonNull(this.activeEndTime) && now.isAfter(this.activeEndTime)) {
            //已过期
            return -1;
        }

        //进行中
        return 1;
    }
}
