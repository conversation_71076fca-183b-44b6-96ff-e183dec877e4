package com.meerkat.crm.order.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.meerkat.user.enums.MarriageStatusEnum;

/**
 * <AUTHOR>
 * @ClassName MarriageConverter
 * @description 婚姻转换器
 * @createTime 2022/4/23 23:09
 */
public class MarriageConverter implements Converter<Integer> {

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(value.equals(MarriageStatusEnum.UNMARRIED.getCode()) ? "未婚" : "已婚");
    }
}
