package com.meerkat.crm.image.vo;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @ClassName ImageStoreVO
 * @description
 * @createTime 2022/5/7 14:45
 */
public class ImageStoreVO {

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 图片类型: 1-项目类别
     */
    @ApiModelProperty("图片类型: 1-项目类别")
    private Integer type;

    /**
     * 图片url
     */
    @ApiModelProperty("图片url")
    private String url;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sequence;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }
}
