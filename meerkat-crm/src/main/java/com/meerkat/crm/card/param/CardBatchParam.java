package com.meerkat.crm.card.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.meerkat.common.db.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("CardBatchParam")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CardBatchParam {

    @ApiModelProperty("批次ID")
    private Long batchId;

    @ApiModelProperty("1机构 2渠道")
    private Long chorgaId;

    @ApiModelProperty("单位ID")
    private Long companyId;

    @ApiModelProperty("分页信息")
    private Page page;

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public Long getChorgaId() {
        return chorgaId;
    }

    public void setChorgaId(Long chorgaId) {
        this.chorgaId = chorgaId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
