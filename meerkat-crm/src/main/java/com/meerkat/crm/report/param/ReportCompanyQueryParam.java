package com.meerkat.crm.report.param;

import com.meerkat.common.db.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @ClassName ReportCompanyQueryParam
 * @description 单位查询
 * @createTime 2022/9/13 16:33
 */
@ApiModel("ReportCompanyQueryParam")
public class ReportCompanyQueryParam {

    /**
     * 单位名称
     */
    @ApiModelProperty("单位名称")
    private String name;

    @ApiModelProperty("分页信息")
    private Page page;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }
}
