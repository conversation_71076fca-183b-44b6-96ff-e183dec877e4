package com.meerkat.crm.report.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @ClassName ExamReportAgeSettingVO
 * @description
 * @createTime 2022/8/9 10:50
 */
@ApiModel("ExamReportAgeSettingVO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExamReportAgeSettingVO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("机构id")
    private Long organizationId;

    @ApiModelProperty("年龄下限")
    private Integer ageLowerLimit;

    @ApiModelProperty("年龄上限")
    private Integer ageUpperLimit;

    @ApiModelProperty("顺序")
    private Integer sequence;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public Integer getAgeLowerLimit() {
        return ageLowerLimit;
    }

    public void setAgeLowerLimit(Integer ageLowerLimit) {
        this.ageLowerLimit = ageLowerLimit;
    }

    public Integer getAgeUpperLimit() {
        return ageUpperLimit;
    }

    public void setAgeUpperLimit(Integer ageUpperLimit) {
        this.ageUpperLimit = ageUpperLimit;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }
}
