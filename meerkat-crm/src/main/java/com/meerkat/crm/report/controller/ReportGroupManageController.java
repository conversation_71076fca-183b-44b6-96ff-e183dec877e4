package com.meerkat.crm.report.controller;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.meerkat.common.api.CommonResult;
import com.meerkat.common.db.PageView;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.crm.report.param.ExamReportQueryParam;
import com.meerkat.crm.report.param.GenerateReportGroupParam;
import com.meerkat.crm.report.param.ReportCompanyQueryParam;
import com.meerkat.crm.report.param.ReportGroupQueryParam;
import com.meerkat.crm.report.vo.ExamReportCompanyVO;
import com.meerkat.crm.report.vo.ExamReportVO;
import com.meerkat.crm.report.vo.ReportGroupInfoVO;
import com.meerkat.crm.web.utils.LoginUtil;
import com.meerkat.report.model.ExamReport;
import com.meerkat.report.model.ExamReportCompany;
import com.meerkat.report.model.ExamReportGroupBase;
import com.meerkat.report.param.ExamReportQuery;
import com.meerkat.report.param.ReportCompanyQuery;
import com.meerkat.report.param.ReportGroupGenerateParam;
import com.meerkat.report.param.ReportGroupQuery;
import com.meerkat.report.service.ExamReportCompanyService;
import com.meerkat.report.service.ReportGroupReadService;
import com.meerkat.report.service.ReportGroupWriteService;
import com.meerkat.report.service.ReportReadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ReportGroupManageController
 * @description 团检报告生成&管理
 * @createTime 2022/8/10 11:56
 */
@Api(tags = {"团检报告生成&管理"})
@RestController
@RequestMapping("/reportGroup/manage")
public class ReportGroupManageController {

    @Resource
    private ExamReportCompanyService reportCompanyService;

    @Resource
    private ReportReadService reportReadService;

    @Resource
    private ReportGroupReadService reportGroupReadService;

    @Resource
    private ReportGroupWriteService reportGroupWriteService;

    @ApiOperation("单位下的个检报告列表")
    @PostMapping("/pageListReport")
    public CommonResult<PageView<ExamReportVO>> pageListReport(@RequestBody ExamReportQueryParam examReportQueryParam) {
        Long organizationId = LoginUtil.getCurrentOrganizationId();
        ExamReportQuery reportQuery = CopyUtil.copy(examReportQueryParam, ExamReportQuery.class);
        reportQuery.setOrganizationId(organizationId);
        PageView<ExamReport> pageView = reportReadService.pageListReport(reportQuery);
        List<ExamReportVO> examReportVOS = CopyUtil.copyList(pageView.getRecords(), ExamReportVO.class);
        return CommonResult.success(new PageView<>(examReportVOS, pageView.getPage()));
    }

    @ApiOperation("生成团检报告")
    @PostMapping("/generateReportGroup")
    public CommonResult generateReportGroup(@RequestBody GenerateReportGroupParam generateReportGroupParam) {
        Long organizationId = LoginUtil.getCurrentOrganizationId();
        // 操作人
        String operatorName = LoginUtil.getCurrentUser().getUser().getNickname();
        ReportGroupGenerateParam reportGroupGenerateParam = CopyUtil.copy(generateReportGroupParam, ReportGroupGenerateParam.class);
        reportGroupGenerateParam.setOrganizationId(organizationId);
        reportGroupGenerateParam.setOperator(StrUtil.isNotBlank(operatorName) ? operatorName : LoginUtil.getCurrentUser().getUser().getMobile());
        reportGroupWriteService.generateReportGroup(reportGroupGenerateParam);
        return CommonResult.success();
    }

    @ApiOperation("团检报告列表")
    @PostMapping("/pageListReportGroup")
    public CommonResult<PageView<ReportGroupInfoVO>> pageListReportGroup(@RequestBody ReportGroupQueryParam reportGroupQueryParam) {
        Long organizationId = LoginUtil.getCurrentOrganizationId();
        ReportGroupQuery reportGroupQuery = CopyUtil.copy(reportGroupQueryParam, ReportGroupQuery.class);
        reportGroupQuery.setOrganizationId(organizationId);
        PageView<ExamReportGroupBase> pageView = reportGroupReadService.pageListReportGroup(reportGroupQuery);
        List<ExamReportGroupBase> reportGroupBaseList = pageView.getRecords();
        List<ReportGroupInfoVO> reportGroupInfoVOS = Lists.newArrayList();
        for (ExamReportGroupBase examReportGroupBase : reportGroupBaseList) {
            ReportGroupInfoVO reportGroupInfoVO = CopyUtil.copy(examReportGroupBase, ReportGroupInfoVO.class);
            reportGroupInfoVO.setOperator(examReportGroupBase.getDoctor());
            reportGroupInfoVOS.add(reportGroupInfoVO);
        }
        return CommonResult.success(new PageView<>(reportGroupInfoVOS, pageView.getPage()));
    }


    @ApiOperation("分页查询单位列表")
    @PostMapping("/pageListCompany")
    public CommonResult<PageView<ExamReportCompanyVO>> pageListCompany(@RequestBody ReportCompanyQueryParam param) {
        Long organizationId = LoginUtil.getCurrentOrganizationId();
        ReportCompanyQuery reportCompanyQuery = CopyUtil.copy(param, ReportCompanyQuery.class);
        reportCompanyQuery.setOrganizationId(organizationId);
        PageView<ExamReportCompany> pageView = reportCompanyService.pageListCompany(reportCompanyQuery);
        List<ExamReportCompanyVO> examReportCompanyVOList = CopyUtil.copyList(pageView.getRecords(), ExamReportCompanyVO.class);
        return CommonResult.success(new PageView<>(examReportCompanyVOList, pageView.getPage()));
    }


}
