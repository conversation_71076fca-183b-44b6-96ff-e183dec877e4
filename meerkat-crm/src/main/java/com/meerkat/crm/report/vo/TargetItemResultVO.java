package com.meerkat.crm.report.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @ClassName TargetItemResultVO
 * @description 指标检查结果
 * @createTime 2022/8/15 11:26
 */
@ApiModel("指标检查结果")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TargetItemResultVO {

    @ApiModelProperty("指标项id")
    private Long targetItemId;

    @ApiModelProperty("指标结果文本")
    private String result;

    @ApiModelProperty("人数统计")
    private Integer count;

    @ApiModelProperty("人数占比")
    private Double percent;

    @ApiModelProperty("指标结果平均值")
    private Double avg;

    @ApiModelProperty("顺序")
    private Integer sequence;

    public Long getTargetItemId() {
        return targetItemId;
    }

    public void setTargetItemId(Long targetItemId) {
        this.targetItemId = targetItemId;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Double getPercent() {
        return percent;
    }

    public void setPercent(Double percent) {
        this.percent = percent;
    }

    public Double getAvg() {
        return avg;
    }

    public void setAvg(Double avg) {
        this.avg = avg;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }
}
