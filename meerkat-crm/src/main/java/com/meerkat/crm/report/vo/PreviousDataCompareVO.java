package com.meerkat.crm.report.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName PreviousDataCompareVO
 * @description 历次数据
 * @createTime 2022/8/15 14:50
 */
@ApiModel("PreviousDataCompareVO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PreviousDataCompareVO {

    @ApiModelProperty("本次报告总检人数")
    private Integer totalCount;

    @ApiModelProperty("本次报告开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("本次报告结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("上份报告总检人数")
    private Integer previousTotalCount;

    @ApiModelProperty("上份报告开始时间")
    private LocalDateTime previousStartTime;

    @ApiModelProperty("上份报告结束时间")
    private LocalDateTime previousEndTime;

    @ApiModelProperty("历次数据对比")
    private List<DiagnoseCompareVO> diagnoseCompareVOList;

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Integer getPreviousTotalCount() {
        return previousTotalCount;
    }

    public void setPreviousTotalCount(Integer previousTotalCount) {
        this.previousTotalCount = previousTotalCount;
    }

    public LocalDateTime getPreviousStartTime() {
        return previousStartTime;
    }

    public void setPreviousStartTime(LocalDateTime previousStartTime) {
        this.previousStartTime = previousStartTime;
    }

    public LocalDateTime getPreviousEndTime() {
        return previousEndTime;
    }

    public void setPreviousEndTime(LocalDateTime previousEndTime) {
        this.previousEndTime = previousEndTime;
    }

    public List<DiagnoseCompareVO> getDiagnoseCompareVOList() {
        return diagnoseCompareVOList;
    }

    public void setDiagnoseCompareVOList(List<DiagnoseCompareVO> diagnoseCompareVOList) {
        this.diagnoseCompareVOList = diagnoseCompareVOList;
    }
}
