package com.meerkat.crm.company.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName InnerCompanyVO.java
 * @Description TODO
 * @createTime 2022-01-13 17:16:00
 */

@ApiModel("innerCompanyVO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InnerCompanyVO implements Serializable {


    private static final long serialVersionUID = -6075075822162978347L;

    private Long id;

    @ApiModelProperty("机构id")
    private Long organizationId;

    @ApiModelProperty("内网单位名称")
    private String name;

    @ApiModelProperty("内网单位编码")
    private String code;

    @ApiModelProperty("内网单位关联的机构单位")
    private List<OrgaCompanyVO> orgaCompanyVOS;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<OrgaCompanyVO> getOrgaCompanyVOS() {
        return orgaCompanyVOS;
    }

    public void setOrgaCompanyVOS(List<OrgaCompanyVO> orgaCompanyVOS) {
        this.orgaCompanyVOS = orgaCompanyVOS;
    }
}
