package com.meerkat.crm.evaluation.disease.helper;

import com.meerkat.common.pinyin.PinYinUtil;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.crm.evaluation.disease.param.DiseaseParam;
import com.meerkat.crm.evaluation.disease.param.OperationDiseaseRiskParam;
import com.meerkat.crm.evaluation.disease.vo.DiseaseRiskVO;
import com.meerkat.crm.evaluation.riskoption.vo.RiskOptionVO;
import com.meerkat.evaluation.risk.model.disease.Disease;
import com.meerkat.evaluation.risk.model.disease.param.DiseaseQuery;
import com.meerkat.evaluation.risk.model.diseaserisk.DiseaseRisk;
import com.meerkat.evaluation.risk.model.riskoption.RiskOption;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName DiseaseControllerHelper
 * @description
 * @createTime 2022/3/24 23:03
 */
public class DiseaseControllerHelper {

    /**
     * 构建疾病查询对象
     * @param diseaseParam
     * @return 疾病查询对象
     */
    public static DiseaseQuery getDiseaseQueryByParam(DiseaseParam diseaseParam) {
        DiseaseQuery diseaseQuery = new DiseaseQuery();
        BeanUtils.copyProperties(diseaseParam, diseaseQuery);
        return diseaseQuery;
    }

    /**
     * 构建疾病对象
     * @param diseaseParam
     * @return 疾病对象
     */
    public static Disease getDiseaseByParam(DiseaseParam diseaseParam) {
        Disease disease = new Disease();
        if (Objects.nonNull(diseaseParam.getId())) {
            disease.setId(diseaseParam.getId());
        }
        disease.setName(diseaseParam.getDiseaseName());
        disease.setPinyin(PinYinUtil.getFirstSpell(diseaseParam.getDiseaseName()));
        return disease;
    }

    /**
     * model转vo
     * @param diseaseRisk model对象
     * @return
     */
    public static DiseaseRiskVO getDiseaseRiskVO(DiseaseRisk diseaseRisk) {
        DiseaseRiskVO diseaseRiskVO = CopyUtil.copy(diseaseRisk, DiseaseRiskVO.class);
        List<RiskOption> riskOptionList = diseaseRisk.getRiskOptionList();
        List<RiskOptionVO> riskOptionVOS = CopyUtil.copyList(riskOptionList, RiskOptionVO.class);
        diseaseRiskVO.setRiskOptionVOList(riskOptionVOS);
        return diseaseRiskVO;
    }

    /**
     * param对象转model对象
     * @param operationDiseaseRiskParam
     * @return
     */
    public static DiseaseRisk getDiseaseRiskByParam(OperationDiseaseRiskParam operationDiseaseRiskParam) {
        DiseaseRisk diseaseRisk = CopyUtil.copy(operationDiseaseRiskParam, DiseaseRisk.class);
        return diseaseRisk;
    }
}
