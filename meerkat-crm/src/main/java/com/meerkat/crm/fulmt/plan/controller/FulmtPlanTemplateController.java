package com.meerkat.crm.fulmt.plan.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meerkat.common.api.CommonResult;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.crm.fulmt.plan.dto.FulmtPlanTemplateDTO;
import com.meerkat.crm.fulmt.plan.dto.StatusDTO;
import com.meerkat.crm.fulmt.plan.param.PlanTemplateQueryParam;
import com.meerkat.crm.fulmt.plan.utils.MyPageView;
import com.meerkat.crm.fulmt.plan.utils.PageUtils;
import com.meerkat.crm.fulmt.plan.vo.FulmtPlanTemplateVO;
import com.meerkat.fulfillment.plan.exception.PlanBizException;
import com.meerkat.fulfillment.plan.model.FulmtPlanTemplate;
import com.meerkat.fulfillment.plan.model.PlanStatus;
import com.meerkat.fulfillment.plan.model.PlanTimeConfig;
import com.meerkat.fulfillment.plan.model.ReachContentConfig;
import com.meerkat.fulfillment.plan.model.enums.PlanTemplateQuery;
import com.meerkat.fulfillment.plan.service.FulmtPlanTemplateService;
import com.meerkat.user.model.User;
import com.meerkat.user.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

/**
 * <p>
 * 计划模版表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
@Api(tags = {"计划模板"})
@RestController
@RequestMapping(value = "/fulmt/plan/template", produces = MediaType.APPLICATION_JSON_VALUE)
public class FulmtPlanTemplateController {


    @Resource
    private FulmtPlanTemplateService fulmtPlanTemplateService;
    @Resource
    private UserService userService;

    @ApiOperation("创建计划模板")
    @PostMapping("/create")
    public CommonResult<Void> create(@RequestBody @Valid FulmtPlanTemplateDTO fulmtPlanTemplateDTO) {
        FulmtPlanTemplate fulmtPlanTemplate = CopyUtil.copy(fulmtPlanTemplateDTO, FulmtPlanTemplate.class);
        fulmtPlanTemplate.setPlanTimeConfig(JSONObject.toJSONString(fulmtPlanTemplateDTO.getTimeConfig()));
        fulmtPlanTemplate.setTimeConfig(CopyUtil.copy(fulmtPlanTemplateDTO.getTimeConfig(), PlanTimeConfig.class));
        fulmtPlanTemplate.setReachContent(JSONObject.toJSONString(fulmtPlanTemplateDTO.getReachContentConfig()));
        fulmtPlanTemplate.setReachContentConfig(CopyUtil.copy(fulmtPlanTemplateDTO.getReachContentConfig(), ReachContentConfig.class));
        boolean saveResult = fulmtPlanTemplateService.saveOne(fulmtPlanTemplate);
        if (!saveResult) {
            return CommonResult.failed(PlanBizException.TEMPLATE_CREATE_ERROR);
        }
        return CommonResult.success();
    }

    @ApiOperation("修改计划模板")
    @PostMapping("/update")
    public CommonResult<Void> update(@RequestBody @Valid FulmtPlanTemplateDTO fulmtPlanTemplateDTO) {
        FulmtPlanTemplate fulmtPlanTemplate = CopyUtil.copy(fulmtPlanTemplateDTO, FulmtPlanTemplate.class);
        fulmtPlanTemplate.setPlanTimeConfig(JSONObject.toJSONString(fulmtPlanTemplateDTO.getTimeConfig()));
        fulmtPlanTemplate.setTimeConfig(CopyUtil.copy(fulmtPlanTemplateDTO.getTimeConfig(), PlanTimeConfig.class));
        fulmtPlanTemplate.setReachContent(JSONObject.toJSONString(fulmtPlanTemplateDTO.getReachContentConfig()));
        fulmtPlanTemplate.setReachContentConfig(CopyUtil.copy(fulmtPlanTemplateDTO.getReachContentConfig(), ReachContentConfig.class));
        boolean updateResult = fulmtPlanTemplateService.updateOne(fulmtPlanTemplate);
        if (!updateResult) {
            return CommonResult.failed(PlanBizException.TEMPLATE_CREATE_ERROR);
        }
        return CommonResult.success();
    }

    @ApiOperation("开启/关闭计划模板")
    @PostMapping("/updateStatus")
    public CommonResult<Void> updateStatus(@RequestBody StatusDTO statusDTO) {
        PlanStatus status = CopyUtil.copy(statusDTO, PlanStatus.class);
        boolean updateResult = fulmtPlanTemplateService.updateStatus(status);
        if (!updateResult) {
            return CommonResult.failed(PlanBizException.TEMPLATE_CREATE_ERROR);
        }
        return CommonResult.success();
    }

    @ApiOperation("计划模板列表")
    @PostMapping("/list")
    public CommonResult<MyPageView<FulmtPlanTemplateVO>> list(@RequestBody PlanTemplateQueryParam queryParam) {
        IPage<FulmtPlanTemplate> iPage = new Page<>(queryParam.getPageNum(), queryParam.getPageSize());

        PlanTemplateQuery planTemplateQuery = CopyUtil.copy(queryParam, PlanTemplateQuery.class);
        IPage<FulmtPlanTemplate> page = fulmtPlanTemplateService.listOfPages(planTemplateQuery, iPage);
        MyPageView<FulmtPlanTemplateVO> pageView = PageUtils.toMyPageView(page, FulmtPlanTemplateVO.class);
        makeUserName(pageView);
        return CommonResult.success(pageView);
    }

    private void makeUserName(MyPageView<FulmtPlanTemplateVO> pageView) {
        Set<Long> userIdList = new HashSet<>();
        List<FulmtPlanTemplateVO> records = pageView.getRecords();
        for (FulmtPlanTemplateVO record : records) {
            userIdList.add(record.getInterveneId());
            userIdList.add(record.getInterveneHeadId());
        }
        List<User> userList = userService.listByIds(new ArrayList<>(userIdList));
        if (userList == null) {
            return;
        }
        HashMap<Long, User> userMap = new HashMap<>();
        for (User user : userList) {
            userMap.put(user.getId(), user);
        }
        for (FulmtPlanTemplateVO record : records) {
            User interveneUser = userMap.get(record.getInterveneId());
            if (interveneUser != null) {
                record.setInterveneName(interveneUser.getNickname());
            }
            User interveneDeadUser = userMap.get(record.getInterveneHeadId());
            if (interveneDeadUser != null) {
                record.setInterveneHeadName(interveneDeadUser.getNickname());
            }
        }
    }

    @ApiOperation("搜索计划模板")
    @GetMapping("/selectByName")
    public CommonResult<List<FulmtPlanTemplateVO>> selectByName(String name) {
        List<FulmtPlanTemplate> list = fulmtPlanTemplateService.selectByName(name);
        List<FulmtPlanTemplateVO> fulmtPlanTemplateVOS = CopyUtil.copyList(list, FulmtPlanTemplateVO.class);
        return CommonResult.success(fulmtPlanTemplateVOS);
    }

}
