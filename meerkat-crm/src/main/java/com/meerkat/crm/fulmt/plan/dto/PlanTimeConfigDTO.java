package com.meerkat.crm.fulmt.plan.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2022/04/20 14:04
 */
@ApiModel
public class PlanTimeConfigDTO {

    /**
     * 第几天开始
     */
    @ApiModelProperty("第几天开始")
    private Integer startDay;

    /**
     * 第几天结束
     */
    @ApiModelProperty("第几天结束")
    private Integer endDay;

    /**
     * 周期间隔天数
     */
    @ApiModelProperty("周期间隔天数")
    private Integer intervalDay;

    /**
     * 时间(时分秒)
     */
    @ApiModelProperty("时间(时分秒)")
    private String time;

    public Integer getStartDay() {
        return startDay;
    }

    public void setStartDay(Integer startDay) {
        this.startDay = startDay;
    }

    public Integer getEndDay() {
        return endDay;
    }

    public void setEndDay(Integer endDay) {
        this.endDay = endDay;
    }

    public Integer getIntervalDay() {
        return intervalDay;
    }

    public void setIntervalDay(Integer intervalDay) {
        this.intervalDay = intervalDay;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    @Override
    public String toString() {
        return "PlanTimeConfig{" + "startDay=" + startDay + ", endDay=" + endDay + ", intervalDay=" + intervalDay + ", time='" + time + '\'' + '}';
    }
}
