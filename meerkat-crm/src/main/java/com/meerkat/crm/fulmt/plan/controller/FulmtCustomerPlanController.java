package com.meerkat.crm.fulmt.plan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meerkat.common.api.CommonResult;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.crm.fulmt.plan.dto.CustomerPlanDTO;
import com.meerkat.crm.fulmt.plan.dto.StatusDTO;
import com.meerkat.crm.fulmt.plan.query.CustomerPlanQuery;
import com.meerkat.crm.fulmt.plan.utils.MyPageView;
import com.meerkat.crm.fulmt.plan.utils.PageUtils;
import com.meerkat.crm.fulmt.plan.vo.FulmtCustomerPlanVO;
import com.meerkat.crm.web.utils.LoginUtil;
import com.meerkat.fulfillment.plan.exception.PlanBizException;
import com.meerkat.fulfillment.plan.model.FulmtCustomerPlan;
import com.meerkat.fulfillment.plan.model.FulmtCustomerPlanQuery;
import com.meerkat.fulfillment.plan.model.PlanStatus;
import com.meerkat.fulfillment.plan.model.ReachContentConfig;
import com.meerkat.fulfillment.plan.service.FulmtCustomerPlanService;
import com.meerkat.user.model.User;
import com.meerkat.user.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/04/21 11:01
 */
@Api(tags = "具体任务")
@RestController
@RequestMapping(value = "/fulmt/customer/plan", produces = MediaType.APPLICATION_JSON_VALUE)
public class FulmtCustomerPlanController {

    @Resource
    private FulmtCustomerPlanService fulmtCustomerPlanService;
    @Resource
    private UserService userService;

    @ApiOperation("创建客户具体任务")
    @PostMapping("/create")
    public CommonResult<Boolean> create(@RequestBody @Valid CustomerPlanDTO customerPlanDTO) {
        FulmtCustomerPlan fulmtCustomerPlan = CopyUtil.copy(customerPlanDTO, FulmtCustomerPlan.class);
        fulmtCustomerPlan.setReachContentConfig(CopyUtil.copy(customerPlanDTO.getReachContentConfig(), ReachContentConfig.class));
        fulmtCustomerPlan.setOrganizationId(LoginUtil.getCurrentOrganizationId());
        boolean result = fulmtCustomerPlanService.create(fulmtCustomerPlan);
        return CommonResult.success(result);
    }

    @ApiOperation("修改任务")
    @PostMapping("/update")
    public CommonResult<Boolean> update(@RequestBody @Valid CustomerPlanDTO customerPlanDTO) {
        FulmtCustomerPlan fulmtCustomerPlan = CopyUtil.copy(customerPlanDTO, FulmtCustomerPlan.class);
        fulmtCustomerPlan.setReachContentConfig(CopyUtil.copy(customerPlanDTO.getReachContentConfig(), ReachContentConfig.class));
        fulmtCustomerPlan.setOrganizationId(LoginUtil.getCurrentOrganizationId());
        boolean updatePlan = fulmtCustomerPlanService.updatePlan(fulmtCustomerPlan);
        if (updatePlan) {
            return CommonResult.success(updatePlan);
        }
        return CommonResult.failed(PlanBizException.PLAN_UPDATE_ERROR);
    }

    @ApiOperation("停用/启用任务")
    @PostMapping("/updateStatus")
    public CommonResult<Boolean> updateStatus(@RequestBody StatusDTO statusDTO) {
        PlanStatus status = CopyUtil.copy(statusDTO, PlanStatus.class);
        boolean updateStatus = fulmtCustomerPlanService.updateStatus(status);
        if (updateStatus) {
            return CommonResult.success(updateStatus);
        }
        return CommonResult.failed(PlanBizException.PLAN_UPDATE_ERROR);
    }

    @ApiOperation("审核任务")
    @PostMapping("/check")
    public CommonResult<Boolean> check(@RequestBody StatusDTO statusDTO) {
        PlanStatus status = CopyUtil.copy(statusDTO, PlanStatus.class);
        boolean updateStatus = fulmtCustomerPlanService.check(status);
        if (updateStatus) {
            return CommonResult.success(updateStatus);
        }
        return CommonResult.failed(PlanBizException.PLAN_UPDATE_ERROR);
    }

    @ApiOperation("执行任务")
    @PostMapping("/execute")
    public CommonResult<Boolean> execute(@RequestBody StatusDTO statusDTO) {
        PlanStatus status = CopyUtil.copy(statusDTO, PlanStatus.class);
        boolean updateStatus = fulmtCustomerPlanService.execute(status);
        if (updateStatus) {
            return CommonResult.success(updateStatus);
        }
        return CommonResult.failed(PlanBizException.PLAN_UPDATE_ERROR);
    }

    @ApiOperation("客户具体任务列表")
    @PostMapping("/pageList")
    public CommonResult<MyPageView<FulmtCustomerPlanVO>> pageList(@RequestBody CustomerPlanQuery customerPlanQuery) {
        FulmtCustomerPlanQuery fulmtCustomerPlanQuery = CopyUtil.copy(customerPlanQuery, FulmtCustomerPlanQuery.class);
        fulmtCustomerPlanQuery.setOrganizationId(LoginUtil.getCurrentOrganizationId());
        IPage<FulmtCustomerPlan> iPage = new Page<>(customerPlanQuery.getPageNum(), customerPlanQuery.getPageSize());
        IPage<FulmtCustomerPlan> page = fulmtCustomerPlanService.pageList(iPage, fulmtCustomerPlanQuery);
        MyPageView<FulmtCustomerPlanVO> myPageView = PageUtils.toMyPageView(page, FulmtCustomerPlanVO.class);
        makeUserName(myPageView.getRecords());
        return CommonResult.success(myPageView);
    }

    @ApiOperation("客户具体任务列表不分页")
    @PostMapping("/listByQuery")
    public CommonResult<List<FulmtCustomerPlanVO>> listByQuery(@RequestBody CustomerPlanQuery customerPlanQuery) {
        FulmtCustomerPlanQuery fulmtCustomerPlanQuery = CopyUtil.copy(customerPlanQuery, FulmtCustomerPlanQuery.class);
        fulmtCustomerPlanQuery.setOrganizationId(LoginUtil.getCurrentOrganizationId());
        List<FulmtCustomerPlan> list = fulmtCustomerPlanService.listByQuery(fulmtCustomerPlanQuery);
        List<FulmtCustomerPlanVO> copyList = CopyUtil.copyList(list, FulmtCustomerPlanVO.class);
        makeUserName(copyList);
        return CommonResult.success(copyList);
    }

    @ApiOperation("客户停用启用")
    @PostMapping("/remindPlan")
    public CommonResult<List<FulmtCustomerPlanVO>> remindPlan(@RequestBody CustomerPlanQuery customerPlanQuery) {
        FulmtCustomerPlanQuery fulmtCustomerPlanQuery = CopyUtil.copy(customerPlanQuery, FulmtCustomerPlanQuery.class);
        fulmtCustomerPlanQuery.setOrganizationId(LoginUtil.getCurrentOrganizationId());
        List<FulmtCustomerPlan> list = fulmtCustomerPlanService.remindPlan(fulmtCustomerPlanQuery);
        List<FulmtCustomerPlanVO> copyList = CopyUtil.copyList(list, FulmtCustomerPlanVO.class);
        makeUserName(copyList);
        return CommonResult.success(copyList);
    }

    private void makeUserName(List<FulmtCustomerPlanVO> records) {
        Set<Long> userIdList = new HashSet<>();
        for (FulmtCustomerPlanVO record : records) {
            userIdList.add(record.getInterveneId());
            userIdList.add(record.getInterveneHeadId());
        }
        List<User> userList = userService.listByIds(new ArrayList<>(userIdList));
        if (userList == null) {
            return;
        }
        HashMap<Long, User> userMap = new HashMap<>();
        for (User user : userList) {
            userMap.put(user.getId(), user);
        }
        for (FulmtCustomerPlanVO record : records) {
            User interveneUser = userMap.get(record.getInterveneId());
            if (interveneUser != null) {
                record.setInterveneName(interveneUser.getNickname());
            }
            User interveneDeadUser = userMap.get(record.getInterveneHeadId());
            if (interveneDeadUser != null) {
                record.setInterveneHeadName(interveneDeadUser.getNickname());
            }
        }
    }
}
