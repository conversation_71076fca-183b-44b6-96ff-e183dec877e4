package com.meerkat.crm.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date ：Created in 2021/12/22 6:02 下午
 * @description：履约人导入记录映射VO层对象
 */
@ApiModel("AcceptorExportVO")
public class AcceptorExportVO {

    /**
     * id
     */
    @NotNull(message = "导入失败数据id不能为空")
    @ApiModelProperty("导入失败id")
    private Long id;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String mobile;

    /**
     * 导入联系方式
     */
    @ApiModelProperty("导入联系方式")
    private String initialMobile;

    /**
     * 证件号码
     */
    @ApiModelProperty("证件号码")
    private String credentialNo;

    /**
     * 证件类型，0：身份证、1：护照
     */
    @ApiModelProperty("证件类型，0：身份证、1：护照")
    private Integer credentialType;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 地址id，对应tb_address
     */
    @ApiModelProperty("地址id")
    private Integer addressId;

    /**
     * 地址
     */
    @ApiModelProperty("详细地址")
    private String address;

    /**
     * 出生年份
     */
    @ApiModelProperty("出生年份")
    private String birthYear;

    /**
     * 出生月日
     */
    @ApiModelProperty("出生月日")
    private String dateBirth;

    /**
     * 婚否，0：未婚、1：已婚
     */
    @ApiModelProperty("婚否，0：未婚、1：已婚")
    private Integer marriageStatus;

    /**
     * 所在部门
     */
    @ApiModelProperty("所在部门")
    private String department;

    /**
     * 员工号
     */
    @ApiModelProperty("员工号")
    private String employeeId;

    /**
     * 职级
     */
    @ApiModelProperty("职级")
    private String position;

    /**
     * 退休，0:未退休 1:退休
     */
    @ApiModelProperty("退休，0:未退休 1:退休")
    private Integer isRetire;

    /**
     * 保健级别
     */
    @ApiModelProperty("保健级别")
    private String healthLevel;

    /**
     * 保健号
     */
    @ApiModelProperty("保健号")
    private String healthNum;

    /**
     * 社保号
     */
    @ApiModelProperty("社保号")
    private String socialSecurity;

    /**
     * 操作员
     */
    @ApiModelProperty("操作员")
    private Long operatorId;

    /**
     * 操作员姓名
     */
    @ApiModelProperty("操作员姓名")
    private String operatorName;

    /**
     * 单位id
     */
    @ApiModelProperty("单位id")
    private Long companyId;

    /**
     * 单位名称
     */
    @ApiModelProperty("单位名称")
    private String companyName;

    /**
     * 站点id，分为机构或渠道
     */
    @ApiModelProperty("站点id，分为机构或渠道")
    private Long siteId;

    /**
     * 站点类型，1：机构、2：渠道
     */
    @ApiModelProperty("站点类型，1：机构、2：渠道")
    private Integer siteType;


    /**
     * 拼音
     */
    @ApiModelProperty("拼音")
    private String pinyin;

    /**
     * 性别，0：女、1：男
     */
    @ApiModelProperty("性别，0：女、1：男")
    private Integer gender;

    /**
     * vip级别，0：非vip、1：vip
     */
    @ApiModelProperty("vip级别，0：非vip、1：vip")
    private Integer vipLevel;

    /**
     * 分组（分组套餐）
     */
    @ApiModelProperty("分组（分组套餐）")
    private String groupGoods;

    /**
     * 民族 关联tb_nation表
     */
    @ApiModelProperty("民族")
    private String nationality;

    /**
     * 失败原因
     */
    @ApiModelProperty("失败原因")
    private String message;

    /**
     * 错误代码，0：其他、1：数据重复
     */
    @ApiModelProperty("错误代码，0：其他、1：数据重复")
    private Integer failCode;

    /**
     * 操作行为，1：再次导入、2：数据覆盖
     */
    @ApiModelProperty("操作行为，1：再次导入、2：数据覆盖")
    private Integer action;

    /**
     * 操作状态，0：未操作、1：操作完成、2：操作失败
     */
    @ApiModelProperty("操作状态，0：未操作、1：操作完成、2：操作失败")
    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getInitialMobile() {
        return initialMobile;
    }

    public void setInitialMobile(String initialMobile) {
        this.initialMobile = initialMobile;
    }

    public String getCredentialNo() {
        return credentialNo;
    }

    public void setCredentialNo(String credentialNo) {
        this.credentialNo = credentialNo;
    }

    public Integer getCredentialType() {
        return credentialType;
    }

    public void setCredentialType(Integer credentialType) {
        this.credentialType = credentialType;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getAddressId() {
        return addressId;
    }

    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBirthYear() {
        return birthYear;
    }

    public void setBirthYear(String birthYear) {
        this.birthYear = birthYear;
    }

    public String getDateBirth() {
        return dateBirth;
    }

    public void setDateBirth(String dateBirth) {
        this.dateBirth = dateBirth;
    }

    public Integer getMarriageStatus() {
        return marriageStatus;
    }

    public void setMarriageStatus(Integer marriageStatus) {
        this.marriageStatus = marriageStatus;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Integer getIsRetire() {
        return isRetire;
    }

    public void setIsRetire(Integer isRetire) {
        this.isRetire = isRetire;
    }

    public String getHealthLevel() {
        return healthLevel;
    }

    public void setHealthLevel(String healthLevel) {
        this.healthLevel = healthLevel;
    }

    public String getHealthNum() {
        return healthNum;
    }

    public void setHealthNum(String healthNum) {
        this.healthNum = healthNum;
    }

    public String getSocialSecurity() {
        return socialSecurity;
    }

    public void setSocialSecurity(String socialSecurity) {
        this.socialSecurity = socialSecurity;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public Integer getSiteType() {
        return siteType;
    }

    public void setSiteType(Integer siteType) {
        this.siteType = siteType;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(Integer vipLevel) {
        this.vipLevel = vipLevel;
    }

    public String getGroupGoods() {
        return groupGoods;
    }

    public void setGroupGoods(String groupGoods) {
        this.groupGoods = groupGoods;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getFailCode() {
        return failCode;
    }

    public void setFailCode(Integer failCode) {
        this.failCode = failCode;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
