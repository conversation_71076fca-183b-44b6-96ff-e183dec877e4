package com.meerkat.crm.user.param;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <p>
 * user新增入参
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/6 15:04
 */
@ApiModel(description = "用户参数")
public class UserParam {
    /**
     * 用户id
     */
    @ApiModelProperty("用户id,修改时需要")
    private Long userId;
    /**
     * 登陆账号
     */
    @ApiModelProperty("登陆账号")
    private String username;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号", required = true)
    private String mobile;
    /**
     * 密码
     */
    @ApiModelProperty("密码")
    private String password;
    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String nickname;
    /**
     * 角色id列表
     */
    @ApiModelProperty("角色id列表")
    private List<Long> roleIds;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public List<Long> getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(List<Long> roleIds) {
        this.roleIds = roleIds;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}
