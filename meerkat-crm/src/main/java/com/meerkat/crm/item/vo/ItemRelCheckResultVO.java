package com.meerkat.crm.item.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ItemRelCheckReslutVO.java
 * @Description TODO
 * @createTime 2022-03-09 21:45:00
 */
@ApiModel("ItemRelCheckResultVO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ItemRelCheckResultVO {

    @ApiModelProperty("校验项目ID")
    private ItemVO itemVO;

    @ApiModelProperty("项目关系")
    private Integer itemRel;

    @ApiModelProperty("关系项目")
    private List<ItemVO> itemVOS;

    @ApiModelProperty("1:父子项-父项 2:父子项-子项 3:组合项-大项 4:组合项-子项")
    private Integer itemRelationRole;

    public ItemVO getItemVO() {
        return itemVO;
    }

    public void setItemVO(ItemVO itemVO) {
        this.itemVO = itemVO;
    }

    public Integer getItemRel() {
        return itemRel;
    }

    public void setItemRel(Integer itemRel) {
        this.itemRel = itemRel;
    }

    public List<ItemVO> getItemVOS() {
        return itemVOS;
    }

    public void setItemVOS(List<ItemVO> itemVOS) {
        this.itemVOS = itemVOS;
    }

    public Integer getItemRelationRole() {
        return itemRelationRole;
    }

    public void setItemRelationRole(Integer itemRelationRole) {
        this.itemRelationRole = itemRelationRole;
    }
}
