package com.meerkat.crm.web.config;

import com.meerkat.base.service.OperatorInfoService;
import com.meerkat.common.enums.SystemEnum;
import com.meerkat.crm.web.utils.LoginUtil;
import com.meerkat.user.model.User;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/9/8 10:56
 */
@Service
public class CrmOperatorInfoService implements OperatorInfoService<User> {
    @Override
    public User getOpt() {
        return LoginUtil.getCurrentUser().getUser();
    }

    @Override
    public Long getUserId() {
        User opt = getOpt();
        if (Objects.isNull(opt)) {
            return null;
        }
        return opt.getId();
    }

    @Override
    public SystemEnum getOptSource() {
        return SystemEnum.CRM;
    }
}
