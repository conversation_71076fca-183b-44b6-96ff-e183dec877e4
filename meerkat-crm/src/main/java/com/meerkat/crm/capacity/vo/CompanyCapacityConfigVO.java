package com.meerkat.crm.capacity.vo;

import com.meerkat.crm.capacity.param.CompanyPeriodMapSettingVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @program meerkat-origin
 * @description: 单位排期配置VO
 * @author: fandongdong
 * @create: 2022/07/22 20:54
 */
@ApiModel("CompanyCapacityConfigVO")
public class CompanyCapacityConfigVO {

    @ApiModelProperty("机构id")
    private Long orgaId;

    @ApiModelProperty("单位id")
    private Long companyId;


    @ApiModelProperty("预约规则,0:按排期总量,1:按单位设置")
    private Integer appointmentRule;

    @ApiModelProperty("时段映射")
    private List<CompanyPeriodMapSettingVO> companyPeriodMapSettingVOS;

    public Long getOrgaId() {
        return orgaId;
    }

    public void setOrgaId(Long orgaId) {
        this.orgaId = orgaId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Integer getAppointmentRule() {
        return appointmentRule;
    }

    public void setAppointmentRule(Integer appointmentRule) {
        this.appointmentRule = appointmentRule;
    }

    public List<CompanyPeriodMapSettingVO> getCompanyPeriodMapSettingVOS() {
        return companyPeriodMapSettingVOS;
    }

    public void setCompanyPeriodMapSettingVOS(List<CompanyPeriodMapSettingVO> companyPeriodMapSettingVOS) {
        this.companyPeriodMapSettingVOS = companyPeriodMapSettingVOS;
    }
}
