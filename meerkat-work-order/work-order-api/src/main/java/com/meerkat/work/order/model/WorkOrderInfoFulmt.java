package com.meerkat.work.order.model;

import java.util.Date;

public class WorkOrderInfoFulmt {
    private Long id;

    private String workOrderNo;

    private String phone;

    private String cityName;

    private String fulmtCityName;

    private String orderNum;

    private String outOrderNum;

    private Long goodsId;

    private String goodsName;

    private Long fulmtOrgaId;

    private String fulmtOrgaName;

    private String acceptorName;

    private Date orderCreateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getWorkOrderNo() {
        return workOrderNo;
    }

    public void setWorkOrderNo(String workOrderNo) {
        this.workOrderNo = workOrderNo == null ? null : workOrderNo.trim();
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    public String getFulmtCityName() {
        return fulmtCityName;
    }

    public void setFulmtCityName(String fulmtCityName) {
        this.fulmtCityName = fulmtCityName == null ? null : fulmtCityName.trim();
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum == null ? null : orderNum.trim();
    }

    public String getOutOrderNum() {
        return outOrderNum;
    }

    public void setOutOrderNum(String outOrderNum) {
        this.outOrderNum = outOrderNum == null ? null : outOrderNum.trim();
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName == null ? null : goodsName.trim();
    }

    public Long getFulmtOrgaId() {
        return fulmtOrgaId;
    }

    public void setFulmtOrgaId(Long fulmtOrgaId) {
        this.fulmtOrgaId = fulmtOrgaId;
    }

    public String getFulmtOrgaName() {
        return fulmtOrgaName;
    }

    public void setFulmtOrgaName(String fulmtOrgaName) {
        this.fulmtOrgaName = fulmtOrgaName == null ? null : fulmtOrgaName.trim();
    }

    public String getAcceptorName() {
        return acceptorName;
    }

    public void setAcceptorName(String acceptorName) {
        this.acceptorName = acceptorName == null ? null : acceptorName.trim();
    }

    public Date getOrderCreateTime() {
        return orderCreateTime;
    }

    public void setOrderCreateTime(Date orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }
}