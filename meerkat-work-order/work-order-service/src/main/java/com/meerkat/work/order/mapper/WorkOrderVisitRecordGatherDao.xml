<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.work.order.mapper.WorkOrderVisitRecordGatherDao">
    <resultMap id="BaseResultMap" type="com.meerkat.work.order.model.WorkOrderVisitRecordGather">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="admin_user_id" jdbcType="BIGINT" property="adminUserId"/>
        <result column="request_action" jdbcType="INTEGER" property="requestAction"/>
        <result column="last_time" jdbcType="TIMESTAMP" property="lastTime"/>
        <result column="num" jdbcType="INTEGER" property="num"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="modified_id" jdbcType="BIGINT" property="modifiedId"/>
        <result column="is_del" jdbcType="BIT" property="isDel"/>
        <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, type, admin_user_id, request_action, last_time, num, create_id, modified_id,
    is_del, gmt_created, gmt_modified
  </sql>

    <insert id="saveOrUpdate" parameterType="com.meerkat.work.order.model.WorkOrderVisitRecordGather">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into tb_work_order_visit_record_gather (type, admin_user_id, request_action,
        last_time, num, create_id,
        modified_id, is_del, gmt_created,
        gmt_modified)
        values (#{type,jdbcType=INTEGER}, #{adminUserId,jdbcType=BIGINT}, #{requestAction,jdbcType=INTEGER},
        #{lastTime,jdbcType=TIMESTAMP}, #{num,jdbcType=INTEGER}, #{createId,jdbcType=BIGINT},
        #{modifiedId,jdbcType=BIGINT}, #{isDel,jdbcType=BIT}, #{gmtCreated,jdbcType=TIMESTAMP},
        #{gmtModified,jdbcType=TIMESTAMP})
        ON DUPLICATE KEY UPDATE
        type = VALUES(type),request_action = VALUES(request_action), last_time = VALUES(last_time),num = num+VALUES(num)
    </insert>
</mapper>