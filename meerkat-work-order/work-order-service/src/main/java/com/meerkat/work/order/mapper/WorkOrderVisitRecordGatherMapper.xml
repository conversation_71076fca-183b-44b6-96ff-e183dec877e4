<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.work.order.mapper.WorkOrderVisitRecordGatherMapper">
  <resultMap id="BaseResultMap" type="com.meerkat.work.order.model.WorkOrderVisitRecordGather">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="admin_user_id" jdbcType="BIGINT" property="adminUserId" />
    <result column="request_action" jdbcType="INTEGER" property="requestAction" />
    <result column="last_time" jdbcType="TIMESTAMP" property="lastTime" />
    <result column="num" jdbcType="INTEGER" property="num" />
    <result column="create_id" jdbcType="BIGINT" property="createId" />
    <result column="modified_id" jdbcType="BIGINT" property="modifiedId" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, type, admin_user_id, request_action, last_time, num, create_id, modified_id, 
    is_del, gmt_created, gmt_modified
  </sql>
  <select id="selectByExample" parameterType="com.meerkat.work.order.model.WorkOrderVisitRecordGatherExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_work_order_visit_record_gather
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_work_order_visit_record_gather
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tb_work_order_visit_record_gather
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meerkat.work.order.model.WorkOrderVisitRecordGatherExample">
    delete from tb_work_order_visit_record_gather
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meerkat.work.order.model.WorkOrderVisitRecordGather">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tb_work_order_visit_record_gather (type, admin_user_id, request_action, 
      last_time, num, create_id, 
      modified_id, is_del, gmt_created, 
      gmt_modified)
    values (#{type,jdbcType=INTEGER}, #{adminUserId,jdbcType=BIGINT}, #{requestAction,jdbcType=INTEGER}, 
      #{lastTime,jdbcType=TIMESTAMP}, #{num,jdbcType=INTEGER}, #{createId,jdbcType=BIGINT}, 
      #{modifiedId,jdbcType=BIGINT}, #{isDel,jdbcType=BIT}, #{gmtCreated,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.meerkat.work.order.model.WorkOrderVisitRecordGather">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tb_work_order_visit_record_gather
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        type,
      </if>
      <if test="adminUserId != null">
        admin_user_id,
      </if>
      <if test="requestAction != null">
        request_action,
      </if>
      <if test="lastTime != null">
        last_time,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="modifiedId != null">
        modified_id,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
      <if test="gmtCreated != null">
        gmt_created,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="adminUserId != null">
        #{adminUserId,jdbcType=BIGINT},
      </if>
      <if test="requestAction != null">
        #{requestAction,jdbcType=INTEGER},
      </if>
      <if test="lastTime != null">
        #{lastTime,jdbcType=TIMESTAMP},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=BIGINT},
      </if>
      <if test="modifiedId != null">
        #{modifiedId,jdbcType=BIGINT},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=BIT},
      </if>
      <if test="gmtCreated != null">
        #{gmtCreated,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meerkat.work.order.model.WorkOrderVisitRecordGatherExample" resultType="java.lang.Long">
    select count(*) from tb_work_order_visit_record_gather
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tb_work_order_visit_record_gather
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.adminUserId != null">
        admin_user_id = #{record.adminUserId,jdbcType=BIGINT},
      </if>
      <if test="record.requestAction != null">
        request_action = #{record.requestAction,jdbcType=INTEGER},
      </if>
      <if test="record.lastTime != null">
        last_time = #{record.lastTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.num != null">
        num = #{record.num,jdbcType=INTEGER},
      </if>
      <if test="record.createId != null">
        create_id = #{record.createId,jdbcType=BIGINT},
      </if>
      <if test="record.modifiedId != null">
        modified_id = #{record.modifiedId,jdbcType=BIGINT},
      </if>
      <if test="record.isDel != null">
        is_del = #{record.isDel,jdbcType=BIT},
      </if>
      <if test="record.gmtCreated != null">
        gmt_created = #{record.gmtCreated,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tb_work_order_visit_record_gather
    set id = #{record.id,jdbcType=BIGINT},
      type = #{record.type,jdbcType=INTEGER},
      admin_user_id = #{record.adminUserId,jdbcType=BIGINT},
      request_action = #{record.requestAction,jdbcType=INTEGER},
      last_time = #{record.lastTime,jdbcType=TIMESTAMP},
      num = #{record.num,jdbcType=INTEGER},
      create_id = #{record.createId,jdbcType=BIGINT},
      modified_id = #{record.modifiedId,jdbcType=BIGINT},
      is_del = #{record.isDel,jdbcType=BIT},
      gmt_created = #{record.gmtCreated,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meerkat.work.order.model.WorkOrderVisitRecordGather">
    update tb_work_order_visit_record_gather
    <set>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="adminUserId != null">
        admin_user_id = #{adminUserId,jdbcType=BIGINT},
      </if>
      <if test="requestAction != null">
        request_action = #{requestAction,jdbcType=INTEGER},
      </if>
      <if test="lastTime != null">
        last_time = #{lastTime,jdbcType=TIMESTAMP},
      </if>
      <if test="num != null">
        num = #{num,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=BIGINT},
      </if>
      <if test="modifiedId != null">
        modified_id = #{modifiedId,jdbcType=BIGINT},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=BIT},
      </if>
      <if test="gmtCreated != null">
        gmt_created = #{gmtCreated,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meerkat.work.order.model.WorkOrderVisitRecordGather">
    update tb_work_order_visit_record_gather
    set type = #{type,jdbcType=INTEGER},
      admin_user_id = #{adminUserId,jdbcType=BIGINT},
      request_action = #{requestAction,jdbcType=INTEGER},
      last_time = #{lastTime,jdbcType=TIMESTAMP},
      num = #{num,jdbcType=INTEGER},
      create_id = #{createId,jdbcType=BIGINT},
      modified_id = #{modifiedId,jdbcType=BIGINT},
      is_del = #{isDel,jdbcType=BIT},
      gmt_created = #{gmtCreated,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>