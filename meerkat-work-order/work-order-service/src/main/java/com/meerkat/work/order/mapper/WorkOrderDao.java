package com.meerkat.work.order.mapper;

import com.meerkat.work.order.model.WorkOrder;
import com.meerkat.work.order.model.WorkOrderReport;
import com.meerkat.work.order.param.WorkOrderQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface WorkOrderDao {
    /**
     * 恢复到待处理
     *
     * @param starteTime
     * @param endTime
     * @return
     */
    int recoveryDelay(@Param("starteTime") Date starteTime, @Param("endTime") Date endTime);

    /**
     * 挂起
     *
     * @param bizNo
     * @return
     */
    int suspend(@Param("bizNo") String bizNo, @Param("adminUserId") Long adminUserId);

    /**
     * 第2个挂起
     *
     * @param workOrderNo
     * @return
     */
    int secondSuspendForWorkOrderNo(@Param("workOrderNo") String workOrderNo, @Param("dealStatus") Integer dealStatus, @Param("adminUserId") Long adminUserId);

    /**
     * 取消第2个挂起
     *
     * @param workOrderBizType
     * @return
     */
    int secondSuspendCanceForBizNo(@Param("workOrderBizType") Integer workOrderBizType, @Param("bizNo") String bizNo, @Param("adminUserId") Long adminUserId);

    /**
     * 取消挂起
     *
     * @param bizNo
     * @return
     */
    int suspendCancel(@Param("bizNo") String bizNo, @Param("adminUserId") Long adminUserId);

    /**
     * 挂起 改 已处理
     *
     * @param bizNo
     * @return
     */
    int suspendForDeal(@Param("bizNo") String bizNo, @Param("operatorId") Long operatorId, @Param("adminUserId") Long adminUserId);

    /**
     * 关闭相等的单号的工单
     *
     * @param bizNo
     * @return
     */
    int closeForEqual(@Param("bizNo") String bizNo, @Param("operatorId") Long operatorId, @Param("adminUserId") Long adminUserId);

    /**
     * 获取第一个处理完成的实际操作人id
     * (调整改成 返回分配者id)
     *
     * @param workOrderBizType
     * @param bizNo
     * @return
     */
    WorkOrder getOperatorIdForKefu(@Param("workOrderBizType") Integer workOrderBizType, @Param("bizNo") String bizNo);

    /**
     * 临时状态 恢复到待处理
     *
     * @return
     */
    int tempToNostatus();


    /**
     * 履约单查询
     *
     * @param query
     * @return
     */
    List<WorkOrder> listForFulmt(@Param("query") WorkOrderQuery query);


    /**
     * 统计  总数
     *
     * @param list
     * @param startTime
     * @param endTime
     * @return
     */
    List<WorkOrderReport> listReportForAll(@Param("list") List<Integer> list, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 统计  已处理
     *
     * @param list
     * @param startTime
     * @param endTime
     * @return
     */
    List<WorkOrderReport> listReportForOk(@Param("list") List<Integer> list, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 统计  已处理(合并)
     *
     * @param list
     * @param startTime
     * @param endTime
     * @return
     */
    List<WorkOrderReport> listReportForOkMerge(@Param("list") List<Integer> list, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 统计  未处理
     *
     * @param list
     * @param startTime
     * @param endTime
     * @return
     */
    List<WorkOrderReport> listReportForNo(@Param("list") List<Integer> list, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 统计  Ta人处理
     *
     * @param list
     * @param startTime
     * @param endTime
     * @return
     */
    List<WorkOrderReport> listReportForTa(@Param("list") List<Integer> list, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 统计  帮处理
     *
     * @param list
     * @param startTime
     * @param endTime
     * @return
     */
    List<WorkOrderReport> listReportForHelp(@Param("list") List<Integer> list, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 统计  退款挂起数
     *
     * @param list
     * @param startTime
     * @param endTime
     * @return
     */
    List<WorkOrderReport> listReportForSuspend(@Param("list") List<Integer> list, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 删除相关工单
     *
     * @param bizNo
     * @return
     */
    int deleteForBizNo(@Param("bizNo") String bizNo, @Param("adminUserId") Long adminUserId);

    /**
     * 删除相关工单
     *
     * @param bizNo
     * @return
     */
    int deleteForWorkOrderBizTypeBizNo(@Param("workOrderBizType") Integer workOrderBizType, @Param("bizNo") String bizNo, @Param("adminUserId") Long adminUserId);

}