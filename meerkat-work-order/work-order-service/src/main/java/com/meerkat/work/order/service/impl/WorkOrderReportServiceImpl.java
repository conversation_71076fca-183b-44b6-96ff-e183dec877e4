package com.meerkat.work.order.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.meerkat.work.order.enums.WorkOrderBizTypeEnum;
import com.meerkat.work.order.mapper.WorkOrderDao;
import com.meerkat.work.order.model.WorkOrderReport;
import com.meerkat.work.order.model.WorkOrderReportExcel;
import com.meerkat.work.order.service.WorkOrderReportService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName WorkOrderReportServiceImpl
 * @Description TODO
 * @createTime 2022/8/26 10:52
 **/
@Service
public class WorkOrderReportServiceImpl implements WorkOrderReportService {
    @Resource
    WorkOrderDao workOrderDao;


    @Override
    public Map<Long, List<WorkOrderReportExcel>> customerServiceReport(Date startDay, Date endDay, boolean isMerge) {
        Map<Long, List<WorkOrderReportExcel>> execlMap = new HashMap<>();
        //----待预约工单统计
        //时间特殊处理 16:30 到
        DateTime sd1 = DateUtil.offsetDay(startDay, -1);
        DateTime subStartTime = DateUtil.parse(DateUtil.format(sd1, "yyyy-MM-dd") + " 16:30:00", "yyyy-MM-dd HH:mm:ss");
        DateTime subEndTime = DateUtil.parse(DateUtil.format(endDay, "yyyy-MM-dd") + " 16:29:00", "yyyy-MM-dd HH:mm:ss");
        report(execlMap, WorkOrderBizTypeEnum.SUBSCRIBE.getType(), subStartTime, subEndTime, isMerge);
        //------待核销+待改期
        DateTime otherStartTime = DateUtil.parse(DateUtil.format(startDay, "yyyy-MM-dd") + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
        DateTime otherEndTime = DateUtil.parse(DateUtil.format(endDay, "yyyy-MM-dd") + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
        report(execlMap, WorkOrderBizTypeEnum.WRITEOFF.getType(), otherStartTime, otherEndTime, isMerge);
        report(execlMap, WorkOrderBizTypeEnum.RESCHEDULE.getType(), otherStartTime, otherEndTime, isMerge);
        return execlMap;
    }

    @Override
    public Map<Long, List<WorkOrderReportExcel>> operateReport(Date startDay, Date endDay, boolean isMerge) {
        Map<Long, List<WorkOrderReportExcel>> execlMap = new HashMap<>();
        //------二方待核销
        DateTime otherStartTime = DateUtil.parse(DateUtil.format(startDay, "yyyy-MM-dd") + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
        DateTime otherEndTime = DateUtil.parse(DateUtil.format(endDay, "yyyy-MM-dd") + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
        report(execlMap, WorkOrderBizTypeEnum.TWOWRITEOFF.getType(), otherStartTime, otherEndTime, isMerge);
        return execlMap;
    }

    @Override
    public Map<Long, List<WorkOrderReportExcel>> businessReport(Date startDay, Date endDay, boolean isMerge) {
        Map<Long, List<WorkOrderReportExcel>> execlMap = new HashMap<>();
        //------机构待确认  审批工单
        DateTime otherStartTime = DateUtil.parse(DateUtil.format(startDay, "yyyy-MM-dd") + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
        DateTime otherEndTime = DateUtil.parse(DateUtil.format(endDay, "yyyy-MM-dd") + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
        report(execlMap, WorkOrderBizTypeEnum.CONFIRM.getType(), otherStartTime, otherEndTime, isMerge);
        report(execlMap, WorkOrderBizTypeEnum.REFUND.getType(), otherStartTime, otherEndTime, isMerge);
        return execlMap;
    }


    private void report(Map<Long, List<WorkOrderReportExcel>> execlMap, Integer type, Date subStartTime, Date subEndTime, boolean isMerge) {
        //----工单统计
        List<Integer> bizList = Lists.newArrayList(type);
        List<WorkOrderReport> listReportForAll = workOrderDao.listReportForAll(bizList, subStartTime, subEndTime);
        List<WorkOrderReport> listReportForNo = workOrderDao.listReportForNo(bizList, subStartTime, subEndTime);
        List<WorkOrderReport> listReportForSuspend = workOrderDao.listReportForSuspend(bizList, subStartTime, subEndTime);

        List<WorkOrderReport> listReportForHelp = null;
        List<WorkOrderReport> listReportForTa = null;
        List<WorkOrderReport> listReportForOk = null;
        if (!isMerge) {
            listReportForHelp = workOrderDao.listReportForHelp(bizList, subStartTime, subEndTime);
            listReportForTa = workOrderDao.listReportForTa(bizList, subStartTime, subEndTime);
            listReportForOk = workOrderDao.listReportForOk(bizList, subStartTime, subEndTime);
        } else {
            listReportForHelp = new ArrayList<>();
            listReportForTa = new ArrayList<>();
            listReportForOk = workOrderDao.listReportForOkMerge(bizList, subStartTime, subEndTime);
        }

        /*        //分配总数  1
        private int receiveNum;
        //已处理数 2
        private int okNum;
        //未处理数 3
        private int noNum;
        //TA人处理数 4
        private int taNum;
        //退款挂起数 5
        private int suspendNum;
        //帮处理数 6
        private int helpNum;*/
        deal(listReportForAll, execlMap, 1);
        deal(listReportForHelp, execlMap, 6);
        deal(listReportForOk, execlMap, 2);
        deal(listReportForNo, execlMap, 3);
        deal(listReportForSuspend, execlMap, 5);
        deal(listReportForTa, execlMap, 4);

        return;
    }

    private void deal(List<WorkOrderReport> datalist, Map<Long, List<WorkOrderReportExcel>> execlMap, int field) {
        if (CollectionUtils.isEmpty(datalist)) {
            return;
        }
        datalist.forEach(workOrderReport -> {
            List<WorkOrderReportExcel> list = null;
            if (execlMap.containsKey(workOrderReport.getUserId())) {
                list = execlMap.get(workOrderReport.getUserId());
            } else {
                list = Lists.newArrayList();
            }
            boolean ext = false;
            for (WorkOrderReportExcel workOrderReportExcel : list) {
                if (workOrderReport.getWorkOrderBizType().equals(workOrderReportExcel.getWorkOrderBizType())) {
                    dealField(workOrderReportExcel, workOrderReport.getCou(), field);
                    ext = true;
                }
            }
            if (!ext) {
                WorkOrderReportExcel workOrderReportExcel = new WorkOrderReportExcel();
                workOrderReportExcel.setWorkOrderBizType(workOrderReport.getWorkOrderBizType());
                dealField(workOrderReportExcel, workOrderReport.getCou(), field);
                list.add(workOrderReportExcel);
            }

            execlMap.put(workOrderReport.getUserId(), list);
        });

    }

    private void dealField(WorkOrderReportExcel workOrderReportExcel, Integer num, Integer field) {
/*        //分配总数  1
        private int receiveNum;
        //已处理数 2
        private int okNum;
        //未处理数 3
        private int noNum;
        //TA人处理数 4
        private int taNum;
        //退款挂起数 5
        private int suspendNum;
        //帮处理数 6
        private int helpNum;*/
        if (1 == field) {
            workOrderReportExcel.setReceiveNum(workOrderReportExcel.getReceiveNum() + num);
        } else if (2 == field) {
            workOrderReportExcel.setOkNum(workOrderReportExcel.getOkNum() + num);
        } else if (3 == field) {
            workOrderReportExcel.setNoNum(workOrderReportExcel.getNoNum() + num);
        } else if (4 == field) {
            workOrderReportExcel.setTaNum(workOrderReportExcel.getTaNum() + num);
        } else if (5 == field) {
            workOrderReportExcel.setSuspendNum(workOrderReportExcel.getSuspendNum() + num);
        } else if (6 == field) {
            workOrderReportExcel.setHelpNum(workOrderReportExcel.getHelpNum() + num);
        }
    }


}
