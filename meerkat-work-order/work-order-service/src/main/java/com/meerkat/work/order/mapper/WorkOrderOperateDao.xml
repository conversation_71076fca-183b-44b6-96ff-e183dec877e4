<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.work.order.mapper.WorkOrderOperateDao">
    <resultMap id="BaseResultMap" type="com.meerkat.work.order.model.WorkOrderOperate">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="work_order_no" jdbcType="VARCHAR" property="workOrderNo"/>
        <result column="before_status" jdbcType="INTEGER" property="beforeStatus"/>
        <result column="before_deal_status" jdbcType="INTEGER" property="beforeDealStatus"/>
        <result column="after_status" jdbcType="INTEGER" property="afterStatus"/>
        <result column="after_deal_status" jdbcType="INTEGER" property="afterDealStatus"/>
        <result column="operate_source" jdbcType="INTEGER" property="operateSource"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="modified_id" jdbcType="BIGINT" property="modifiedId"/>
        <result column="is_del" jdbcType="BIT" property="isDel"/>
        <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.meerkat.work.order.model.WorkOrderOperate">
        <result column="operate_content" jdbcType="LONGVARCHAR" property="operateContent"/>
    </resultMap>

    <insert id="recoveryDelay">
        INSERT into
        tb_work_order_operate(work_order_no,operate_status,before_status,before_deal_status,after_status,after_deal_status,
        operate_source,gmt_created)
        select work_order_no,3,1,deal_status,0,deal_status,0,NOW() from tb_work_order where status = 1
        and last_time &gt;= #{starteTime,jdbcType=TIMESTAMP}
        and last_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        and is_del = 0;
    </insert>

    <insert id="suspend">
        INSERT into
        tb_work_order_operate(work_order_no,operate_status,before_status,before_deal_status,after_status,after_deal_status,
        operate_source,gmt_created,create_id,modified_id)
		select work_order_no,4,status,deal_status,status,deal_status,#{operateSource,jdbcType=INTEGER},NOW(),#{adminUserId,jdbcType=BIGINT},#{adminUserId,jdbcType=BIGINT} from tb_work_order where work_order_biz_type in (100,101,102,103,104) and biz_no = #{bizNo,jdbcType=VARCHAR}
         and status != 3 and is_suspend = 0 and is_del = 0;
    </insert>

    <insert id="suspendCancel">
        INSERT into
        tb_work_order_operate(work_order_no,operate_status,before_status,before_deal_status,after_status,after_deal_status,
        operate_source,gmt_created,create_id,modified_id)
		select work_order_no,5,status,deal_status,status,deal_status,#{operateSource,jdbcType=INTEGER},NOW(),#{adminUserId,jdbcType=BIGINT},#{adminUserId,jdbcType=BIGINT} from tb_work_order where work_order_biz_type in (100,101,102,103,104) and biz_no = #{bizNo,jdbcType=VARCHAR}
         and status != 3 and is_suspend = 1 and is_del = 0;
    </insert>

    <insert id="suspendForDeal">
        INSERT into
        tb_work_order_operate(work_order_no,operate_status,before_status,before_deal_status,after_status,after_deal_status,
        operate_source,gmt_created,create_id,modified_id)
		select work_order_no,6,status,deal_status,3,deal_status,#{operateSource,jdbcType=INTEGER},NOW(),#{adminUserId,jdbcType=BIGINT},#{adminUserId,jdbcType=BIGINT} from tb_work_order where work_order_biz_type in (100,101,102,103,104) and biz_no = #{bizNo,jdbcType=VARCHAR}
         and status != 3 and is_del = 0;
    </insert>

    <insert id="closeForEqual">
        INSERT into
        tb_work_order_operate(work_order_no,operate_status,before_status,before_deal_status,after_status,after_deal_status,
        operate_source,gmt_created,create_id,modified_id)
		select work_order_no,7,status,deal_status,3,deal_status,#{operateSource,jdbcType=INTEGER},NOW(),#{adminUserId,jdbcType=BIGINT},#{adminUserId,jdbcType=BIGINT} from tb_work_order where work_order_biz_type in (100,101,102,103,104) and biz_no = #{bizNo,jdbcType=VARCHAR}
         and status != 3 and work_order_biz_type_son != 10099002 and is_del = 0;
    </insert>

    <insert id="secondSuspendCanceForBizNo">
        INSERT into
        tb_work_order_operate(work_order_no,operate_status,before_status,before_deal_status,after_status,after_deal_status,
        operate_source,gmt_created,create_id,modified_id)
		select work_order_no,#{operateStatus,jdbcType=INTEGER},status,deal_status,status,deal_status,#{operateSource,jdbcType=INTEGER},NOW(),#{adminUserId,jdbcType=BIGINT},#{adminUserId,jdbcType=BIGINT} from tb_work_order
		where work_order_biz_type  = #{workOrderBizType,jdbcType=INTEGER} and biz_no = #{bizNo,jdbcType=VARCHAR} and is_second_suspend = 1 and is_del = 0;
    </insert>

    <select id="getLastContentWorkOrderOperate" resultType="java.lang.String">
        select operate_content from tb_work_order_operate where work_order_no = #{workOrderNo,jdbcType=VARCHAR}
         and after_status = #{status,jdbcType=INTEGER} and is_del = 0 order by id desc limit 1
    </select>


</mapper>