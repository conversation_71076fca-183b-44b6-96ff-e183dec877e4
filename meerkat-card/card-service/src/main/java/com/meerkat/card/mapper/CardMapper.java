package com.meerkat.card.mapper;

import com.meerkat.card.mapper.dataobj.CardDO;
import com.meerkat.card.model.param.CardDaoQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CardMapper {

    CardDO selectOneById(Long id);

    /**
     * 根据ID，履约人ID查询卡信息
     * @param id
     * @return
     */
    CardDO selectOneByIdAndUserId(Long id,Long userId);

    List<CardDO> selectByQuery(CardDaoQuery cardDaoQuery);

    Long insertList(List<CardDO> cardDOList);

    List<CardDO> selectByIds(List<Long> ids);

    int updateByIdAndVersion(CardDO cardDO);

    /**
     * 根据ID和版本号，批量跟新
     * @param cardDOList
     * @return
     */
    int batchUpdateByIdAndVersion(List<CardDO> cardDOList);
}
