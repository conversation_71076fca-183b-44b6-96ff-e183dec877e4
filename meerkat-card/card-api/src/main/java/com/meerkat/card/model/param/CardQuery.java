package com.meerkat.card.model.param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 卡查询条件
 */
public class CardQuery {
    /**
     * 机构/渠道类型
     */
    private Integer chorgaType;
    /**
     * 机构/渠道id
     */
    private Long chorgaId;
    /**
     * 兑换码
     */
    private String cdk;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 批次id
     */
    private Long batchId;

    /**
     * 卡状态
     */
    private List<Integer> status;

    /**
     * 过期时间,获取过期时间小于该时间的数据
     */
    private LocalDateTime expireTime;

    /**
     * 根据状态排序
     */
    private List<Integer> orderByStatus;

    public String getCdk() {
        return cdk;
    }

    public void setCdk(String cdk) {
        this.cdk = cdk;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    public List<Integer> getStatus() {
        return status;
    }

    public void setStatus(List<Integer> status) {
        this.status = status;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public List<Integer> getOrderByStatus() {
        return orderByStatus;
    }

    public void setOrderByStatus(List<Integer> orderByStatus) {
        this.orderByStatus = orderByStatus;
    }

    public Integer getChorgaType() {
        return chorgaType;
    }

    public void setChorgaType(Integer chorgaType) {
        this.chorgaType = chorgaType;
    }

    public Long getChorgaId() {
        return chorgaId;
    }

    public void setChorgaId(Long chorgaId) {
        this.chorgaId = chorgaId;
    }
}
