package com.meerkat.card.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 卡状态枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2022/3/16 11:28
 */
public enum CardStatusEnum {

    /**
     * 未领取
     */
    UN_GET(0, "未领取"),
    /**
     * 未使用
     */
    UN_USE(1, "未使用"),
    /**
     * 已撤销
     */
    CANCELED(2, "已撤销"),
    /**
     * 已使用
     */
    USED(3, "已使用"),
    /**
     * 已过期
     */
    TIME_OUT(4, "已过期"),
    ;

    private final int code;
    private final String message;

    CardStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
