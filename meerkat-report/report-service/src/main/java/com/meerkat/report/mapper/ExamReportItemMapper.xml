<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.meerkat.report.mapper.ExamReportItemMapper" >
  <resultMap id="BaseResultMap" type="com.meerkat.report.dao.object.ExamReportItemDO" >
    <result column="id" property="id" />
    <result column="report_id" property="reportId" />
    <result column="report_department_id" property="reportDepartmentId" />
    <result column="name" property="name" />
    <result column="his_item_code" property="hisItemCode" />
    <result column="summary" property="summary" />
    <result column="doctor" property="doctor" />
    <result column="exam_date" property="examDate" />
    <result column="sort_field" property="sortField" />
    <result column="is_deleted" property="isDeleted" />
    <result column="gmt_created" property="gmtCreated" />
    <result column="gmt_modified" property="gmtModified" />
  </resultMap>

  <sql id="Insert_Column">
    id,
    report_id,
    report_department_id,
    `name`,
    his_item_code,
    summary,
    doctor,
    exam_date,
    sort_field
  </sql>

  <sql id="Base_Column_All">
    <include refid="Insert_Column"/>,
    is_deleted,
    gmt_created,
    gmt_modified
  </sql>

  <insert id="insertBatch" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
          parameterType="com.meerkat.report.dao.object.ExamReportItemDO" >
    INSERT INTO tb_examreport_item(
    <include refid="Insert_Column"/>
    )VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT},
      #{item.reportId,jdbcType=BIGINT},
      #{item.reportDepartmentId,jdbcType=BIGINT},
      #{item.name,jdbcType=VARCHAR},
      #{item.hisItemCode,jdbcType=VARCHAR},
      #{item.summary,jdbcType=VARCHAR},
      #{item.doctor,jdbcType=VARCHAR},
      #{item.examDate,jdbcType=DATE},
      #{item.sortField,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <select id="selectByReportId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    SELECT <include refid="Base_Column_All" />
      FROM tb_examreport_item
    WHERE report_id = #{reportId,jdbcType=BIGINT}
      AND is_deleted = 0
  </select>

  <delete id="deleteByReportId" >
    DELETE FROM tb_examreport_item
    WHERE report_id = #{reportId,jdbcType=BIGINT}
  </delete>

</mapper>