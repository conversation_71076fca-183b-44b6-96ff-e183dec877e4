package com.meerkat.report.dao.object;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @ClassName ExamReportGroupBaseDO
 * @description 团检报告基础信息表
 * @createTime 2022/7/27 10:25
 */
public class ExamReportGroupBaseDO {

    /**
     * id
     */
    private Long id;

    /**
     * 报告名称
     */
    private String name;

    /**
     * 报告名称拼音
     */
    private String pinyin;

    /**
     * 机构id
     */
    private Long organizationId;

    /**
     * 机构单位id
     */
    private Long orgaCompanyId;

    /**
     * 单位名称
     */
    private String companyName;

    /**
     * 单位名称拼音
     */
    private String companyNamePy;

    /**
     * logo的url
     */
    private String logo;

    /**
     * logo的名称
     */
    private String logoName;

    /**
     * 起始体检时间
     */
    private LocalDateTime startTime;

    /**
     * 结束体检时间
     */
    private LocalDateTime endTime;

    /**
     * 前言
     */
    private String foreword;

    /**
     * 团检总结
     */
    private String summary;

    /**
     * 团检总结医生/操作人
     */
    private String doctor;

    /**
     * 标语
     */
    private String slogan;

    /**
     * 人数
     */
    private Integer count;

    /**
     * 联系方式
     */
    private String contactDetails;

    /**
     * 体检中心二维码
     */
    private String qrCode;

    /**
     * 是否删除：0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public Long getOrgaCompanyId() {
        return orgaCompanyId;
    }

    public void setOrgaCompanyId(Long orgaCompanyId) {
        this.orgaCompanyId = orgaCompanyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyNamePy() {
        return companyNamePy;
    }

    public void setCompanyNamePy(String companyNamePy) {
        this.companyNamePy = companyNamePy;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogoName() {
        return logoName;
    }

    public void setLogoName(String logoName) {
        this.logoName = logoName;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getForeword() {
        return foreword;
    }

    public void setForeword(String foreword) {
        this.foreword = foreword;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getDoctor() {
        return doctor;
    }

    public void setDoctor(String doctor) {
        this.doctor = doctor;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public LocalDateTime getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(LocalDateTime gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getSlogan() {
        return slogan;
    }

    public void setSlogan(String slogan) {
        this.slogan = slogan;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getContactDetails() {
        return contactDetails;
    }

    public void setContactDetails(String contactDetails) {
        this.contactDetails = contactDetails;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }
}
