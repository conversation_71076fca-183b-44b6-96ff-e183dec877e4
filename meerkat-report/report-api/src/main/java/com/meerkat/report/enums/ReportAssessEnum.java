package com.meerkat.report.enums;

/**
 * <AUTHOR>
 * @ClassName ReportAssessEnum.java
 * @Description TODO
 * @createTime 2022-04-21 16:06:00
 */
public enum ReportAssessEnum {

    UN_ASSESS(0, "未审核"),

    PASS(1, "审核通过"),

    UN_PASS(2, "审核未通过"),

    ;


    private int code;

    private String name;

    private ReportAssessEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
