package com.meerkat.report.model;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName PreviousDataCompare
 * @description 历次数据
 * @createTime 2022/8/15 14:50
 */
public class PreviousDataCompare {

    /**
     * 本次报告总检人数
     */
    private Integer totalCount;

    /**
     * 本次报告开始时间
     */
    private LocalDateTime startTime;

    /**
     * 本次报告结束时间
     */
    private LocalDateTime endTime;

    /**
     * 上份报告总检人数
     */
    private Integer previousTotalCount;

    /**
     * 上份报告开始时间
     */
    private LocalDateTime previousStartTime;

    /**
     * 上份报告结束时间
     */
    private LocalDateTime previousEndTime;

    /**
     * 历次数据对比
     */
    private List<DiagnoseCompare> diagnoseCompareList;

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Integer getPreviousTotalCount() {
        return previousTotalCount;
    }

    public void setPreviousTotalCount(Integer previousTotalCount) {
        this.previousTotalCount = previousTotalCount;
    }

    public LocalDateTime getPreviousStartTime() {
        return previousStartTime;
    }

    public void setPreviousStartTime(LocalDateTime previousStartTime) {
        this.previousStartTime = previousStartTime;
    }

    public LocalDateTime getPreviousEndTime() {
        return previousEndTime;
    }

    public void setPreviousEndTime(LocalDateTime previousEndTime) {
        this.previousEndTime = previousEndTime;
    }

    public List<DiagnoseCompare> getDiagnoseCompareList() {
        return diagnoseCompareList;
    }

    public void setDiagnoseCompareList(List<DiagnoseCompare> diagnoseCompareList) {
        this.diagnoseCompareList = diagnoseCompareList;
    }
}
