package com.meerkat.report.param;

import com.meerkat.common.db.Page;

/**
 * <AUTHOR>
 * @ClassName TargetItemQuery
 * @description 指标项查询参数
 * @createTime 2022/8/17 11:57
 */
public class TargetItemQuery {

    private String name;

    private Long organizationId;

    private Page page;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }
}
