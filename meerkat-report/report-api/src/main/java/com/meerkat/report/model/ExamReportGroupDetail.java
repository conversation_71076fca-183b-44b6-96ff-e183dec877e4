package com.meerkat.report.model;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ExamReportGroupDetail
 * @description 团建报告详情数据
 * @createTime 2022/8/24 14:51
 */
public class ExamReportGroupDetail {

    /**
     * 团检报告id
     */
    private Long id;

    /**
     * 团检报告配置/目录
     */
    private ExamReportGroupConfig reportGroupConfig;

    /**
     * 封面、封底、前言、总结
     */
    private ExamReportGroupBase reportGroupBase;

    /**
     * 体检基本信息
     */
    private ExamBaseInfo examBaseInfo;

    /**
     * 年龄及性别分布
     */
    private List<ExamReportGroupAgeSummary> reportGroupAgeSummaryList;

    /**
     * 重要指标结果分析
     */
    private List<ReportGroupImportSummary> reportGroupImportSummaryList;

    /**
     * 体检异常top10/主要异常解释及建议
     */
    private List<ReportGroupDiagnoseRecord> reportGroupDiagnoseRecordList;

    /**
     * 组合异常统计
     */
    private List<ReportGroupComboEx> reportGroupComboExList;

    /**
     * 历次数据对比
     */
    private PreviousDataCompare previousDataCompare;

    /**
     * 所有体检异常统计
     */
    private List<ReportGroupDiagnoseRecord> examAllExList;

    /**
     * 附录
     */
    private List<ReportGroupDiagnoseRecord> appendixList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ExamReportGroupConfig getReportGroupConfig() {
        return reportGroupConfig;
    }

    public void setReportGroupConfig(ExamReportGroupConfig reportGroupConfig) {
        this.reportGroupConfig = reportGroupConfig;
    }

    public ExamReportGroupBase getReportGroupBase() {
        return reportGroupBase;
    }

    public void setReportGroupBase(ExamReportGroupBase reportGroupBase) {
        this.reportGroupBase = reportGroupBase;
    }

    public ExamBaseInfo getExamBaseInfo() {
        return examBaseInfo;
    }

    public void setExamBaseInfo(ExamBaseInfo examBaseInfo) {
        this.examBaseInfo = examBaseInfo;
    }

    public List<ExamReportGroupAgeSummary> getReportGroupAgeSummaryList() {
        return reportGroupAgeSummaryList;
    }

    public void setReportGroupAgeSummaryList(List<ExamReportGroupAgeSummary> reportGroupAgeSummaryList) {
        this.reportGroupAgeSummaryList = reportGroupAgeSummaryList;
    }

    public List<ReportGroupImportSummary> getReportGroupImportSummaryList() {
        return reportGroupImportSummaryList;
    }

    public void setReportGroupImportSummaryList(List<ReportGroupImportSummary> reportGroupImportSummaryList) {
        this.reportGroupImportSummaryList = reportGroupImportSummaryList;
    }


    public List<ReportGroupComboEx> getReportGroupComboExList() {
        return reportGroupComboExList;
    }

    public void setReportGroupComboExList(List<ReportGroupComboEx> reportGroupComboExList) {
        this.reportGroupComboExList = reportGroupComboExList;
    }

    public PreviousDataCompare getPreviousDataCompare() {
        return previousDataCompare;
    }

    public void setPreviousDataCompare(PreviousDataCompare previousDataCompare) {
        this.previousDataCompare = previousDataCompare;
    }

    public List<ReportGroupDiagnoseRecord> getReportGroupDiagnoseRecordList() {
        return reportGroupDiagnoseRecordList;
    }

    public void setReportGroupDiagnoseRecordList(List<ReportGroupDiagnoseRecord> reportGroupDiagnoseRecordList) {
        this.reportGroupDiagnoseRecordList = reportGroupDiagnoseRecordList;
    }

    public List<ReportGroupDiagnoseRecord> getExamAllExList() {
        return examAllExList;
    }

    public void setExamAllExList(List<ReportGroupDiagnoseRecord> examAllExList) {
        this.examAllExList = examAllExList;
    }

    public List<ReportGroupDiagnoseRecord> getAppendixList() {
        return appendixList;
    }

    public void setAppendixList(List<ReportGroupDiagnoseRecord> appendixList) {
        this.appendixList = appendixList;
    }
}
