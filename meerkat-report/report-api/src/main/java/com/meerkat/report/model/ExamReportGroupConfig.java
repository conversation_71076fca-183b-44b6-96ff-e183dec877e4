package com.meerkat.report.model;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @ClassName ExamReportGroupConfig
 * @description 团检报告配置表
 * @createTime 2022/7/26 20:56
 */
public class ExamReportGroupConfig {

    /**
     * id
     */
    private Long id;

    /**
     * 机构id
     */
    private Long organizationId;

    /**
     * 是否需要基本资料：0-不需要 1-需要
     */
    private Integer basicData;

    /**
     * 是否需要重要指标：0-不需要 1-需要
     */
    private Integer importTarget;

    /**
     * 是否需要组合异常：0-不需要 1-需要
     */
    private Integer combinationEx;

    /**
     * 是否需要top10异常：0-不需要 1-需要
     */
    private Integer topEx;

    /**
     * 是否需要报告对比：0-不需要 1-需要
     */
    private Integer reportCompare;

    /**
     * 是否需要全量异常统计：0-不需要 1-需要
     */
    private Integer allExCount;

    /**
     * 是否需要top异常解释：0-不需要 1-需要
     */
    private Integer topExplain;

    /**
     * 是否需要附录：0-不需要 1-需要
     */
    private Integer appendix;

    /**
     * 是否删除：0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public Integer getImportTarget() {
        return importTarget;
    }

    public void setImportTarget(Integer importTarget) {
        this.importTarget = importTarget;
    }

    public Integer getCombinationEx() {
        return combinationEx;
    }

    public void setCombinationEx(Integer combinationEx) {
        this.combinationEx = combinationEx;
    }

    public Integer getTopEx() {
        return topEx;
    }

    public void setTopEx(Integer topEx) {
        this.topEx = topEx;
    }

    public Integer getReportCompare() {
        return reportCompare;
    }

    public void setReportCompare(Integer reportCompare) {
        this.reportCompare = reportCompare;
    }

    public Integer getAllExCount() {
        return allExCount;
    }

    public void setAllExCount(Integer allExCount) {
        this.allExCount = allExCount;
    }

    public Integer getTopExplain() {
        return topExplain;
    }

    public void setTopExplain(Integer topExplain) {
        this.topExplain = topExplain;
    }

    public Integer getAppendix() {
        return appendix;
    }

    public void setAppendix(Integer appendix) {
        this.appendix = appendix;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public LocalDateTime getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(LocalDateTime gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getBasicData() {
        return basicData;
    }

    public void setBasicData(Integer basicData) {
        this.basicData = basicData;
    }
}
