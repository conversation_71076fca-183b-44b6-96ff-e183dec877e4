package com.meerkat.report.model;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @ClassName ExamReportTargetItem
 * @description 报告指标项
 * @createTime 2022/7/28 15:51
 */
public class ExamReportTargetItem implements Serializable {

    private static final long serialVersionUID = -7448274546606725607L;
    /**
     * id
     */
    private Long id;

    /**
     * 机构id
     */
    private Long organizationId;

    /**
     * 指标项目名称
     */
    private String targetItemName;

    /**
     * 指标项目解释
     */
    private String targetItemExplain;

    /**
     * 性别: 0-女 1-男 2-通用
     */
    private Integer gender;

    /**
     * 关联项名称(报告的小项名称)
     */
    private String relItemName;

    /**
     * 正常下限
     */
    private Double normalLowerLimit;

    /**
     * 正常上限
     */
    private Double normalUpperLimit;

    /**
     * 异常高值
     */
    private Double exHighValue;

    /**
     * 偏低文本
     */
    private String lowText;

    /**
     * 正常值文本
     */
    private String normalText;

    /**
     * 偏高文本
     */
    private String highText;

    /**
     * 异常高值文本
     */
    private String exHighValueText;

    /**
     * 解析规则
     */
    private String rule;

    /**
     * 是否启用：0-不启用 1-启动
     */
    private Integer state;

    /**
     * 是否删除：0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public String getTargetItemName() {
        return targetItemName;
    }

    public void setTargetItemName(String targetItemName) {
        this.targetItemName = targetItemName;
    }

    public String getTargetItemExplain() {
        return targetItemExplain;
    }

    public void setTargetItemExplain(String targetItemExplain) {
        this.targetItemExplain = targetItemExplain;
    }

    public String getRelItemName() {
        return relItemName;
    }

    public void setRelItemName(String relItemName) {
        this.relItemName = relItemName;
    }

    public Double getNormalLowerLimit() {
        return normalLowerLimit;
    }

    public void setNormalLowerLimit(Double normalLowerLimit) {
        this.normalLowerLimit = normalLowerLimit;
    }

    public Double getNormalUpperLimit() {
        return normalUpperLimit;
    }

    public void setNormalUpperLimit(Double normalUpperLimit) {
        this.normalUpperLimit = normalUpperLimit;
    }

    public Double getExHighValue() {
        return exHighValue;
    }

    public void setExHighValue(Double exHighValue) {
        this.exHighValue = exHighValue;
    }

    public String getLowText() {
        return lowText;
    }

    public void setLowText(String lowText) {
        this.lowText = lowText;
    }

    public String getNormalText() {
        return normalText;
    }

    public void setNormalText(String normalText) {
        this.normalText = normalText;
    }

    public String getHighText() {
        return highText;
    }

    public void setHighText(String highText) {
        this.highText = highText;
    }

    public String getExHighValueText() {
        return exHighValueText;
    }

    public void setExHighValueText(String exHighValueText) {
        this.exHighValueText = exHighValueText;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public LocalDateTime getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(LocalDateTime gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }
}
