package com.meerkat.report.model;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ReportGroupComboEx
 * @description 组合异常
 * @createTime 2022/8/15 12:49
 */
public class ReportGroupComboEx {

    /**
     * 组合异常id
     */
    private Long comboExId;

    /**
     * 组合异常名称
     */
    private String comboExName;


    /**
     * 总人数
     */
    private Integer totalCount;

    /**
     * 总人数占比
     */
    private Double totalPercent;

    /**
     * 男性人数
     */
    private Integer maleCount;

    /**
     * 女性人数
     */
    private Integer femaleCount;

    /**
     * 组合异常关联的异常
     */
    private List<ReportGroupDiagnoseRecord> reportGroupDiagnoseRecordList;

    public Long getComboExId() {
        return comboExId;
    }

    public void setComboExId(Long comboExId) {
        this.comboExId = comboExId;
    }

    public String getComboExName() {
        return comboExName;
    }

    public void setComboExName(String comboExName) {
        this.comboExName = comboExName;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Double getTotalPercent() {
        return totalPercent;
    }

    public void setTotalPercent(Double totalPercent) {
        this.totalPercent = totalPercent;
    }

    public Integer getMaleCount() {
        return maleCount;
    }

    public void setMaleCount(Integer maleCount) {
        this.maleCount = maleCount;
    }

    public Integer getFemaleCount() {
        return femaleCount;
    }

    public void setFemaleCount(Integer femaleCount) {
        this.femaleCount = femaleCount;
    }

    public List<ReportGroupDiagnoseRecord> getReportGroupDiagnoseRecordList() {
        return reportGroupDiagnoseRecordList;
    }

    public void setReportGroupDiagnoseRecordList(List<ReportGroupDiagnoseRecord> reportGroupDiagnoseRecordList) {
        this.reportGroupDiagnoseRecordList = reportGroupDiagnoseRecordList;
    }
}
