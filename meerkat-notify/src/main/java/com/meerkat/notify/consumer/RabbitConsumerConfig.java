package com.meerkat.notify.consumer;

import com.meerkat.common.exception.BizException;
import com.meerkat.notify.exception.ConsumerErrorInfoEnum;
import com.meerkat.notify.listener.AbstractMessageListener;
import com.meerkat.notify.retry.MessageRetry;
import com.meerkat.notify.retry.retryListener.AbstractRetryListener;
import com.meerkat.notify.store.StoreTypeEnum;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.util.Assert;

import javax.validation.constraints.NotNull;
import java.util.List;


public interface RabbitConsumerConfig {
    /**
     * 队列断开 重连次数
     * @return
     */
    default Integer reconnectionTimes() {
        return Integer.MAX_VALUE;
    }

    /**
     * 队列名称
     * @return
     */
    @NotNull
    String queueName();


    /**
     * 消息监听器
     * @return
     */
    @NotNull
    AbstractMessageListener messageListener();

    MessageRetry messageRetry();

    /**
     *
     * @return key queueName  value routingKey
     */
    List<String> routingKeys();

    Exchange exchange();

    /**
     * 交换机名称
     * @return
     */
     String topicExchangeName();
    
    default String messageListenerContainerName() {
        if (messageListener() != null) {
            return messageListener().getClass().getSimpleName() + "Container";
        }
        throw new BizException(ConsumerErrorInfoEnum.PARAM_ERROR, "messageListener 不能为空");
    }

    /**
     *
     *
     * 1. 线下环境自动创建virtualHost，并且交换机 队列都会绑定在这个virtaul Host下。可以到rabbitmq控制台查看结果。
     * 2. 线上环境不会自动创建virtualHost,需要人工到线上rabbitmq控制台新建virtualHost，启动完成后会自动连接对应的virtualHost。
     *
     * @return
     */
    @NotNull
    String virtualHost();

    /**
     * 预拉取几条消息
     * @return
     */
    default Integer preFetchCount() {
        return null;
    }

    /**
     * 最小并发-创建几个消费者
     * 可以在rabbitmq控制台查看消费者数量。
     * @return
     */
    default Integer minConcurrency() {
        return null;
    }

    /**
     * 消息确认
     *  自动确认模式 消息监听器（ @see ChannelAwareMessageListener）推荐使用 AbstractMessageListener
     *  AUTO  与 rabbitmq autoack不同 这是spring rabbitmq自己内部调用了 channel.basicAck {@link  org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.commitIfNecessary(boolean localTx)()}
     * @return
     */
    @NotNull
    default AcknowledgeMode ackMode() {
        return AcknowledgeMode.AUTO;
    }
    default StoreTypeEnum messageStoreType() {
        return null;
    }
    default String messageStoreTable() {
        if (messageListener() != null) {
            return messageListener().getClass().getSimpleName();
        }
        return null;
    }
    default AbstractRetryListener getRetryListener() {
        return null;
    }
}
