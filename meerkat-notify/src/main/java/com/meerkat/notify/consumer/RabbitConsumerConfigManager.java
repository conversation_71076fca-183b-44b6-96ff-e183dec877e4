package com.meerkat.notify.consumer;

import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Strings;
import com.meerkat.common.utils.EnvironmentUtil;
import com.meerkat.notify.connection.ConnectionFactoryManager;
import com.meerkat.notify.listener.AbstractMessageListener;
import com.meerkat.notify.retry.MessageRetry;
import com.meerkat.notify.store.MetaMessageRepositoryConsumerManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerEndpoint;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.listener.MessageListenerContainer;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public class RabbitConsumerConfigManager implements InitializingBean {
    private static final Logger logger = LoggerFactory.getLogger(RabbitConsumerConfigManager.class);
    @Autowired(required = false)
    private SimpleRabbitListenerContainerFactoryConfigurer containerFactoryConfigurer;
    @Autowired(required = false)
    private SimpleRabbitListenerContainerFactory defaultContainerFacotry;
    @Autowired
    private DefaultListableBeanFactory defaultListableBeanFactory;
    @Autowired
    private RabbitProperties rabbitProperties;

    @Autowired
    private ConnectionFactoryManager connectionFactoryManager;

    private MetaMessageRepositoryConsumerManager repositoryConsumerManager = MetaMessageRepositoryConsumerManager.getInstance();
    @Autowired
    private List<RabbitConsumerDailyConfig> rabbitConsumerDailyConfigs;
    @Autowired
    private List<RabbitConsumerProConfig> rabbitConsumerProConfigs;


    private List<RabbitConsumerDailyConfig> getConsumerDailyConfigs() {
        return rabbitConsumerDailyConfigs;
    }

    private List<RabbitConsumerProConfig> getConsumerProConfigs() {
        return rabbitConsumerProConfigs;
    }

    /**
     * 1,指定接受生产者的ip ，消费者会监听指定ip结尾的队列
     * 2，不指定生存者的ip ，消费者会监听daily的队列
     */
    private void configDailyListenerContainer() {
        List<RabbitConsumerDailyConfig> list = getConsumerDailyConfigs();
        if (CollectionUtils.isEmpty(list)) {
            logger.info("没有配置RabbitConsumerDailyConfig");
            return;
        }
        for (RabbitConsumerDailyConfig dailyConfig : list) {
            try {
                // 获取连接
                ConnectionFactory connectionFactory = connectionFactoryManager.getConnectionFactory(dailyConfig.virtualHost());
                DailyQueueConfig config = new DailyQueueConfig();
                // 将交换机，队列绑定到对应的virtualHost下
                config.bindExchangeAndQueue(connectionFactory, dailyConfig);
                //注册消息监听器
                registerMessageListenerContainer(dailyConfig, !Strings.isNullOrEmpty(dailyConfig.producerIp()), connectionFactory);
            } catch (Exception e) {
                logger.error("连接队列{}失败 e = {}", dailyConfig.queueName(), e);
            }

        }
    }


    /**
     * 线上环境
     */
    private void configProListenerContainer() {
        List<RabbitConsumerProConfig> list = getConsumerProConfigs();
        if (CollectionUtils.isEmpty(list)) {
            logger.info("没有配置RabbitConsumerProConfig");
            return;
        }
        for (RabbitConsumerProConfig consumerProConfig : list) {
            ConnectionFactory connectionFactory = connectionFactoryManager.getConnectionFactory(consumerProConfig.virtualHost());
            ProQueueConfig config = new ProQueueConfig();
            config.bindExchangeAndQueue(connectionFactory, consumerProConfig);
            registerMessageListenerContainer(consumerProConfig, false, connectionFactory);
        }
    }

    private void registerMessageListenerContainer(RabbitConsumerConfig consumerConfig, boolean withIp, ConnectionFactory connectionFactory) {
        String simpleName = consumerConfig.getClass().getSimpleName();
        Assert.notNull(consumerConfig.queueName(), simpleName + ".queueName不能为空");
        Assert.notNull(consumerConfig.messageListenerContainerName(), "messageListenerContainerName不能为空");
        Assert.notNull(consumerConfig.messageListener(), simpleName + ".messageListener not null");
        Assert.notNull(consumerConfig.ackMode(), simpleName + ".ackMode不能为空");
        MessageListenerContainer messageListenerContainer = createMessageListenerContainer(consumerConfig, withIp, connectionFactory);
        defaultListableBeanFactory.registerSingleton(consumerConfig.messageListenerContainerName(), messageListenerContainer);

        configMessageStore(consumerConfig);

    }

    private void configMessageStore(RabbitConsumerConfig rabbitConsumerConfig) {
        if (rabbitConsumerConfig.messageStoreType() == null || StringUtils.isEmpty(rabbitConsumerConfig.messageStoreTable())) {
            return;
        }
        //todo:消息自定义持久化安装
    }

    private MessageListenerContainer createMessageListenerContainer(RabbitConsumerConfig rabbitConsumerConfig, boolean withIp,
                                                                    ConnectionFactory connectionFactory) {

        AbstractMessageListener abstractMessageListener = rabbitConsumerConfig.messageListener();
        abstractMessageListener.setAckMode(rabbitConsumerConfig.ackMode());
        MessageRetry messageRetry = rabbitConsumerConfig.messageRetry();
        RetryTemplate retryTemplate = new RetryTemplate();
        retryTemplate.setRetryPolicy(messageRetry.retryPolicy());
        retryTemplate.setBackOffPolicy(messageRetry.backOffPolicy());
        abstractMessageListener.setRetryTemplate(retryTemplate);

        abstractMessageListener.registerRetryListener(rabbitConsumerConfig.getRetryListener());

        SimpleMessageListenerContainer container = getSimpleRabbitListenerContainer(connectionFactory, abstractMessageListener);
        if (rabbitConsumerConfig.preFetchCount() != null) {
            container.setPrefetchCount(rabbitConsumerConfig.preFetchCount());
        }
        if (rabbitConsumerConfig.minConcurrency() != null) {
            container.setConcurrentConsumers(rabbitConsumerConfig.minConcurrency());
        }
        if (rabbitConsumerConfig.ackMode() != null) {
            container.setAcknowledgeMode(rabbitConsumerConfig.ackMode());
        }
        container.setQueueNames(withIp ? rabbitConsumerConfig.queueName() + "." + getAddress() : rabbitConsumerConfig.queueName());
        return container;
    }

    /**
     * @param
     * @return
     */
    private SimpleMessageListenerContainer getSimpleRabbitListenerContainer(ConnectionFactory connectionFactory,
                                                                            AbstractMessageListener messageListener) {
        SimpleRabbitListenerContainerFactory factory = null;
        if (rabbitProperties.getVirtualHost().equals(connectionFactory.getVirtualHost())) {
            factory = defaultContainerFacotry;
        }
        if (factory == null && containerFactoryConfigurer != null) {
            factory = new SimpleRabbitListenerContainerFactory();
            containerFactoryConfigurer.configure(factory, connectionFactory);
        }
        if (factory != null) {
            SimpleRabbitListenerEndpoint endpoint = new SimpleRabbitListenerEndpoint();
            endpoint.setMessageListener(new MessageListenerAdapter(messageListener));
            endpoint.setId(String.valueOf(UUID.randomUUID()));
            return factory.createListenerContainer(endpoint);
        }
        return new SimpleMessageListenerContainer(connectionFactory);
    }

    private String getAddress() {
        try {
            return NetUtil.getLocalhost().getHostAddress();
        } catch (Exception e) {
            logger.error("获取本地ip 失败");
        }
        return "";
    }

    @Override
    public void afterPropertiesSet() {
        //线上环境配置
        if (EnvironmentUtil.isProd() || EnvironmentUtil.isStage()) {
            configProListenerContainer();
        } else {
            //日常环境配置
            configDailyListenerContainer();
        }
    }

    private class DailyQueueConfig {
        private void bindExchangeAndQueue(ConnectionFactory connectionFactory, RabbitConsumerDailyConfig rabbitConsumerDailyConfig) {
            RabbitAdmin rabbitAdmin = new RabbitAdmin(connectionFactory);
            Queue queue = buildQueue(rabbitConsumerDailyConfig);
            rabbitAdmin.declareQueue(queue);
            Set<String> producerIps = getProducerIps(rabbitConsumerDailyConfig.producerIp());
            rabbitAdmin.declareExchange(rabbitConsumerDailyConfig.exchange());
            for (String routingKey : rabbitConsumerDailyConfig.routingKeys()) {
                Binding binding = null;
                for (String ip : producerIps) {
                    if (rabbitConsumerDailyConfig.exchange() instanceof TopicExchange) {
                        binding = BindingBuilder.bind(queue).to((TopicExchange) rabbitConsumerDailyConfig.exchange()).with(getRoutingKey(ip,
                                routingKey));
                    }
                    if (rabbitConsumerDailyConfig.exchange() instanceof DirectExchange) {
                        binding = BindingBuilder.bind(queue).to((DirectExchange) rabbitConsumerDailyConfig.exchange()).with(getRoutingKey(ip,
                                routingKey));
                    }
                    if (rabbitConsumerDailyConfig.exchange() instanceof HeadersExchange) {
                        binding = BindingBuilder.bind(queue).to((HeadersExchange) rabbitConsumerDailyConfig.exchange()).where(getRoutingKey(ip,
                                routingKey)).exists();
                    }
                    if (rabbitConsumerDailyConfig.exchange() instanceof FanoutExchange) {
                        binding = BindingBuilder.bind(queue).to((FanoutExchange) rabbitConsumerDailyConfig.exchange());
                    }
                    if (binding == null) {
                        binding = BindingBuilder.bind(queue).to(rabbitConsumerDailyConfig.exchange()).with(getRoutingKey(ip, routingKey)).noargs();
                    }
                    rabbitAdmin.declareBinding(binding);
                }

            }
        }

        private Set<String> getProducerIps(String productorIds) {
            Set<String> ips = new HashSet<>();
            if (StrUtil.isEmpty(productorIds)) {
                ips.add("");
                return ips;
            }
            String[] ipArray = productorIds.split(";");
            for (String ip : ipArray) {
                if (StrUtil.isEmpty(ip)) {
                    continue;
                }
                ips.add(ip.trim());
            }
            return ips;
        }

        private String getRoutingKey(String producterIp, String routingKey) {
            if (StrUtil.isEmpty(producterIp)) {
                return routingKey;
            }

            return routingKey + "." + producterIp;
        }

        private Queue buildQueue(RabbitConsumerDailyConfig dailyConfig) {
            //指定生产者ip
            if (!StrUtil.isEmpty(dailyConfig.producerIp())) {
                String queueName = dailyConfig.queueName() + "." + RabbitConsumerConfigManager.this.getAddress();
                return new Queue(queueName, Boolean.FALSE, Boolean.FALSE, Boolean.TRUE, dailyConfig.arguments());
            }
            return new Queue(dailyConfig.queueName(), Boolean.TRUE, Boolean.FALSE, Boolean.FALSE, dailyConfig.arguments());
        }
    }

    private class ProQueueConfig {
        private void bindExchangeAndQueue(ConnectionFactory connectionFactory, RabbitConsumerProConfig rabbitConsumerProConfig) {
            RabbitAdmin rabbitAdmin = new RabbitAdmin(connectionFactory);
            Queue queue = buildQueue(rabbitConsumerProConfig);
            rabbitAdmin.declareQueue(queue);
            rabbitAdmin.declareExchange(rabbitConsumerProConfig.exchange());
            for (String routingKey : rabbitConsumerProConfig.routingKeys()) {
                Binding binding = null;
                if (rabbitConsumerProConfig.exchange() instanceof TopicExchange) {
                    binding = BindingBuilder.bind(queue).to((TopicExchange) rabbitConsumerProConfig.exchange()).with(routingKey);
                }
                if (rabbitConsumerProConfig.exchange() instanceof DirectExchange) {
                    binding = BindingBuilder.bind(queue).to((DirectExchange) rabbitConsumerProConfig.exchange()).with(routingKey);
                }
                if (rabbitConsumerProConfig.exchange() instanceof HeadersExchange) {
                    binding = BindingBuilder.bind(queue).to((HeadersExchange) rabbitConsumerProConfig.exchange()).where(routingKey).exists();
                }
                if (rabbitConsumerProConfig.exchange() instanceof FanoutExchange) {
                    binding = BindingBuilder.bind(queue).to((FanoutExchange) rabbitConsumerProConfig.exchange());
                }
                if (binding == null) {
                    binding = BindingBuilder.bind(queue).to(rabbitConsumerProConfig.exchange()).with(routingKey).noargs();
                }
                rabbitAdmin.declareBinding(binding);
            }

        }
    }

    private Queue buildQueue(RabbitConsumerProConfig proConfig) {
        String queueName = proConfig.queueName();
        return new Queue(queueName, Boolean.TRUE, false, Boolean.FALSE, null);
    }
}

