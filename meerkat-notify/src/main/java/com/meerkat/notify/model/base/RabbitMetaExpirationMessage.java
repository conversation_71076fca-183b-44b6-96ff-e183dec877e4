package com.meerkat.notify.model.base;


import javax.validation.constraints.NotNull;
import java.util.Date;

public final class RabbitMetaExpirationMessage{
    private MetaMessage rabbitMetaMessage;
    /**
     * 消息过期发送时间
     */
    @NotNull
    private Date expirationDate;
    private Boolean send;

    public Boolean getSend() {
        return send;
    }

    public void setSend(Boolean send) {
        this.send = send;
    }

    public MetaMessage getRabbitMetaMessage() {
        return rabbitMetaMessage;
    }

    public void setRabbitMetaMessage(MetaMessage rabbitMetaMessage) {
        this.rabbitMetaMessage = rabbitMetaMessage;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }
}
