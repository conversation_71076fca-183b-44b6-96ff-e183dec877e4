package com.meerkat.notify.model.base;

import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * 这个是所有的消息的共同的字段
 */
public class MetaMessage implements Serializable {

    private static final long serialVersionUID = -528747048157149257L;

    private String msgId;
    /**
     * 交换机
     */
    @NotNull
    private String exchange;
    /**
     * 路由键
     */
    @NotNull
    private String routingKey;
    private Boolean autoTrigger;

    /**
     * 是否到达交换机
     */
    private Boolean isArriveExchange;

    /**
     * 是否到达队列
     */
    private Boolean isArriveQueue;
    @NotNull
    private Payload payload;

    /**
     * 消费否成功
     */
    private Boolean isConsumerSuccess;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 失败重试次数
     */
    private int retryTime;
    @NotNull
    private String virtualHost;
    /**
     * 业务到期时间
     * eg: 卡到
     */
    private Date bizExpirationDate;
    /**1-255
     * 消息优先级
     * 值越大优先级越高
     */
    private Integer priority;

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Date getBizExpirationDate() {
        return bizExpirationDate;
    }

    public void setBizExpirationDate(Date bizExpirationDate) {
        this.bizExpirationDate = bizExpirationDate;
    }

    public String getVirtualHost() {
        return virtualHost;
    }

    public void setVirtualHost(String virtualHost) {
        this.virtualHost = virtualHost;
    }

    public Payload getPayload() {
        return payload;
    }

    public void setPayload(Payload payload) {
        this.payload = payload;
    }

    public Boolean getConsumerSuccess() {
        return isConsumerSuccess;
    }

    public void setConsumerSuccess(Boolean consumerSuccess) {
        isConsumerSuccess = consumerSuccess;
    }

    public int getRetryTime() {
        return retryTime;
    }

    public void setRetryTime(int retryTime) {
        this.retryTime = retryTime;
    }

    public Boolean getAutoTrigger() {
        return autoTrigger;
    }

    public void setAutoTrigger(Boolean autoTrigger) {
        this.autoTrigger = autoTrigger;
    }

    public Boolean getArriveExchange() {
        return isArriveExchange;
    }

    public void setArriveExchange(Boolean arriveExchange) {
        isArriveExchange = arriveExchange;
    }

    public Boolean getArriveQueue() {
        return isArriveQueue;
    }

    public void setArriveQueue(Boolean arriveQueue) {
        isArriveQueue = arriveQueue;
    }

    public String getExchange() {
        return exchange;
    }

    public void setExchange(String exchange) {
        this.exchange = exchange;
    }

    public String getRoutingKey() {
        return routingKey;
    }

    public void setRoutingKey(String routingKey) {
        this.routingKey = routingKey;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }


    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;

    }

    @Override
    public String toString() {
        return "MetaMessage{" +
                "msgId='" + msgId + '\'' +
                ", exchange='" + exchange + '\'' +
                ", routingKey='" + routingKey + '\'' +
                ", autoTrigger=" + autoTrigger +
                ", isArriveExchange=" + isArriveExchange +
                ", isArriveQueue=" + isArriveQueue +
                ", payload=" + payload +
                ", isConsumerSuccess=" + isConsumerSuccess +
                ", createDate=" + createDate +
                ", retryTime=" + retryTime +
                '}';
    }

    public MetaMessage() {
        this.msgId = UUID.randomUUID().toString().replace("-", "");
        this.autoTrigger = false;
        //默认已经到达交换机
        this.isArriveExchange = true;
        //默认已经到达队列
        this.isArriveQueue = true;
        this.isConsumerSuccess = false;
        this.createDate = new Date();
        //当前重试次数
        this.retryTime = 0;
    }
}
