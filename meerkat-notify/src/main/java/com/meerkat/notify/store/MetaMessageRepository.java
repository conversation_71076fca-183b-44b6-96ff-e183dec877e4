package com.meerkat.notify.store;

import com.meerkat.notify.model.base.MetaMessage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Map;

/**
 * rabbit 消息体存储的顶级接口 默认没有
 * @param <R>
 */
public interface MetaMessageRepository {
    /**
     * 1，不存在则保存
     * 2，存在则更新
     *
     * @param rabbitMetaMessage
     */
    void save(MetaMessage rabbitMetaMessage);

    MetaMessage getRabbitMetaMessage(String messageId, Class<? extends MetaMessage> clazz);


    void updateFirst(Map<String, Object> updateValue, String messageId);

    Page<MetaMessage> getRabbitMetaMessagesByPage(Map<String, Object> query, Pageable page);

    void deleteByCreateDate(int days);
}
