package com.meerkat.notify.listener;


import cn.hutool.json.JSONUtil;
import com.meerkat.common.exception.BizException;
import com.meerkat.notify.exception.ConsumerErrorInfoEnum;
import com.meerkat.notify.model.base.FailObject;
import com.meerkat.notify.model.base.MetaMessage;
import com.meerkat.notify.model.base.SuccessObject;
import com.meerkat.notify.util.RabbitMetaMessageUtils;
import com.meerkat.notify.retry.retryListener.AbstractRetryListener;
import com.meerkat.notify.retry.retryListener.DefaultConsumerRetryListener;
import com.meerkat.notify.store.MetaMessageRepositoryConsumerManager;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.retry.support.RetryTemplate;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息监听器
 */
public abstract class AbstractMessageListener implements ChannelAwareMessageListener, RabbitMessageResolvable {
    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    protected MetaMessageRepositoryConsumerManager repositoryManager = MetaMessageRepositoryConsumerManager.getInstance();
    private RetryTemplate retryTemplate = null;
    private AcknowledgeMode ackMode = AcknowledgeMode.AUTO;
    private List<AbstractRetryListener> retryListeners = new ArrayList<>();
    public AbstractMessageListener(){
        retryListeners.add(new DefaultConsumerRetryListener());
    }
    public final void setRetryTemplate(RetryTemplate retryTemplate) {
        this.retryTemplate = retryTemplate;
    }
    public final void setAckMode(AcknowledgeMode ackMode) {
        this.ackMode = ackMode;
    }
    public final void registerRetryListener(AbstractRetryListener abstractRetryListener) {
        if (abstractRetryListener == null) {
            return;
        }
        retryListeners.add(abstractRetryListener);
    }
    /**
     * @return true 需要重试  false 不需要
     * @throws BizException
     */
    public abstract boolean receiveMessage(MetaMessage rabbitMetaMessage);

    /**
     * @param successObject
     * @return
     */
    public abstract void consumerSuccess(SuccessObject successObject);

    /**
     * @param failObject
     * @return true 需要重试  false 不需要重试
     */
    public abstract boolean consumeFail(FailObject failObject);

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        MetaMessage rabbitMetaMessage = toMetaMessage(message);
        if (rabbitMetaMessage != null) {
            Throwable throwable = null;
            int[] retry = new int[]{0};
            try {
                if (retryTemplate != null) {
                    retryTemplate.execute(context -> {
                        if (context.getRetryCount() != 0) {
                            notifyRetryError(rabbitMetaMessage,context.getRetryCount(), context.getLastThrowable());
                            retry[0]= context.getRetryCount() ;
                        }
                        return innerReceiveMessage(rabbitMetaMessage);
                    });
                } else {
                    innerReceiveMessage(rabbitMetaMessage);
                }

            }catch (Exception e) {
                throwable = e;
            }
            notifyRetryFinish(rabbitMetaMessage, retry[0], throwable);
        } else {
            logger.error("rabbitMetaMessage is null,  message = {}", JSONUtil.toJsonStr(message));
        }

        if (AcknowledgeMode.MANUAL.equals(ackMode)) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
        }
    }
    private MetaMessage toMetaMessage(Message message) {
        String routingKey = message.getMessageProperties().getReceivedRoutingKey();
        MetaMessage rabbitMetaMessage = RabbitMetaMessageUtils.convertRabbitMetaMessage(message, resolveMessageClass(routingKey), resolvePayloadClass(routingKey));
        if (rabbitMetaMessage != null) {
            return rabbitMetaMessage;
        }
        try {
            return RabbitMetaMessageUtils.getRabbitMetaMessageWithPayload(message);
        } catch (UnsupportedEncodingException e) {
            logger.error("getRabbitMetaMessageWithPayload",e);
            return null;
        }
    }
    private void notifyRetryFinish(MetaMessage rabbitMetaMessage, int retryCount, Throwable throwable) {
        Map<String, Object> map = new HashMap<>();
        map.put("from", this);
        retryListeners.forEach(retry -> {
            try {
                retry.retryFinish(rabbitMetaMessage, retryCount, throwable,map);
            }catch (Exception e) {
                logger.error("retryListenerName = {} notifyRetryFinish 处理失败 e= {}",retry.getClass().getSimpleName(),e);

            }
        });
    }
    private void notifyRetryError(MetaMessage rabbitMetaMessage, int retryCount, Throwable throwable) {
        retryListeners.forEach(retry-> {
            try {
                retry.retryError(rabbitMetaMessage, retryCount, throwable);
            }catch (Exception e) {
                logger.error("retryListenerName = {} notifyRetryError  处理失败 e= {}",retry.getClass().getSimpleName(),e);
            }
        });
    }
    private MetaMessage innerReceiveMessage(MetaMessage rabbitMetaMessage) {
        boolean retry;
        boolean consumerSuccess = true;
        long begin = System.currentTimeMillis();
        Throwable exception = null;
        try {
            save(rabbitMetaMessage);
            // 业务逻辑处理
            retry = receiveMessage(rabbitMetaMessage);
        } catch (Throwable e) {
            exception = e;
            if (e instanceof BizException) {
                logger.warn("消费失败 warn msgId = {} e= {}",rabbitMetaMessage.getMsgId(), e);

            } else {
                logger.error("消费失败 error msgId = {} e= {}",rabbitMetaMessage.getMsgId(), e);
            }
            try {
                retry = consumeFail(converFailObject(rabbitMetaMessage, e));
            }catch (Throwable throwable) {
                logger.error("执行 consumerFail 失败 msgId = {} e= {}",rabbitMetaMessage.getMsgId(), throwable );
                retry = true;
                exception = throwable;
            }
            consumerSuccess = false;
        }
        // 重试
        if (retry) {
            BizException bizException = new BizException(ConsumerErrorInfoEnum.RETRY, this.getClass().getName());
            Map<String, Object> ext = new HashMap<>();
            ext.put("currentMessageListenerClass", this.getClass());
            ext.put("rabbitMetaMessage", rabbitMetaMessage);
            bizException.setExtInfo(ext);
            bizException.initCause(exception);
            throw bizException;
        }
        // 消费成功
        if (consumerSuccess) {
            try {
                consumerSuccess(converSuccessObject(rabbitMetaMessage));

            }catch (Throwable throwable) {
                logger.warn("执行consumerSuccess messageId ={} consumerSuccess 失败 e ={}",rabbitMetaMessage.getMsgId(), throwable);
            }
            // 更新消费成功
            try {
                updateConsumerSuccess(rabbitMetaMessage);
            }catch (Throwable throwable) {
                logger.warn("更新消费成功失败 messageId = {} e = {}",rabbitMetaMessage.getMsgId(), throwable);
            }
        }
        if (rabbitMetaMessage != null) {
            logger.info("msgId = {},domainId = {}, 执行完成 所耗时间= {}",rabbitMetaMessage.getMsgId(), rabbitMetaMessage.getPayload().getDomainId(), System.currentTimeMillis() - begin);
        }
        return rabbitMetaMessage;
    }

    private FailObject converFailObject(MetaMessage rabbitMetaMessage, Throwable throwable) {
        FailObject object = new FailObject();
        object.setRabbitMetaMessage(rabbitMetaMessage);
        object.setThrowable(throwable);
        return object;
    }

    private SuccessObject converSuccessObject(MetaMessage rabbitMetaMessage) {
        SuccessObject object = new SuccessObject();
        object.setRabbitMetaMessage(rabbitMetaMessage);
        return object;

    }
    private void updateConsumerSuccess(MetaMessage rabbitMetaMessage) {
        if (rabbitMetaMessage != null) {
            Map<String, Object> map = new HashMap<>();
            map.put("isConsumerSuccess", true);
            repositoryManager.update(map, rabbitMetaMessage.getMsgId(), this.getClass());
        }
    }
    protected void save(MetaMessage rabbitMetaMessage) {
        repositoryManager.save(rabbitMetaMessage, this.getClass());
    }


}
