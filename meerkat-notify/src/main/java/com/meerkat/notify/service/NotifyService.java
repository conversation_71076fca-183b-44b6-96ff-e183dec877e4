package com.meerkat.notify.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.meerkat.common.api.CommonResult;
import com.meerkat.notify.connection.RabbitTemplateManager;
import com.meerkat.notify.model.base.MetaMessage;
import com.meerkat.notify.producer.ProducerMessageManager;
import com.meerkat.notify.store.MetaMessageRepositoryProducterManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;


/**
 * 消息服务
 */
public class NotifyService {
    public final static String PAYLOAD_TYPE = "payloadType";
    private final MetaMessageRepositoryProducterManager manager = MetaMessageRepositoryProducterManager.getInstance();
    @Autowired
    private RabbitTemplateManager rabbitTemplateManager;

    private Logger logger = LoggerFactory.getLogger(NotifyService.class);

    /**
     * 立即发送消息
     *
     * @param rabbitMetaMessage 消息实体
     */
    public CommonResult sendMessage(MetaMessage rabbitMetaMessage) {
        return sendMessage(rabbitMetaMessage, Boolean.FALSE);
    }

    public CommonResult sendMessage(MetaMessage rabbitMetaMessage, boolean saveMessage) {
        String virtualHost = getVirtualHost(rabbitMetaMessage);
        Assert.notNull(virtualHost, "virtualHost not null");
        rabbitMetaMessage.setVirtualHost(virtualHost);
        RabbitTemplate rabbitTemplate = rabbitTemplateManager.getRabbitTemplate(virtualHost);
        return sendMessage(rabbitMetaMessage, rabbitTemplate, saveMessage);
    }

    public CommonResult sendMessage(MetaMessage rabbitMetaMessage, RabbitTemplate rabbitTemplate, boolean save) {
        if (save) {
            save(rabbitMetaMessage);
        }
        try {
            ProducerMessageManager.hold(rabbitMetaMessage);
            rabbitTemplate.send(rabbitMetaMessage.getExchange(), rabbitMetaMessage.getRoutingKey(), convertMessage(rabbitMetaMessage), new CorrelationData(rabbitMetaMessage.getMsgId()));
        } catch (Exception e) {
            logger.error("消息发送失败 domainId = {} rabbitMessage = {} e= {}", rabbitMetaMessage.getPayload().getDomainId(), JSONUtil.toJsonStr(rabbitMetaMessage), e);
            throw e;
        } finally {
            ProducerMessageManager.clear();
        }
        return CommonResult.success();
    }

    private String getVirtualHost(MetaMessage rabbitMetaMessage) {
        if (StrUtil.isEmpty(rabbitMetaMessage.getVirtualHost())) {
            return rabbitTemplateManager.getDefaultVirtualHost();
        }
        return rabbitMetaMessage.getVirtualHost();
    }

    public CommonResult sendExpiredMessage(MetaMessage rabbitMetaMessage) {
        return CommonResult.success();
    }

    private void save(MetaMessage metaMessage) {
        manager.save(metaMessage);
    }

    /**
     * @param metaMessage
     * @return
     */
    private Message convertMessage(MetaMessage metaMessage) {
        Jackson2JsonMessageConverter converter = new Jackson2JsonMessageConverter();
        MessageProperties properties = new MessageProperties();
        properties.setMessageId(metaMessage.getMsgId());
        properties.setPriority(metaMessage.getPriority());
        properties.getHeaders().put(PAYLOAD_TYPE, metaMessage.getPayload().getClass().getCanonicalName());
        return converter.toMessage(metaMessage, properties);
    }


}
