package com.meerkat.user.exception;

import com.meerkat.common.api.BizCode;

/**
 * <p>
 * 用户帐户异常
 * </p>
 *
 * <AUTHOR>
 * @since 2022/2/22 11:12
 */
public enum UserSubBizException implements BizCode {

    /**
     * 未找到用户帐户
     */
//    USER_SUB_NOT_FOUND("EX_USER_SUB_001_01", "未找到用户帐户"),

    /**
     * 用户帐户新增时为空
     */
    USER_SUB_IS_NULL("EX_USER_SUB_001_02", "用户帐户新增时为空"),
    /**
     * 登录账户已存在
     */
    USER_SUB_IN("EX_USER_SUB_001_03", "登录账户已存在"),
    ;

    UserSubBizException(String code, String message) {
        this.code = code;
        this.message = message;
    }

    private final String code;
    private final String message;

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}
