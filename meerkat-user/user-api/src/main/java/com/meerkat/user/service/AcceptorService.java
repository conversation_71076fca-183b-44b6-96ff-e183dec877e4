package com.meerkat.user.service;

import com.meerkat.common.db.PageView;
import com.meerkat.user.model.Acceptor;
import com.meerkat.user.param.AcceptorQuery;
import com.meerkat.user.model.AcceptorQueryCRM;
import com.meerkat.user.model.FailAcceptor;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/12/21 3:40 下午
 * @description：履约人Service层服务接口
 */
public interface AcceptorService {

    /**
     * 查询履约人
     * @param query
     * @return
     */
    PageView<Acceptor> acceptorFuzzyQueryInCRM(AcceptorQuery query);

    /**
     * 获取履约人分组
     * @param companyId
     * @return
     */
    List<String> listAcceptorGroup(Long companyId);


    /**
     * 查询CRM列表
     *
     * @param acceptorQueryCRM
     * @return
     */
    List<Acceptor> getAcceptorListInCRM(AcceptorQueryCRM acceptorQueryCRM);

    /**
     * 查询C端列表
     *
     * @param operatorId 操作人/客户 id
     * @return 履约人列表
     */
    List<Acceptor> getAcceptorListInCustomer(Long operatorId, Long chorgaId, Integer chorgaType);

    /**
     * 根据单位Id获取CRM四要素信
     *
     * @return credentialNo证件号码, addAccountType 证件类型, organizationId 机构id, companyId 单位id
     */
    List<Acceptor> getUniqueListInCRM(Long companyId);

    /**
     * 根据id结合查询列表
     *
     * @param ids
     * @return
     */
    List<Acceptor> getAcceptorListByIds(List<Long> ids);

    /**
     * 根据四要素定位Id
     *
     * @param credentialNo   证件号码
     * @param companyId      单位id
     * @param chorgaId       站点id
     * @param credentialType 证件类型
     * @return
     */
    Long getId(String credentialNo, Long companyId, Long chorgaId, Integer credentialType);

    /**
     * 根据Id获取Acceptor
     *
     * @param Id
     * @return
     */
    Acceptor getAcceptorById(Long Id);

    /**
     * 单条数据进行插入
     *
     * @param acceptor 返回值为插入数据的自增id
     */
    Long addAcceptor(Acceptor acceptor);

    /**
     * 批量插入
     */
    List<FailAcceptor> batchAddAcceptor(List<Acceptor> acceptorList);

    /**
     * 逻辑删除Acceptor
     *
     * @param ids 要删除的id
     */
    void deleteAcceptor(List<Long> ids);

    /**
     * 更新信息
     *
     * @param acceptor 传输对象
     */
    void updateAcceptor(Acceptor acceptor);

    /**
     * 更新已操作标识
     *
     * @param id              id
     * @param appointmentFlag 预约标识 false/0未预约 true/1已预约
     */
    void updateAppointment(Long id, Boolean appointmentFlag);

    /**
     * 批量更新已操作
     *
     * @param ids              id
     * @param appointmentFlag 预约标识 false/0未预约 true/1已预约
     * @return void
     * <AUTHOR>
     * @date 2022/7/14 17:20
     */
    public void batchUpdateOperation(List<Long> ids, Boolean appointmentFlag);

    public List<Acceptor> listAcceptorByMobile(String mobile, Long chorgaId, Integer chorgaType, Integer source);
}
