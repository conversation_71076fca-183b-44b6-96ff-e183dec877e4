package com.meerkat.user.repository.mapper;

import com.meerkat.user.repository.dataobj.AcceptorExportDO;
import com.meerkat.user.repository.dataobj.param.AcceptorExportFuzzyQueryDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/12/22 11:14 上午
 * @description：tb_acceptor_export表Mapper接口
 */
@Mapper
public interface AcceptorExportMapper {

    /**
     * 根据id 取单条记录
     * @param id id
     * @return 数据
     */
    AcceptorExportDO getById(@Param("id") Long id);

    /**
     * 根据id取多条记录
     * @param ids
     * @return
     */
    List<AcceptorExportDO> getByIds(List<Long> ids);

    /**
     * 根据条件取列表
     * @param acceptorExportDO do
     * @return 数据列表
     */
    List<AcceptorExportDO> list(AcceptorExportDO acceptorExportDO);

    /**
     * 模糊查询
     * @param acceptorExportFuzzyQueryDO
     * @return
     */
    List<AcceptorExportDO> fuzzyList(AcceptorExportFuzzyQueryDO acceptorExportFuzzyQueryDO);

    /**
     * 模糊查询
     * @param acceptorExportFuzzyQueryDO
     * @return
     */
    List<Long> selectFuzzy(AcceptorExportFuzzyQueryDO acceptorExportFuzzyQueryDO);

    /**
     * 根据id更新
     * @param acceptorExportDO do
     * @return 操作行数
     */
    Integer update(AcceptorExportDO acceptorExportDO);

    /**
     * 插入一条数据
     * @param acceptorExportDO do
     * @return 插入数据的Id
     */
    Long insert(AcceptorExportDO acceptorExportDO);

    /**
     * 批量插入数据
     * @param list list
     * @return 数据变动条数
     */
    Integer insertBatch(List<AcceptorExportDO> list);

    /**
     * 更改 Action操作行为
     * @param id id
     * @param action 行为
     */
    void updateAction(@Param("id") Long id, @Param("action") Integer action);

    /**
     * 更改状态
     * @param id
     * @param status
     */
    void updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新message
     * @param id id
     * @param message message
     */
    void updateMessage(@Param("id") Long id, @Param("message") String message);

    /**
     * 逻辑删除
     * @param list
     */
    void updateDeleted(List<Long> list);

    /**
     * 批量更新操作信息
     * @param id id列表
     * @param action 操作行为
     * @param status 状态
     * @param message 信息列表
     */
    void updateActionInfo(@Param("id") Long id, @Param("action") Integer action,
                               @Param("status") Integer status, @Param("message") String message);

    /**
     * 批量更新操作信息
     * @param list 数据
     * @param action 操作行为
     * @param status 状态
     */
    void updateActionInfoBatch(@Param("list") List<AcceptorExportDO> list,
                               @Param("action") Integer action, @Param("status") Integer status);

    /**
     * 批量更新成功
     * @param ids id列表
     * @param action 操作行为
     * @param message 信息列表
     */
    void updateSuccessBatch(@Param("ids") List<Long> ids, @Param("action") Integer action, @Param("message") String message);
}
