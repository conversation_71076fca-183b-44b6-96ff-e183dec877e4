package com.meerkat.user.service.impl;

import com.google.common.collect.Sets;
import com.meerkat.common.enums.SystemEnum;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.user.model.Menu;
import com.meerkat.user.model.Role;
import com.meerkat.user.model.RoleMenuRelation;
import com.meerkat.user.param.MenuQuery;
import com.meerkat.user.repository.dataobj.MenuDO;
import com.meerkat.user.repository.dataobj.param.MenuDaoQuery;
import com.meerkat.user.repository.mapper.MenuMapper;
import com.meerkat.user.service.MenuReaderService;
import com.meerkat.user.service.RoleMenuRelationService;
import com.meerkat.user.service.RoleReaderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * MenuReaderService实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2021/12/22 11:52
 */
@Service
public class MenuReaderServiceImpl implements MenuReaderService {

    @Autowired
    private MenuMapper menuMapper;

    @Autowired
    private RoleMenuRelationService roleMenuRelationService;

    @Autowired
    private RoleReaderService roleReaderService;

    @Override
    public List<Menu> load(MenuQuery menuQuery, boolean isTree, boolean isOpen) {
        List<MenuDO> menuDOS = menuMapper.selectByMenuDaoQuery(CopyUtil.copy(menuQuery, MenuDaoQuery.class));
        if (CollectionUtils.isEmpty(menuDOS)) {
            return new ArrayList<>();
        }
        Set<Long> menuIds = Sets.newHashSet();
        menuDOS.forEach(e->{
            menuIds.add(e.getId());
            menuIds.add(e.getParentId());
        });
        List<Long> menuIdList = new ArrayList<>(menuIds);
        if (isTree) {
            //构建完整树
            return loadByIds(menuIdList, isTree, isOpen);
        } else {
            return CopyUtil.copyList(menuDOS, Menu.class);
        }
    }

    @Override
    public List<Menu> loadByIds(List<Long> ids, boolean isTree, boolean isOpen) {
        List<MenuDO> menuDOS = menuMapper.selectByIds(ids);
        if (CollectionUtils.isEmpty(menuDOS)) {
            return new ArrayList<>();
        }
        List<Menu> menus = CopyUtil.copyList(menuDOS, Menu.class);
        if (isOpen){
            menus = menus.stream().filter(e->e.getStatus() == 0).collect(Collectors.toList());
        }
        if (isTree) {
            return buildTree(menus);
        } else {
            return menus;
        }
    }

    @Override
    public List<Menu> loadMenu(Long userId, Long organizationId, SystemEnum systemEnum, boolean isTree) {
        List<Menu> menus = new ArrayList<>();
        List<Long> roleIds = roleReaderService.loadRoleIdByUserIdAndOrgaId(userId, organizationId, systemEnum);
        List<Role> roles = roleReaderService.loadByIds(roleIds);
        roleIds = roles.stream()
                .map(Role::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(roleIds)){
            return menus;
        }
        //超级管理员获取所有菜单
        if (roleIds.contains(1L)){
            if (isTree){
                return buildTree(load(systemEnum, true));
            }else {
                return load(systemEnum, true);
            }
        }
        List<RoleMenuRelation> roleMenuRelations = roleMenuRelationService.loadByRoleIds(roleIds);
        List<Long> menuIds = roleMenuRelations.stream()
                .map(RoleMenuRelation::getMenuId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(menuIds)) {
            return menus;
        }
        return loadByIds(menuIds, isTree, true);
    }

    @Override
    public List<Menu> loadMenuByRole(Long roleId, Long organizationId, boolean isTree, boolean isOpen) {
        //可展示的所有菜单
        Role role = roleReaderService.loadOrgaAdminRole(organizationId);
        List<Menu> allMenus = loadMenu(role.getId(), isTree, isOpen);

        //获取当前角色的menus
        List<Menu> checkMenus = loadMenu(roleId, isTree, isOpen);
        signMenus(allMenus, checkMenus);
        return buildTree(allMenus);
    }

    @Override
    public List<Menu> loadMenuByRoleAdmin(Long roleId, Long organizationId, boolean isTree, boolean isOpen) {
        List<Menu> allMenus = load(SystemEnum.CRM, true);
        //获取当前角色的menus
        List<Menu> checkMenus = loadMenu(roleId, isTree, isOpen);
        signMenus(allMenus, checkMenus);
        return buildTree(allMenus);
    }

    @Override
    public List<Menu> load(SystemEnum systemEnum, boolean isOpen) {
        MenuDaoQuery menuDaoQuery = new MenuDaoQuery();
        menuDaoQuery.setSystem(systemEnum.getCode());
        if (isOpen) {
            menuDaoQuery.setStatus(0);
        }
        List<MenuDO> menuDOS = menuMapper.selectByMenuDaoQuery(menuDaoQuery);
        return CopyUtil.copyList(menuDOS, Menu.class);
    }

    @Override
    public Set<String> loadPerms(Long userId, SystemEnum systemEnum) {
        List<Menu> menus = loadMenu(userId, null, systemEnum, false);
        return menus.stream().map(Menu::getPerms).collect(Collectors.toSet());
    }

    /**
     * 获取角色的菜单
     */
    @Override
    public List<Menu> loadMenu(Long roleId, boolean isTree, boolean isOpen) {
        List<Menu> menus = new ArrayList<>();
        List<RoleMenuRelation> roleMenuRelations = roleMenuRelationService.loadByRoleIds(Collections.singletonList(roleId));
        if (CollectionUtils.isEmpty(roleMenuRelations)) {
            return menus;
        }
        List<Long> menuIds = roleMenuRelations.stream()
                .map(RoleMenuRelation::getMenuId)
                .collect(Collectors.toList());
        return loadByIds(menuIds, isTree, isOpen);

    }

    @Override
    public List<Menu> loadMenuByRole(Long roleId, SystemEnum systemEnum, boolean isTree, boolean isOpen) {
        //可展示的所有菜单
        List<Menu> allMenus = load(systemEnum, isOpen);
        //获取当前角色的menus
        List<Menu> checkMenus = loadMenu(roleId, isTree, isOpen);
        signMenus(allMenus, checkMenus);
        return buildTree(allMenus);
    }

    private void signMenus(List<Menu> allMenus, List<Menu> checkMenus) {
        List<Long> checkIdList = checkMenus.stream().map(Menu::getId).collect(Collectors.toList());
        allMenus.forEach(e -> {
            if (checkIdList.contains(e.getId())) {
                e.setIsChecked(1);
            }else {
                e.setIsChecked(0);
            }
        });
    }

    /**
     * 构建菜单树结构
     */
    private List<Menu> buildTree(List<Menu> menus) {
        if (CollectionUtils.isEmpty(menus)) {
            return menus;
        }
        //目标列表
        List<Menu> returnList = new ArrayList<Menu>();
        List<Menu> topMenus = menus.stream()
                .filter(e -> e.getParentId() == 0)
                .collect(Collectors.toList());
        //从顶级节点开始处理
        topMenus.stream()
                .sorted(Comparator.comparingInt(Menu::getSequence))
                .forEach(e -> {
                    recursionFn(menus, e);
                    returnList.add(e);
                });
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<Menu> list, Menu t) {
        // 得到子节点列表
        List<Menu> childList = getChildList(list, t);
        t.setChildren(childList);
        childList.stream()
                .filter(e -> hasChild(list, e))
                .sorted(Comparator.comparingInt(Menu::getSequence))
                .forEach(e -> {
                    recursionFn(list, e);
                });
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<Menu> list, Menu t) {
        return list.stream().anyMatch(e -> e.getParentId().equals(t.getId()));
    }

    /**
     * 得到子节点列表
     */
    private List<Menu> getChildList(List<Menu> list, Menu t) {
        return list.stream()
                .filter(e -> e.getParentId().equals(t.getId()))
                .sorted(Comparator.comparingInt(Menu::getSequence))
                .collect(Collectors.toList());
    }
}
