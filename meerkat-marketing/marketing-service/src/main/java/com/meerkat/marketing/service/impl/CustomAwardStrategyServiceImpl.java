package com.meerkat.marketing.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.meerkat.marketing.enums.ActivityCouponTypeEnum;
import com.meerkat.marketing.enums.AwardTypeEnum;
import com.meerkat.marketing.model.*;
import com.meerkat.marketing.param.ActivityUserAwardsReceiveParam;
import com.meerkat.marketing.param.ActivityUserAwardsSendParam;
import com.meerkat.marketing.service.ActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 自定义活动奖品
 * 一般为抽奖内定
 *
 * <AUTHOR>
 * @date 2022/09/29 11:49
 */
@Service
public class CustomAwardStrategyServiceImpl extends BaseAwardStrategyServiceImpl {

    @Autowired
    private ActivityService activityService;

    @Override
    public int getType() {
        return AwardTypeEnum.CUSTOM.getCode();
    }


    @Override
    public void receive(ActivityUserAwardsReceiveParam param) {
        //暂无领取逻辑，假的
    }

    @Override
    public void send(ActivityUserAwardsSendParam param) {
        //暂无发放逻辑，活动表里内定
    }

    @Override
    public void userAward(Long awardId, Long userId) {
        //暂无使用逻辑，假的
    }

    @Override
    public List<ActivityAwards> getAwardList(Long userId, List<ActivityAwards> activityAwardsList) {
        if (activityAwardsList == null || activityAwardsList.isEmpty()) {
            return activityAwardsList;
        }

        //获取活动id
        Long activityId = activityAwardsList.get(0).getActivityId();
        Activity activity = activityService.load(activityId.intValue());

        boolean isIng = activity.getEndTime().isBefore(LocalDateTime.now());

        Map<Long, ActivityAwards> map = new HashMap<>();
        for (ActivityAwards activityAwards : activityAwardsList) {
            map.put(activityAwards.getId(), activityAwards);
        }

        //获取用户奖品
        Map<Long, ActivityUserAwards> userAwardsMap = new HashMap<>();
        if (userId != null) {
            List<ActivityUserAwards> userAwardsList = activityUserAwardsService.getListByUser(activityId, userId);
            if (!userAwardsList.isEmpty()) {
                userAwardsMap = userAwardsList.stream().collect(Collectors.toMap(ActivityUserAwards::getAwardId, Function.identity(), (a, b) -> a));
            }

        }

        List<ActivityAwards> resultList = new ArrayList<>();

        for (Long awardId : map.keySet()) {
            ActivityAwards activityAwards = map.get(awardId);
            ActivityUserAwards activityUserAwards = userAwardsMap.get(awardId);

            ActivityAwardsReceiver receiver = new ActivityAwardsReceiver();

            // 默认未获取的状态
            receiver.setStatus(isIng ? ActivityCouponTypeEnum.TYPE_2.getCode() : ActivityCouponTypeEnum.TYPE_0.getCode());


            //真实用户才会有奖品信息
            if (activityUserAwards != null) {
                receiver.setIsReminded(activityUserAwards.getIsReminded());
                ActivityUserAwardsExt ext = JSONObject.parseObject(activityUserAwards.getExt(), ActivityUserAwardsExt.class);
                receiver.setCustomAwardTitle(ext.getCustomAwardTitle());
                receiver.setCustomAwardImg(ext.getCustomAwardImg());
            }


            activityAwards.setReceiver(receiver);
            resultList.add(activityAwards);
        }

        return resultList;
    }
}
