package com.meerkat.marketing.mapper.dataobj.query;

import java.io.Serializable;
/**
 * <p>
 * 活动表,查询条件
 * </p>
 *
 * <AUTHOR>
 * @since 2022/09/08 16:00:14
 */
public class ActivityDaoQuery {

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String title;

    /**
     * 活动状态
     */
    private Integer state;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
}