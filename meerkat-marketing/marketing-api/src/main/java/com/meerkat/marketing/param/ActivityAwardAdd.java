package com.meerkat.marketing.param;

/**
 * <AUTHOR>
 * @Since 2022-11-03
 * @Description 活动奖品新增编辑参数
 */
public class ActivityAwardAdd {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;
    /**
     * 接收者类型 0 自定义 1 优惠券 2 活动
     */
    private Integer receiverType;

    /**
     * 接收者id 根据类型定义
     */
    private String receiverId;

    /**
     * 接收者名称
     */
    private String receiverTitle;

    /**
     * 奖品标识
     */
    private String code;

    /**
     * 其他信息
     */
    private String ext;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 更新人id
     */
    private Long modifiedId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Integer getReceiverType() {
        return receiverType;
    }

    public void setReceiverType(Integer receiverType) {
        this.receiverType = receiverType;
    }

    public String getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(String receiverId) {
        this.receiverId = receiverId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getReceiverTitle() {
        return receiverTitle;
    }

    public void setReceiverTitle(String receiverTitle) {
        this.receiverTitle = receiverTitle;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Long getModifiedId() {
        return modifiedId;
    }

    public void setModifiedId(Long modifiedId) {
        this.modifiedId = modifiedId;
    }
}
