package com.meerkat.marketing.param;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Since 2022-09-28
 * @Description 助力活动详情查询入参
 */
public class HelpActivityQuery {

    @NotNull(message = "活动id不能为空")
    private Long activityId;

    private Long userId;

    private Long helpUserId;

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getHelpUserId() {
        return helpUserId;
    }

    public void setHelpUserId(Long helpUserId) {
        this.helpUserId = helpUserId;
    }

    @Override
    public String toString() {
        return "HelpActivityQuery{" +
                "activityId=" + activityId +
                ", userId=" + userId +
                '}';
    }
}
