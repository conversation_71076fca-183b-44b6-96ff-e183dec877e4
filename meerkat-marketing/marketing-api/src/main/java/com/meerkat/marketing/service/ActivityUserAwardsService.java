package com.meerkat.marketing.service;

import com.meerkat.marketing.model.ActivityUserAwards;

import java.util.List;

/**
 * <p>
 * 用户活动奖品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-28
 */
public interface ActivityUserAwardsService {

    /**
     * 添加一条奖品记录
     *
     * @param activityUserAwards activityUserAwards
     * @return boolean
     * <AUTHOR>
     * @date 2022/9/29 15:04
     */
    boolean saveOne(ActivityUserAwards activityUserAwards);

    /**
     * 修改一条奖品记录
     *
     * @param activityUserAwards activityUserAwards
     * @return boolean
     * <AUTHOR>
     * @date 2022/9/29 15:04
     */
    boolean updateOneById(ActivityUserAwards activityUserAwards);

    /**
     * 获取一条奖品记录
     *
     * @param awardId 奖品id
     * @param userId  用户id
     * @return com.meerkat.marketing.model.ActivityUserAwards
     * <AUTHOR>
     * @date 2022/9/29 15:05
     */
    ActivityUserAwards getOneByUser(Long awardId, Long userId);

    /**
     * 获取用户的奖品记录
     *
     * @param activityId 活动id
     * @param userId     用户id
     * @return java.util.List<com.meerkat.marketing.model.ActivityUserAwards>
     * <AUTHOR>
     * @date 2022/9/29 15:05
     */
    List<ActivityUserAwards> getListByUser(Long activityId, Long userId);

}
