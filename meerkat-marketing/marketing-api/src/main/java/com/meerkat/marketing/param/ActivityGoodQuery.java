package com.meerkat.marketing.param;

import com.meerkat.shop.tag.enums.TagEnum;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * <p>
 * 活动表,查询条件
 * </p>
 *
 * <AUTHOR>
 * @since 2022/09/08 16:00:14
 */
public class ActivityGoodQuery implements Serializable{

    private static final long serialVersionUID = 1567784866396467200L;


    private Integer activityId;

    @NotNull
    private Integer currentPage;

    private Double lon;

    private Double lat;

    @NotNull
    private Integer pageSize;

    /**
     * 商品标签ID列表
     */
    private List<TagEnum> tagEnums;

    /**
     * 现货状态 1 现货 2预售
     */
    private Integer inStockState;

    public Double getLon() {
        return lon;
    }

    public void setLon(Double lon) {
        this.lon = lon;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<TagEnum> getTagEnums() {
        return tagEnums;
    }

    public void setTagEnums(List<TagEnum> tagEnums) {
        this.tagEnums = tagEnums;
    }

    public Integer getInStockState() {
        return inStockState;
    }

    public void setInStockState(Integer inStockState) {
        this.inStockState = inStockState;
    }
}