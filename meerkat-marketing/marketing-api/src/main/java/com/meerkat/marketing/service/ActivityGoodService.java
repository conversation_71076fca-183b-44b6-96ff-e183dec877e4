package com.meerkat.marketing.service;

import com.meerkat.common.db.PageView;
import com.meerkat.marketing.model.ActivityGood;
import com.meerkat.marketing.param.ActivityGoodQuery;

import java.util.List;

/**
 * <p>
 * 活动表
 * </p>
 *
 * <AUTHOR>
 * @since 2022/09/08 16:00:14
 */
public interface ActivityGoodService {

    /**
     * 初始化数据用
     * @param activityId
     * @param goodIds
     */
    void init(Integer activityId, List<Long> goodIds, String ret);

    PageView<ActivityGood> page(ActivityGoodQuery activityGoodQuery);

    ActivityGood load(Long goodId, Integer activityId);
}