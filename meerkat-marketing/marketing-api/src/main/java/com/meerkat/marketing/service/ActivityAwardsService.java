package com.meerkat.marketing.service;


import com.meerkat.marketing.model.ActivityAwards;
import com.meerkat.marketing.param.ActivityAwardAdd;
import com.meerkat.marketing.param.ActivityUserAwardsReceiveParam;
import com.meerkat.marketing.param.ActivityAwardsSendParam;

import java.util.List;

/**
 * <p>
 * 活动奖品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-30
 */
public interface ActivityAwardsService {

    /**
     * @Description:根据活动ID查询活动奖品信息
     * @Param: [activityId]
     * @Return: java.util.List<com.meerkat.marketing.model.ActivityAwards>
     * @Author: ChaoFan
     * @DateTime: 14:31 2022/9/30
     */
    List<ActivityAwards> getActivityAwardsList(Long activityId, Long userId);

    ActivityAwards getOneByCode(Long activityId, String awardCode);

    /**
     * 领取奖品
     *
     * @param param param
     * <AUTHOR>
     * @date 2022/10/16 16:59
     */
    void receiveAward(ActivityUserAwardsReceiveParam param);

    ActivityAwards getOneById(Long awardId);

    /**
     * 发放奖品
     *
     * @param param param
     * <AUTHOR>
     * @date 2022/10/27 15:23
     */
    void sendAward(ActivityAwardsSendParam param);

    /**
     * 新增或更新活动奖品
     *
     * @param activityAwardAddList 奖品信息
     * <AUTHOR>
     * @date 2022/10/27 15:23
     */
    void addOrUpdateAward(List<ActivityAwardAdd> activityAwardAddList);
}
