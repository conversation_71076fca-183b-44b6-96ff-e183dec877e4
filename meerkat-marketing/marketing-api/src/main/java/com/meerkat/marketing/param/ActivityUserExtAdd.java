package com.meerkat.marketing.param;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Since 2022-09-28
 * @Description 助力人扩展信息入参
 */
public class ActivityUserExtAdd {

    @NotBlank(message = "参与活动用户昵称不能为空")
    private String nickname;

    @NotBlank(message = "参与活动用户头像不能为空")
    private String icon;

    private Integer age;


    private Long cityId;

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    @Override
    public String toString() {
        return "ActivityUserExtAdd{" +
                "nickname='" + nickname + '\'' +
                ", age=" + age +
                ", cityId=" + cityId +
                ", icon='" + icon + '\'' +
                '}';
    }
}
