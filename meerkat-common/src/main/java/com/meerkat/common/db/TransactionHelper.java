package com.meerkat.common.db;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

/**
 * @description: 事务帮助类型
 * @author: pantaoling
 * @date: 2021/10/13
 */
@Component
public class TransactionHelper {

    @Autowired
    private PlatformTransactionManager transactionManager;

    /**
     * 方便自定义事物接口
     * @param definition
     * @return
     */
    public TransactionStatus beginTransaction(TransactionDefinition definition) {
        return transactionManager.getTransaction(definition);
    }

    /**
     * 使用spring缺省的事物定义，详情如下：
     * 1、事物传播特性，使用当前存在的，如果不存在创建一个
     * 2、事物隔离级别，使用数据库缺省
     * 3、事物超时时间，使用数据库缺省
     * @return
     */
    public TransactionStatus beginTransaction() {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        return transactionManager.getTransaction(def);
    }

    /**
     * 提交事物
     * @param status
     */
    public void commit(TransactionStatus status) {
        transactionManager.commit(status);
    }

    /**
     * 回滚事物
     * @param status
     */
    public void rollback(TransactionStatus status) {
        transactionManager.rollback(status);
    }
}
