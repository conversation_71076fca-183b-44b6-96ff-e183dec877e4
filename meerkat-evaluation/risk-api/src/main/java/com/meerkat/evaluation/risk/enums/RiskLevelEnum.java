package com.meerkat.evaluation.risk.enums;

/**
 *
 */
public enum RiskLevelEnum {

    HIGH(3, "高"),
    MID(2, "中"),
    LOW(1, "低"),

    ;

    final private int code;

    final private String message;

    RiskLevelEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
