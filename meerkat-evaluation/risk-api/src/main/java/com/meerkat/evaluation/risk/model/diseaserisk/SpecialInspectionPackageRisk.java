package com.meerkat.evaluation.risk.model.diseaserisk;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @description 专项检查包风险表
 * <AUTHOR>
 * @date 2022-03-24
 */
public class SpecialInspectionPackageRisk implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 专项检查包id
     */
    private Long itemPkgId;

    /**
     * 疾病风险id集合
     */
    private Long diseaseRiskId;

    /**
     * 是否删除：0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;


    public SpecialInspectionPackageRisk() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getItemPkgId() {
        return itemPkgId;
    }

    public void setItemPkgId(Long itemPkgId) {
        this.itemPkgId = itemPkgId;
    }

    public Long getDiseaseRiskId() {
        return diseaseRiskId;
    }

    public void setDiseaseRiskId(Long diseaseRiskId) {
        this.diseaseRiskId = diseaseRiskId;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public LocalDateTime getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(LocalDateTime gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

}