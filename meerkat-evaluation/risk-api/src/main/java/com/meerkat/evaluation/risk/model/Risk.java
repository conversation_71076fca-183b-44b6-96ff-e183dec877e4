package com.meerkat.evaluation.risk.model;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/2/25 19:20
 */
public class Risk implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 风险名称
     */
    private String name;

    /**
     * 名称拼音
     */
    private String pinyin;

    /**
     * 高风险边界值
     */
    private Double highVal;

    /**
     * 中风险边界值
     */
    private Double midVal;

    /**
     * 低风险边界值
     */
    private Double lowVal;

    /**
     * 是否删除：0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;


    public Risk() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public Double getHighVal() {
        return highVal;
    }

    public void setHighVal(Double highVal) {
        this.highVal = highVal;
    }

    public Double getMidVal() {
        return midVal;
    }

    public void setMidVal(Double midVal) {
        this.midVal = midVal;
    }

    public Double getLowVal() {
        return lowVal;
    }

    public void setLowVal(Double lowVal) {
        this.lowVal = lowVal;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public LocalDateTime getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(LocalDateTime gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

}