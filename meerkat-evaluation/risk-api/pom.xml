<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>meerkat-evaluation</artifactId>
        <groupId>com.meerkat.origin</groupId>
        <version>1.2.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>risk-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.meerkat.origin</groupId>
            <artifactId>meerkat-common</artifactId>
        </dependency>
    </dependencies>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

</project>