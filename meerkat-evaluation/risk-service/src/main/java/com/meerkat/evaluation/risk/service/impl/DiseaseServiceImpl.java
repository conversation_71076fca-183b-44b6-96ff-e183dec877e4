package com.meerkat.evaluation.risk.service.impl;

import com.meerkat.common.exception.BizException;
import com.meerkat.common.utils.CopyUtil;
import com.meerkat.evaluation.risk.dao.object.DiseaseDO;
import com.meerkat.evaluation.risk.enums.RiskDegreeEnum;
import com.meerkat.evaluation.risk.exception.RiskBizExEnum;
import com.meerkat.evaluation.risk.mapper.DiseaseMapper;
import com.meerkat.evaluation.risk.mapper.DiseaseRiskMapper;
import com.meerkat.evaluation.risk.model.disease.Disease;
import com.meerkat.evaluation.risk.model.disease.param.DiseaseQuery;
import com.meerkat.evaluation.risk.model.diseaserisk.DiseaseRisk;
import com.meerkat.evaluation.risk.service.DiseaseRiskService;
import com.meerkat.evaluation.risk.service.DiseaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName DiseaseServiceImpl
 * @description
 * @createTime 2022/3/24 22:21
 */
@Service
public class DiseaseServiceImpl implements DiseaseService {

    @Resource
    private DiseaseMapper diseaseMapper;
    @Resource
    private DiseaseRiskService diseaseRiskService;

    @Override
    public int add(Disease disease) {
        this.checkDiseaseName(disease);
        return diseaseMapper.insert(CopyUtil.copy(disease, DiseaseDO.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        diseaseMapper.logicDel(id);
        // 删除对应的疾病规则
        diseaseRiskService.deleteByDiseaseId(id);


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(Disease disease) {
        DiseaseDO diseaseDO = diseaseMapper.selectById(disease.getId());
        // 修改的时候名字没有变化，不做处理
        if(diseaseDO.getName().equals(disease.getName())) {
            return 1;
        }
        this.checkDiseaseName(disease);
        // 修改疾病对应的疾病风险
        List<DiseaseRisk> diseaseRiskList = diseaseRiskService.listByDiseaseId(disease.getId());
        diseaseRiskList.forEach(diseaseRisk -> {
            diseaseRisk.setDiseaseName(disease.getName());
            RiskDegreeEnum riskDegreeEnum = RiskDegreeEnum.getByCode(diseaseRisk.getRiskDegree());
            diseaseRisk.setName(disease.getName() + Objects.requireNonNull(riskDegreeEnum).getMessage());
        });
        diseaseMapper.update(CopyUtil.copy(disease, DiseaseDO.class));
        return diseaseRiskService.batchUpdateDiseaseRisk(diseaseRiskList);
    }

    @Override
    public Disease getById(Long id) {
        return CopyUtil.copy(diseaseMapper.selectById(id), Disease.class);
    }

    @Override
    public List<Disease> listDisease(DiseaseQuery diseaseQuery) {
        List<DiseaseDO> diseaseDOS = diseaseMapper.selectListByQuery(diseaseQuery);
        return CopyUtil.copyList(diseaseDOS, Disease.class);
    }



    // =================== private method util ===================

    /**
     * 检查疾病名称是否已经存在
     * @param disease
     */
    private void checkDiseaseName(Disease disease) {
        DiseaseQuery diseaseQuery = new DiseaseQuery();
        diseaseQuery.setDiseaseName(disease.getName());
        // 判断名字是否存在
        DiseaseDO diseaseDO = diseaseMapper.selectByQuery(diseaseQuery);
        if (Objects.nonNull(diseaseDO)) {
            throw new BizException(RiskBizExEnum.DISEASE_ALREADY_EXIST);
        }
    }
}
