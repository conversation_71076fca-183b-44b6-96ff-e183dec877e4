package com.meerkat.evaluation.risk.mapper;

import com.meerkat.evaluation.risk.dao.object.DiseaseDO;
import com.meerkat.evaluation.risk.model.disease.param.DiseaseQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @InterfaceName DiseaseMapper
 * @description
 * @createTime 2022/3/24 16:10
 */
@Mapper
public interface DiseaseMapper {

    /**
     * 新增
     * <AUTHOR>
     * @date 2022/03/24
     **/
    int insert(DiseaseDO diseaseDO);

    /**
     * 逻辑刪除
     * <AUTHOR>
     * @date 2022/03/24
     **/
    int logicDel(Long id);

    /**
     * 更新
     * <AUTHOR>
     * @date 2022/03/24
     **/
    int update(DiseaseDO diseaseDO);

    /**
     * 查询 根据主键 id 查询
     * <AUTHOR>
     * @date 2022/03/24
     **/
    DiseaseDO selectById(Long id);

    /**
     * 根据疾病名称模糊查询，并根据更新时间降序排列
     * @param diseaseQuery
     * @return
     */
    List<DiseaseDO> selectListByQuery(DiseaseQuery diseaseQuery);

    /**
     * 根据疾病名称精确查询疾病信息
     * @param diseaseQuery
     * @return
     */
    DiseaseDO selectByQuery(DiseaseQuery diseaseQuery);
}
