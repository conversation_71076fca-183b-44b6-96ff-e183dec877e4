package com.meerkat.evaluation.risk.dao.object;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @ClassName DiseaseRiskDO
 * @description
 * @createTime 2022/3/24 16:28
 */
public class DiseaseRiskDO {

    /**
     * id
     */
    private Long id;

    /**
     * 疾病风险名称
     */
    private String name;

    /**
     * 风险程度：低、中、高
     */
    private Integer riskDegree;

    /**
     * 疾病id
     */
    private Long diseaseId;

    /**
     * 疾病名称
     */
    private String diseaseName;

    /**
     * 风险建议
     */
    private String advice;

    /**
     * 是否删除：0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getRiskDegree() {
        return riskDegree;
    }

    public void setRiskDegree(Integer riskDegree) {
        this.riskDegree = riskDegree;
    }

    public Long getDiseaseId() {
        return diseaseId;
    }

    public void setDiseaseId(Long diseaseId) {
        this.diseaseId = diseaseId;
    }

    public String getDiseaseName() {
        return diseaseName;
    }

    public void setDiseaseName(String diseaseName) {
        this.diseaseName = diseaseName;
    }

    public String getAdvice() {
        return advice;
    }

    public void setAdvice(String advice) {
        this.advice = advice;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public LocalDateTime getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(LocalDateTime gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }
}
