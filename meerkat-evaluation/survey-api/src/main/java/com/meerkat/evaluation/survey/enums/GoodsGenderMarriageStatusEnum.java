package com.meerkat.evaluation.survey.enums;

/**
 * <AUTHOR>
 * @EnumName GoodsGenderMarriageStatusEnum
 * @description
 * @createTime 2022/4/22 10:12
 */
public enum GoodsGenderMarriageStatusEnum {
    MALE(1, "男性套餐"),
    FEMALEUNMARRIED(2, "女未婚套餐"),
    FEMALEMARRIED(3, "女已婚套餐"),

    ;
    private int code;

    private String name;

    GoodsGenderMarriageStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
