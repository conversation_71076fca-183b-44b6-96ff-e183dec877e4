package com.meerkat.evaluation.survey.model.question.param;

import com.meerkat.evaluation.survey.model.answer.SurveyQuestionAnswer;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/3 11:53 上午
 */
public class SurveyQuestionUpdate {
    /**
     * id
     */
    private Long id;

    /**
     * 问卷id
     */
    private Long surveyId;

    /**
     * 问题
     */
    private String content;

    /**
     * 答案类型：1-单选， 2-多选， 3-文本填写， 4-地址获取， 5-位置获取， 6-身份信息， 7-承诺， 8-文本域
     */
    private Integer type;

    /**
     * 性别：0女 1男 2通用
     */
    private Integer gender;

    /**
     * 0:非必答，1:必答
     */
    private Integer mustAnswer;

    /**
     * 启用状态
     */
    private Integer status;

    /**
     * 选项列表
     */
    private List<SurveyQuestionAnswer> surveyQuestionAnswers;

    /**
     * 前置选项id
     */
    private List<Long> conditionAnswerId;

    /**
     * 标签名称
     */
    private String tagName;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getMustAnswer() {
        return mustAnswer;
    }

    public void setMustAnswer(Integer mustAnswer) {
        this.mustAnswer = mustAnswer;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<SurveyQuestionAnswer> getSurveyQuestionAnswers() {
        return surveyQuestionAnswers;
    }

    public void setSurveyQuestionAnswers(List<SurveyQuestionAnswer> surveyQuestionAnswers) {
        this.surveyQuestionAnswers = surveyQuestionAnswers;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public List<Long> getConditionAnswerId() {
        return conditionAnswerId;
    }

    public void setConditionAnswerId(List<Long> conditionAnswerId) {
        this.conditionAnswerId = conditionAnswerId;
    }
}
