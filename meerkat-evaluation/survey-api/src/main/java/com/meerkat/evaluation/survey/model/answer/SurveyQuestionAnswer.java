package com.meerkat.evaluation.survey.model.answer;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: yangquansheng
 * @ClassName SurveyQuestionAnswer
 * @Description 问卷答案
 * @createTime 2022/2/25 11:51
 */
public class SurveyQuestionAnswer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 选项id
     */
    private Long id;

    /**
     * 问题id
     */
    private Long questionId;

    /**
     * 内容
     */
    private String content;

    /**
     * 分值
     */
    private Integer score;

    /**
     * 建议
     */
    private String advice;

    /**
     * 顺序
     */
    private Integer sequence;


    /**
     * 0  女
     * 1  男
     * 2  通用
     * @see com.meerkat.evaluation.survey.enums.GenderEnum
     * 选项应用性别
     */
    private Integer gender;

    /**
     * 标准风险选项
     */
    private Long riskOptionId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 是否删除：0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 答案说明
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 选中状态
     */
    private Boolean selected;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }


    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public LocalDateTime getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(LocalDateTime gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getAdvice() {
        return advice;
    }

    public void setAdvice(String advice) {
        this.advice = advice;
    }


    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Long getRiskOptionId() {
        return riskOptionId;
    }

    public void setRiskOptionId(Long riskOptionId) {
        this.riskOptionId = riskOptionId;
    }

    public Boolean getSelected() {
        return selected;
    }

    public void setSelected(Boolean selected) {
        this.selected = selected;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
