package com.meerkat.evaluation.survey.model.answer.param;

import java.util.List;

/**
 * @description:
 * @author: pantaoling
 * @date: 2022/3/1
 */
public class AnswerQuery {

    /**
     * 问题id集合
     */
    private List<Long> questionIds;
    /**
     * 选项内容
     */
    private String content;
    /**
     * 风险选项名称
     */
    private String riskOptionName;

    /**
     * 启用状态 0—关闭 1-启用
     */
    private Integer status;

    public List<Long> getQuestionIds() {
        return questionIds;
    }

    public void setQuestionIds(List<Long> questionIds) {
        this.questionIds = questionIds;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getRiskOptionName() {
        return riskOptionName;
    }

    public void setRiskOptionName(String riskOptionName) {
        this.riskOptionName = riskOptionName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
