package com.meerkat.evaluation.survey.enums;

/**
 * <AUTHOR>
 * @EnumName SurveySceneTypeEnum
 * @description 问卷投放位置枚举
 * @createTime 2022/4/21 23:01
 */
public enum SurveySceneTypeEnum {

    SMART(1, "智能加项"),

    RECOMMEND(2, "推荐套餐"),

    COMMON(3, "通用"),

    EXAM(4, "体检流调"),

    EHOSP(5, "互联网门诊流调"),

    HM_SUB_CHANNEL(6, "健管子频道"),

    EHOSP_NUCLEIC(7, "互联网核酸流调"),

    ;

    private final int code;

    private final String name;

    SurveySceneTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
