package com.meerkat.fulfillment.plan.model;

/**
 * <AUTHOR>
 * @date 2022/04/27 16:36
 */
public class FulmtCustomerPlanQuery {

    /**
     * 机构id
     */
    private Long organizationId;

    /**
     * 客户组客户计划id
     */
    private Long customerTemplatePlanId;

    /**
     * 客户套餐id
     */
    private Long customerPkgPlanId;

    /**
     * 计划名称
     */
    private String name;

    /**
     * 客户组id
     */
    private String groupId;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 类型 1患者分组 2客户分组
     */
    private Integer customerType;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 干预人
     */
    private Long interveneId;

    /**
     * 干预负责人
     */
    private Long interveneHeadId;

    /**
     * 是否人工干预 0 自动 1 人工
     */
    private Integer isHumanIntervention;

    /**
     * 执行时间
     */
    private String executeTime;

    /**
     * 执行时间月份
     */
    private String executeMonthTime;

    /**
     * 触达方式 -1 人工 0 企业微信
     */
    private Integer reachWay;

    /**
     * 触达类型 0 文本 1 链接 2 图片
     */
    private Integer reachType;

    /**
     * 执行状态 0未执行 1执行中 2执行成功 3执行失败
     */
    private Integer executeStatus;

    /**
     * 1启用2停用
     */
    private Integer status;

    /**
     * 审核状态 0待审核 1审核成功 2审核失败
     */
    private Integer checkStatus;

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public Long getInterveneHeadId() {
        return interveneHeadId;
    }

    public void setInterveneHeadId(Long interveneHeadId) {
        this.interveneHeadId = interveneHeadId;
    }

    public String getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(String executeTime) {
        this.executeTime = executeTime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getReachWay() {
        return reachWay;
    }

    public void setReachWay(Integer reachWay) {
        this.reachWay = reachWay;
    }

    public Integer getReachType() {
        return reachType;
    }

    public void setReachType(Integer reachType) {
        this.reachType = reachType;
    }

    public Integer getExecuteStatus() {
        return executeStatus;
    }

    public void setExecuteStatus(Integer executeStatus) {
        this.executeStatus = executeStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public Long getInterveneId() {
        return interveneId;
    }

    public void setInterveneId(Long interveneId) {
        this.interveneId = interveneId;
    }

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    public Integer getIsHumanIntervention() {
        return isHumanIntervention;
    }

    public void setIsHumanIntervention(Integer isHumanIntervention) {
        this.isHumanIntervention = isHumanIntervention;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getExecuteMonthTime() {
        return executeMonthTime;
    }

    public void setExecuteMonthTime(String executeMonthTime) {
        this.executeMonthTime = executeMonthTime;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Long getCustomerPkgPlanId() {
        return customerPkgPlanId;
    }

    public void setCustomerPkgPlanId(Long customerPkgPlanId) {
        this.customerPkgPlanId = customerPkgPlanId;
    }

    public Long getCustomerTemplatePlanId() {
        return customerTemplatePlanId;
    }

    public void setCustomerTemplatePlanId(Long customerTemplatePlanId) {
        this.customerTemplatePlanId = customerTemplatePlanId;
    }
}
