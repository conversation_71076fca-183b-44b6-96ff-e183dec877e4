package com.meerkat.fulfillment.plan.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/04/27 14:47
 */
public class FulmtCustomerTemplatePlan implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户套餐id
     */
    private Long customerPkgPlanId;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 类型 1患者分组 2客户分组
     */
    private Integer customerType;

    /**
     * 客户组id
     */
    private String groupId;

    /**
     * 计划id
     */
    private Long planTemplateId;

    /**
     * 计划名称
     */
    private String name;

    /**
     * 介绍
     */
    private String introduce;

    /**
     * 触达方式 -1 人工 0 企业微信
     */
    private Integer reachWay;

    /**
     * 触达类型 0 文本 1 链接 2 图片
     */
    private Integer reachType;

    /**
     * 触达内容
     */
    private String reachContent;

    /**
     * 计划时间
     */
    private String planTimeConfig;

    /**
     * 干预人
     */
    private Long interveneId;

    /**
     * 干预负责人
     */
    private Long interveneHeadId;

    /**
     * 是否人工干预 0 自动 1 人工
     */
    private Integer isHumanIntervention;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 开始时间
     */
    private Date planStartTime;

    /**
     * 结束时间
     */
    private Date planEndTime;

    /**
     * 1启用2停用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 是否删除 1是0否
     */
    private Integer isDeleted;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCustomerPkgPlanId() {
        return customerPkgPlanId;
    }

    public void setCustomerPkgPlanId(Long customerPkgPlanId) {
        this.customerPkgPlanId = customerPkgPlanId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public Long getPlanTemplateId() {
        return planTemplateId;
    }

    public void setPlanTemplateId(Long planTemplateId) {
        this.planTemplateId = planTemplateId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIntroduce() {
        return introduce;
    }

    public void setIntroduce(String introduce) {
        this.introduce = introduce;
    }

    public Integer getReachWay() {
        return reachWay;
    }

    public void setReachWay(Integer reachWay) {
        this.reachWay = reachWay;
    }

    public Integer getReachType() {
        return reachType;
    }

    public void setReachType(Integer reachType) {
        this.reachType = reachType;
    }

    public String getReachContent() {
        return reachContent;
    }

    public void setReachContent(String reachContent) {
        this.reachContent = reachContent;
    }

    public String getPlanTimeConfig() {
        return planTimeConfig;
    }

    public void setPlanTimeConfig(String planTimeConfig) {
        this.planTimeConfig = planTimeConfig;
    }

    public Long getInterveneId() {
        return interveneId;
    }

    public void setInterveneId(Long interveneId) {
        this.interveneId = interveneId;
    }

    public Long getInterveneHeadId() {
        return interveneHeadId;
    }

    public void setInterveneHeadId(Long interveneHeadId) {
        this.interveneHeadId = interveneHeadId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Date getPlanStartTime() {
        return planStartTime;
    }

    public void setPlanStartTime(Date planStartTime) {
        this.planStartTime = planStartTime;
    }

    public Date getPlanEndTime() {
        return planEndTime;
    }

    public void setPlanEndTime(Date planEndTime) {
        this.planEndTime = planEndTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsHumanIntervention() {
        return isHumanIntervention;
    }

    public void setIsHumanIntervention(Integer isHumanIntervention) {
        this.isHumanIntervention = isHumanIntervention;
    }
}
