package com.meerkat.fulfillment.fulfillment.enums;
/**
 * <AUTHOR>
 * @ClassName FulmtRetailStateEnum.java
 * @Description TODO
 * @createTime 2022-07-26 11:56:00
 */
public enum FulmtRetailStateEnum {

    SHIPPED(0, "备货中"),

    SHIPPING(1, "待发货"),

    DISTRI(4, "配送中"),

    SIGN(6, "已签收"),

    FAIL(7, "已取消"),
    RESCHEDULE(8, "改期待定"),

    REFUNDING(9, "退款中"),

    REFUND(10, "完成退款"),

    REFUND_FAIL(11, "拒绝退款");

    private int code;

    private String name;

    FulmtRetailStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static FulmtRetailStateEnum getByCode(Integer code) {
        for (FulmtRetailStateEnum fulmtRetailStateEnum : FulmtRetailStateEnum.values()) {
            if (fulmtRetailStateEnum.getCode() == code) {
                return fulmtRetailStateEnum;
            }
        }
        return null;
    }
}
