package com.meerkat.fulfillment.fulfillment.enums;

import com.meerkat.common.api.BizCode;

/**
 * <p>
 * 用户帐户异常
 * </p>
 *
 * <AUTHOR>
 * @since 2022/2/22 11:12
 */
public enum OrderBizException implements BizCode {

    /**
     * 商品信息不能为null，二方渠道没有进行关联商品中心
     */
    ORDER_GOOD_NOT_NULL("EX_ORDER_001_01", "商品信息不能为null，二方渠道没有进行关联商品中心"),

    /**
     * 订单sku编码不能为null
     */
    ORDER_SKU_CODE_NULL("EX_ORDER_001_02", "订单sku编码不能为null"),

    /**
     * 订单号不能为null
     */
    ORDER_OUT_NUMBER_NULL("EX_ORDER_001_03", "订单号不能为null"),

    /**
     * 订单状态不正确
     */
    ORDER_STATE_NULL("EX_ORDER_001_04", "订单状态不正确"),

    /**
     * 订单导入失败
     */
    ORDER_STATE_FAIL("EX_ORDER_001_05", "订单导入失败,无有效数据"),

    FULMT_GOOD_NOT_NULL("EX_ORDER_001_06", "标准商品没有关联履约商品"),
    /**
     * 订单导入失败sku或者渠道id异常
     */
    ORDER_SKU_CHA_GA_FAIL("EX_ORDER_001_07", "订单导入失败sku或者渠道id异常"),


    /**
     * 订单渠道不能为null
     */
    ORDER_CHORGAID_NOT_NULL("EX_ORDER_001_08", "订单渠道不能为null"),


    /**
     * 商品数量不能为null
     */
    GOOD_COUNT_NOT_NULL("EX_ORDER_001_09", "商品数量不能为null"),

    /**
     * 下单人不能为null
     */
    ORDER_ID_NOT_NULL("EX_ORDER_001_010", "下单人不能为null"),

    /**
     * 渠道编码和子订单编码不应该都相同
     */
    CHILDORDER_ORDERNUM("EX_ORDER_001_011", "渠道编码和子订单编码不应该都相同"),

    /**
     * 实付金额不能为null
     */
    ORDER_AMOUNT("EX_ORDER_001_012", "实付金额不能为null"),

    /**
     * 实付金额不能为null
     */
    APPOINT_INFO("EX_ORDER_001_013", "状态已变更，请刷新页面"),
    ;

    OrderBizException(String code, String message) {
        this.code = code;
        this.message = message;
    }

    private final String code;
    private final String message;

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}
