package com.meerkat.fulfillment.fulfillment.handler.fulmtstrategy;

import com.meerkat.common.enums.SystemEnum;
import com.meerkat.common.exception.BizException;
import com.meerkat.fulfillment.fulfillment.enums.FulMtGoodsBizExEnum;
import com.meerkat.fulfillment.fulfillment.enums.FulmtStatusEnum;
import com.meerkat.fulfillment.fulfillment.enums.OrderFulmtEventEnum;
import com.meerkat.fulfillment.fulfillment.handler.AbstractOrderFulmtStrategy;
import com.meerkat.fulfillment.fulfillment.model.FulmtOrderOperateRecord;
import com.meerkat.fulfillment.fulfillment.model.OrderFulmt;

import com.meerkat.order.event.OrderCancelEvent;
import com.meerkat.order.model.Order;
import com.meerkat.order.service.OrderFsmEngine;
import com.meerkat.order.service.OrderReadService;
import com.meerkat.shop.goods.model.Goods;
import com.meerkat.shop.goods.model.GoodsTag;
import com.meerkat.shop.goods.service.GoodsService;
import com.meerkat.shop.tag.model.Tag;
import com.meerkat.smart.organization.model.Organization;
import com.meerkat.smart.organization.service.OrganizationService;
import com.meerkat.work.order.enums.WorkOrderBizTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 待机构确认，取消预约
 * <AUTHOR>
 */
@Component
public class OrderFulmtAgencyConfirmedCancelStrategy extends AbstractOrderFulmtStrategy {

    @Value("${feishu.robot.organChangeWebHook:https://open.feishu.cn/open-apis/bot/v2/hook/42a1437a-b535-47dc-801f-d8365904f5ad}")
    private  String vaccineWebHook;

    @Autowired
    private OrderFsmEngine orderFsmEngine;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private OrderReadService orderReadService;
    @Autowired
    private OrganizationService organizationService;

    private static final Logger LOG = LoggerFactory.getLogger(OrderFulmtAgencyConfirmedCancelStrategy.class);

    @Override
    public OrderFulmtEventEnum getType() {
        return OrderFulmtEventEnum.AGENCY_CONFIRMED_CANCEL;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer apply(OrderFulmt orderFulmt, Long id) throws Exception {
        if (!getFlag(orderFulmt.getOrderNum(), FulmtStatusEnum.AGENCY_CONFIRMED.getCode())){
            throw new BizException(FulMtGoodsBizExEnum.FULMT_STATE_FAIL,"履约单状态已变更，请刷新页面");
        }

        OrderCancelEvent orderCancelEvent = new OrderCancelEvent(orderFulmt.getOrgaOrderNum(), id);
        orderCancelEvent.setSystem(SystemEnum.OPS.getCode());

        //撤销机构订单  释放排期和库存
        try {
            orderFsmEngine.sendEvent(orderCancelEvent);
        } catch (Exception e) {
            LOG.error("订单撤销失败,orderNum={},operator={}", orderFulmt.getOrderNum(),id);
        }

        //状态推进至改期待定 1 设置改期待定标识 清空 履约商品 履约机构 履约商品信息 履约排期时间  订单状态回退至已支付
        updateState(orderFulmt.getId(),orderFulmt.getOrderNum(),orderFulmt.getState());

        //添加日志
        FulmtOrderOperateRecord fulmtOrderOperateRecord = new FulmtOrderOperateRecord();
        fulmtOrderOperateRecord.setFulmtOrderNum(orderFulmt.getOrderNum());
        fulmtOrderOperateRecord.setFromState(FulmtStatusEnum.AGENCY_CONFIRMED.getCode());
        fulmtOrderOperateRecord.setEventName(" 待机构确认，取消预约");
        fulmtOrderOperateRecord.setToState(FulmtStatusEnum.RESCHEDULE.getCode());
        fulmtOrderOperateRecord.setOperatorId(Math.toIntExact(id));
        this.addLog(fulmtOrderOperateRecord);
        this.addWorkOrder(orderFulmt.getOrderNum(), WorkOrderBizTypeEnum.SUBSCRIBE.getType());
        Order order = orderReadService.loadByOrderNum(orderFulmt.getOrderNum());
        String tagName = "暂无";
        if (orderFulmt.getFulmtGoodsId() != null){
            Goods goods = goodsService.getGoods(orderFulmt.getFulmtGoodsId());

            if (!CollectionUtils.isEmpty(goods.getGoodsTags())){
                GoodsTag goodsTag = goods.getGoodsTags().get(0);
                Tag tag = goodsTag.getTag();
                tagName = tag.getName();
            }

        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date fulmtDate = orderFulmt.getFulmtDate();
        String fulmtDateString = "暂无";
        if (fulmtDate != null){
            fulmtDateString = sdf.format(fulmtDate);
        }
        Organization organization = organizationService.getOrganization(orderFulmt.getFulmtOrgaId());
        //订单号：{Data.订单号} 姓名：{Data.姓名} 手机号：{Data.手机号} 渠道：{Data.渠道}的订单取消预约，目前暂未确定预约日期，请勿同步给机构
        String[] arr = {"18758117501","15906649014"};
        String info =  "项目："+ tagName+"\n" +"机构："+ organization.getName()+"\n"+"姓名："+ orderFulmt.getAcceptorName()+"\n"+"身份证号："+ order.getAcceptorIdcard()+"\n"+"手机号："+orderFulmt.getAcceptorMobile()+"\n"+ "原预约时间为："+fulmtDateString+"\n"+"的订单取消预约，目前暂未确定预约日期，请勿同步给机构。";
        messageByMobileList(vaccineWebHook,info,arr,"疫苗改期任务");
        return 1;
    }
}
