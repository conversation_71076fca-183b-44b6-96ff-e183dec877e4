package com.meerkat.fulfillment.fulfillment.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meerkat.fulfillment.fulfillment.mapper.dataobj.OrderFulmtDO;
import com.meerkat.fulfillment.fulfillment.model.OrderFulmtCheck;
import com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单履约信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
@Mapper
public interface OrderFulmtMapper extends BaseMapper<OrderFulmtDO> {

    /**
     * 分页查询履约订单信息
     *
     * @param orderFulmtCheck orderFulmtCheck
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue>
     * <AUTHOR>
     * @date 2022/4/22 11:34
     */
    List<OrderFulmtValue> listInfoByPage(@Param("orderFulmtCheck") OrderFulmtCheck orderFulmtCheck);


    /**
     * 查询履约简化订单信息
     */
    default List<OrderFulmtDO> listFulmtSimplifyInfo(Integer state, List<Long> chorgaList) {
        return selectList(new LambdaQueryWrapper<OrderFulmtDO>().select(OrderFulmtDO::getParentOrderNum, OrderFulmtDO::getChorgaId).eq(OrderFulmtDO::getState, state).in(OrderFulmtDO::getChorgaId, chorgaList).and(i -> i.notIn(OrderFulmtDO::getRefundState, 1, 2).or().isNull(OrderFulmtDO::getRefundState)));
    }

    /**
     * 分页查询履约订单信息
     *
     * @param orderFulmtCheck orderFulmtCheck
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue>
     * <AUTHOR>
     * @date 2022/4/22 11:34
     */
    List<OrderFulmtValue> fulmtInfoList(@Param("orderFulmtCheck") OrderFulmtCheck orderFulmtCheck);


    /**
     * 分页查询履约订单信息
     *
     * @param orderFulmtCheck orderFulmtCheck
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue>
     * <AUTHOR>
     * @date 2022/4/22 11:34
     */
    List<OrderFulmtValue> listInfoByNum(@Param("orderFulmtCheck") OrderFulmtCheck orderFulmtCheck);

    /**
     * 根据订单编号查询履约单信息
     *
     * @param orderNumList orderNumList
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue>
     * <AUTHOR>
     * @date 2022/4/22 11:34
     */
    List<OrderFulmtValue> listInfoByOrderNum(List<String> orderNumList);


    /**
     * 根据履约编号查询履约单信息
     *
     * @param flumtNumList flumtNumList
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue>
     * <AUTHOR>
     * @date 2022/4/22 11:34
     */
    List<OrderFulmtValue> listInfoByflumtNum(List<String> flumtNumList);


    /**
     * 根据订单编号查询履约单信息
     *
     * @param orderNum orderNum
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue>
     * <AUTHOR>
     * @date 2022/4/22 11:34
     */
    OrderFulmtValue getInfoByOrderNum(String orderNum, Long fulmtGoodsId);


    /**
     * 根据订单编号查询履约单信息
     *
     * @param orderNum orderNum
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue>
     * <AUTHOR>
     * @date 2022/4/22 11:34
     */
    OrderFulmtValue checkOrderNum(String orderNum);


    /**
     * 根据履约编号查询履约单信息
     *
     * @param fulmtNum fulmtNum
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue>
     * <AUTHOR>
     * @date 2022/4/22 11:34
     */
    OrderFulmtValue checkByOrderFulmtNum(String fulmtNum);

    /**
     * 查询履约单列表
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue>
     * <AUTHOR>
     * @date 2022/4/22 11:34
     */
    List<OrderFulmtValue> list();


    /**
     * 查询每天需要待预约推进的订单
     *
     * @param orderFulmtCheck orderFulmtCheck
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue>
     * <AUTHOR>
     * @date 2022/4/22 11:34
     */
    List<OrderFulmtValue> listInfoByUpdateState(@Param("orderFulmtCheck") OrderFulmtCheck orderFulmtCheck);

    /**
     * 更新
     *
     * <AUTHOR>
     * @date 2022/06/14
     **/
    int updateByCancel(OrderFulmtDO orderFulmtDO);

    /**
     * 查询每天需要待预约推进的订单
     *
     * @param orderFulmtCheck orderFulmtCheck
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue>
     * <AUTHOR>
     * @date 2022/4/22 11:34
     */
    List<OrderFulmtValue> listInfoByUpdate(@Param("orderFulmtCheck") OrderFulmtCheck orderFulmtCheck);

}
