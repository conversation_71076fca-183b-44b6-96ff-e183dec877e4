package com.meerkat.fulfillment.fulfillment.handler.fulmtstrategy;

import com.meerkat.common.enums.SystemEnum;
import com.meerkat.common.exception.BizException;
import com.meerkat.fulfillment.fulfillment.enums.FulMtGoodsBizExEnum;
import com.meerkat.fulfillment.fulfillment.enums.FulmtStatusEnum;
import com.meerkat.fulfillment.fulfillment.enums.OrderFulmtEventEnum;
import com.meerkat.fulfillment.fulfillment.handler.AbstractOrderFulmtStrategy;
import com.meerkat.fulfillment.fulfillment.model.FulmtOrderOperateRecord;
import com.meerkat.fulfillment.fulfillment.model.OrderFulmt;

import com.meerkat.order.event.OrderCancelEvent;
import com.meerkat.order.service.OrderFsmEngine;
import com.meerkat.work.order.enums.WorkOrderBizTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 已预约，取消预约
 * <AUTHOR>
 */
@Component
public class OrderFulmtReservedCancelStrategy extends AbstractOrderFulmtStrategy {

    @Autowired
    private OrderFsmEngine orderFsmEngine;

    private static final Logger LOG = LoggerFactory.getLogger(OrderFulmtReservedCancelStrategy.class);

    @Override
    public OrderFulmtEventEnum getType() {
        return OrderFulmtEventEnum.RESERVED_CANCEL;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer apply(OrderFulmt orderFulmt, Long id) {
        if (!getFlag(orderFulmt.getOrderNum(),1)){
            throw new BizException(FulMtGoodsBizExEnum.FULMT_STATE_FAIL,"履约单状态已变更，请刷新页面");
        }
        OrderCancelEvent orderCancelEvent = new OrderCancelEvent(orderFulmt.getOrgaOrderNum(), id);
        orderCancelEvent.setSystem(SystemEnum.OPS.getCode());
        //撤销机构订单  释放排期和库存
        try {
            orderFsmEngine.sendEvent(orderCancelEvent);
        } catch (Exception e) {
            LOG.error("订单撤销失败,orderNum={},operator={}", orderFulmt.getOrderNum(), id);
        }
        //状态推进至改期待定 1 设置改期待定标识 清空 履约商品 履约机构 履约商品信息 履约排期时间
        updateState(orderFulmt.getId(),orderFulmt.getOrderNum(),orderFulmt.getState());
        //添加日志
        FulmtOrderOperateRecord fulmtOrderOperateRecord = new FulmtOrderOperateRecord();
        fulmtOrderOperateRecord.setFulmtOrderNum(orderFulmt.getOrderNum());
        fulmtOrderOperateRecord.setFromState(FulmtStatusEnum.APPOINt.getCode());
        fulmtOrderOperateRecord.setEventName("已预约，取消预约");
        if (orderFulmt.getType() != null && orderFulmt.getType() == 5){
            fulmtOrderOperateRecord.setEventName("用户操作已预约，取消推进到待机构确认");
            fulmtOrderOperateRecord.setType(1);
        }
        fulmtOrderOperateRecord.setToState(FulmtStatusEnum.RESCHEDULE.getCode());
        fulmtOrderOperateRecord.setOperatorId(Math.toIntExact(id));
        this.addLog(fulmtOrderOperateRecord);
        this.addWorkOrder(orderFulmt.getOrderNum(), WorkOrderBizTypeEnum.SUBSCRIBE.getType());
        return 1;
    }
}
