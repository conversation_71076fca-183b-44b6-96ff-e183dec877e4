package com.meerkat.fulfillment.fulfillment.listener;

import cn.hutool.core.net.NetUtil;
import cn.hutool.json.JSONUtil;
import com.meerkat.fulfillment.fulfillment.constant.FulmtOrderRefundMqConstant;
import com.meerkat.fulfillment.fulfillment.model.FulmtOrderOperateRecord;
import com.meerkat.fulfillment.fulfillment.model.OrderFulmtValue;
import com.meerkat.fulfillment.fulfillment.service.FulmtOrderOperateRecordService;
import com.meerkat.fulfillment.fulfillment.service.OrderFulmtService;
import com.meerkat.notify.listener.AbstractMessageListener;
import com.meerkat.notify.model.base.FailObject;
import com.meerkat.notify.model.base.MetaMessage;
import com.meerkat.notify.model.base.Payload;
import com.meerkat.notify.model.base.SuccessObject;
import com.meerkat.notify.service.NotifyService;
import com.meerkat.order.enums.OrderRefundStateEnum;
import com.meerkat.order.model.Order;
import com.meerkat.order.service.OrderReadService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @ClassName FulmtMessageListener
 * @description 履约单监听器
 * @createTime 2022/7/05 17:27
 */
@Configuration
public class OpsOrderRefundMessageListener extends AbstractMessageListener {
    private static final Logger LOG = LoggerFactory.getLogger(OpsOrderRefundMessageListener.class);

    @Autowired
    private OrderFulmtService orderFulmtService;

    @Autowired
    private FulmtOrderOperateRecordService fulmtOrderOperateRecordService;

    @Autowired
    private OrderReadService orderReadService;

    @Autowired
    private NotifyService notifyService;

    @Value("${open.ip.routingKey:false}")
    private boolean isIpRoutingKey;


    @Override
    public boolean receiveMessage(MetaMessage rabbitMetaMessage) {
        //获得数据
        LOG.info("订单退款，履约单消息,payload={}", JSONUtil.toJsonStr(rabbitMetaMessage.getPayload()));
        //子订单编号
        String domainId = rabbitMetaMessage.getPayload().getDomainId();
        Order order = orderReadService.loadByOrderNum(domainId);
        if (order == null) {
            logger.error("退款消息接收的订单为null,domain:", domainId);
        }

        //退款中
        if (order.getRefundState() == OrderRefundStateEnum.REFUNDING.getCode()) {
            String ext = rabbitMetaMessage.getPayload().getExt();
            Long id = Long.valueOf(ext);
            LOG.info("退款中消息----------------------------" + domainId);
            //添加日志
            OrderFulmtValue orderFulmtValue = orderFulmtService.checkOrder(domainId);
            Integer i = 0;
            while (orderFulmtValue == null && i < 3) {
                try {
                    Thread.sleep(1000L);
                    orderFulmtValue = orderFulmtService.checkOrder(domainId);
                } catch (InterruptedException e) {
                    logger.error("线程睡眠异常,", e);
                }
                i++;
                logger.info("履约单为空，等待...订单号,{}", domainId);
            }
            if (orderFulmtValue != null) {
                orderFulmtService.updateRefund(domainId);
                FulmtOrderOperateRecord fulmtOrderOperateRecord = new FulmtOrderOperateRecord();
                fulmtOrderOperateRecord.setFulmtOrderNum(domainId);
                fulmtOrderOperateRecord.setFromState(orderFulmtValue.getState());
                fulmtOrderOperateRecord.setEventName("退款中，履约挂起");
                fulmtOrderOperateRecord.setToState(9);
                fulmtOrderOperateRecord.setOperatorId(Math.toIntExact(id));
                fulmtOrderOperateRecordService.insert(fulmtOrderOperateRecord);

                //通知工单
                String routingKey = FulmtOrderRefundMqConstant.RoutingKey.FULMT_REFUNDING;
                Payload payload = new Payload();
                payload.setDomainId(orderFulmtValue.getFulmtNum());
                payload.setExt(ext);
                MetaMessage message = new MetaMessage();
                message.setPayload(payload);
                message.setRoutingKey(isIpRoutingKey ? routingKey + "." + NetUtil.getLocalhost().getHostAddress() : routingKey);
                message.setExchange(FulmtOrderRefundMqConstant.EXCHANGE);
                notifyService.sendMessage(message);

            }
            return Boolean.FALSE;
        }

        //退款成功
        if (OrderRefundStateEnum.REFUNDED.getCode() == order.getRefundState()) {
            String ext = rabbitMetaMessage.getPayload().getExt();
            Long id = Long.valueOf(ext);
            LOG.info("退款成功消息--------------" + domainId);
            //添加日志
            OrderFulmtValue orderFulmtValue = orderFulmtService.checkOrder(domainId);
            if (orderFulmtValue != null) {
                orderFulmtService.updateRefundSuccess(domainId);
                FulmtOrderOperateRecord fulmtOrderOperateRecord = new FulmtOrderOperateRecord();
                fulmtOrderOperateRecord.setFulmtOrderNum(domainId);
                fulmtOrderOperateRecord.setFromState(orderFulmtValue.getState());
                fulmtOrderOperateRecord.setEventName("退款成功，取消履约单");
                fulmtOrderOperateRecord.setToState(10);
                fulmtOrderOperateRecord.setOperatorId(Math.toIntExact(id));
                fulmtOrderOperateRecordService.insert(fulmtOrderOperateRecord);

                //通知工单
                String routingKey = FulmtOrderRefundMqConstant.RoutingKey.FULMT_REFUNDED;
                Payload payload = new Payload();
                payload.setDomainId(orderFulmtValue.getFulmtNum());
                payload.setExt(ext);
                MetaMessage message = new MetaMessage();
                message.setPayload(payload);
                message.setRoutingKey(isIpRoutingKey ? routingKey + "." + NetUtil.getLocalhost().getHostAddress() : routingKey);
                message.setExchange(FulmtOrderRefundMqConstant.EXCHANGE);
                notifyService.sendMessage(message);
            } else {
                logger.error("更新履约单退款失败,履约单为空，domain:{}", domainId);
            }

            return Boolean.FALSE;
        }

        //退款拒绝
        if (OrderRefundStateEnum.REFUSE.getCode() == order.getRefundState()) {
            String ext = rabbitMetaMessage.getPayload().getExt();
            Long id = Long.valueOf(ext);
            LOG.info("拒绝退款消息--------------" + domainId);
            OrderFulmtValue orderFulmtValue = orderFulmtService.checkOrder(domainId);
            if (orderFulmtValue != null) {
                orderFulmtService.updateRefundOver(domainId);
                //添加日志

                FulmtOrderOperateRecord fulmtOrderOperateRecord = new FulmtOrderOperateRecord();
                fulmtOrderOperateRecord.setFulmtOrderNum(domainId);
                fulmtOrderOperateRecord.setFromState(orderFulmtValue.getState());
                fulmtOrderOperateRecord.setEventName("拒绝退款，履约状态不变，挂起取消");
                fulmtOrderOperateRecord.setToState(11);
                fulmtOrderOperateRecord.setOperatorId(Math.toIntExact(id));
                fulmtOrderOperateRecordService.insert(fulmtOrderOperateRecord);

                //通知工单
                String routingKey = FulmtOrderRefundMqConstant.RoutingKey.FULMT_REFUSE;
                Payload payload = new Payload();
                payload.setDomainId(orderFulmtValue.getFulmtNum());
                payload.setExt(ext);
                MetaMessage message = new MetaMessage();
                message.setPayload(payload);
                message.setRoutingKey(isIpRoutingKey ? routingKey + "." + NetUtil.getLocalhost().getHostAddress() : routingKey);
                message.setExchange(FulmtOrderRefundMqConstant.EXCHANGE);
                notifyService.sendMessage(message);

            }
        }


        //取消退款
        if (OrderRefundStateEnum.CANCEL.getCode() == order.getRefundState()) {
            String ext = rabbitMetaMessage.getPayload().getExt();
            Long id = Long.valueOf(ext);
            LOG.info("退款取消消息--------------" + domainId);
            OrderFulmtValue orderFulmtValue = orderFulmtService.checkOrder(domainId);
            if (orderFulmtValue != null) {
                orderFulmtService.updateRefundOver(domainId);
                //添加日志
                FulmtOrderOperateRecord fulmtOrderOperateRecord = new FulmtOrderOperateRecord();
                fulmtOrderOperateRecord.setFulmtOrderNum(domainId);
                fulmtOrderOperateRecord.setFromState(orderFulmtValue.getState());
                fulmtOrderOperateRecord.setEventName("取消退款，履约状态不变，挂起取消");
                fulmtOrderOperateRecord.setToState(11);
                fulmtOrderOperateRecord.setOperatorId(Math.toIntExact(id));
                fulmtOrderOperateRecordService.insert(fulmtOrderOperateRecord);

            }
        }

        return Boolean.FALSE;
    }

    @Override
    public void consumerSuccess(SuccessObject successObject) {

    }

    @Override
    public boolean consumeFail(FailObject failObject) {
        return false;
    }
}
