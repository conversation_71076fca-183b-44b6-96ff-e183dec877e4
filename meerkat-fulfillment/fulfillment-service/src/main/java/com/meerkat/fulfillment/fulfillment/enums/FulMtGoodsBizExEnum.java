package com.meerkat.fulfillment.fulfillment.enums;

import com.meerkat.common.api.BizCode;

/**
 * <AUTHOR>
 */

public enum FulMtGoodsBizExEnum implements BizCode {

    /**
     * 添加履約单失败
     */
    ADD_FUL_MT_FAIL("EX_FUL_ORDER__001_01", "添加履约单失败,已存在履约单"),

    /**
     * 添加商品成功，但添加商品库存失败
     */
    ADD_FUL_MT_ORDER_FAIL("EX_FUL_ORDER__001_02", "添加履约单失败，无履约人信息"),

    /**
     * 添加商品成功，但添加商品库存失败
     */
    CHECK_FUL_MT_ORDER_FAIL("EX_FUL_ORDER__001_03", "无履约单信息"),

    /**
     * 添加商品成功，但添加商品库存失败
     */
    CHECK_ORDER_FAIL("EX_FUL_ORDER__001_04", "无订单信息"),

    /**
     * 机构确认失败
     */
    ORG_CHECK_FAIL("EX_FUL_ORDER__002_01", "机构确认失败"),

    /**
     * 机构确认参数不全
     */
    ORG_CHECK_PARAM_FAIL("EX_FUL_ORDER__002_02", "机构确认参数不全"),

    /**
     * 导出表格失败
     */
    EXPORT_EXCEL_FAIL("EX_FUL_ORDER__002_03", "导出表格失败"),

    /**
     * 参数异常
     */
    FULMT_PARAM_FAIL("EX_FUL_ORDER__002_04", "参数异常"),


    /**
     * 参数异常
     */
    FULMT_STATE_FAIL("EX_FUL_ORDER__002_05", "履约单状态已变更，请刷新页面"),

    /**
     * 参数异常
     */
    FULMT_GOODS_PRICE_NOTNULL("EX_FUL_ORDER__002_06", "履约成本价不能为null"),

    ;

    private final String code;

    private final String message;

    FulMtGoodsBizExEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
