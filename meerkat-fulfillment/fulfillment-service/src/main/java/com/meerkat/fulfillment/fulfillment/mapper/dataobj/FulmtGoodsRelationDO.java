package com.meerkat.fulfillment.fulfillment.mapper.dataobj;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 履约商品信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
@TableName("tb_fulmt_goods_relation")
public class FulmtGoodsRelationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品中心id
     */
    private Long goodsId;

    /**
     * 履约商品id
     */
    private Long fulmtGoodsId;

    /**
     * 履约机构id
     */
    private Long fulmtOrgaId;

    /**
     * 机构费用
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long orgaCosts;

    /**
     * 中介费用
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long intermediationCosts;

    /**
     * 外部销售提成
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long outSalesCommissions;

    /**
     * 疫苗价格
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long vaccinePrice;

    /**
     * 删除状态：0->未删除；1->已删除
     */
    private Integer isDeleted;

    /**
     * 提交时间
     */
    private Date gmtCreated;

    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 供货价
     */
    private Long supplyPrice;

    public Long getSupplyPrice() {
     return supplyPrice;
    }

    public void setSupplyPrice(Long supplyPrice) {
        this.supplyPrice = supplyPrice;
    }

    public Long getIntermediationCosts() {
        return intermediationCosts;
    }

    public void setIntermediationCosts(Long intermediationCosts) {
        this.intermediationCosts = intermediationCosts;
    }

    public Long getOutSalesCommissions() {
        return outSalesCommissions;
    }

    public void setOutSalesCommissions(Long outSalesCommissions) {
        this.outSalesCommissions = outSalesCommissions;
    }

    public Long getVaccinePrice() {
        return vaccinePrice;
    }

    public void setVaccinePrice(Long vaccinePrice) {
        this.vaccinePrice = vaccinePrice;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getOrgaCosts() {
        return orgaCosts;
    }

    public void setOrgaCosts(Long orgaCosts) {
        this.orgaCosts = orgaCosts;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public Long getFulmtGoodsId() {
        return fulmtGoodsId;
    }

    public void setFulmtGoodsId(Long fulmtGoodsId) {
        this.fulmtGoodsId = fulmtGoodsId;
    }

    public Long getFulmtOrgaId() {
        return fulmtOrgaId;
    }

    public void setFulmtOrgaId(Long fulmtOrgaId) {
        this.fulmtOrgaId = fulmtOrgaId;
    }


    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    @Override
    public String toString() {
        return "FulmtGoodsRelationDO{" +
                "id=" + id +
                ", goodsId=" + goodsId +
                ", fulmtGoodsId=" + fulmtGoodsId +
                ", fulmtOrgaId=" + fulmtOrgaId +
                ", orgaCosts=" + orgaCosts +
                ", intermediationCosts=" + intermediationCosts +
                ", outSalesCommissions=" + outSalesCommissions +
                ", vaccinePrice=" + vaccinePrice +
                ", isDeleted=" + isDeleted +
                ", gmtCreated=" + gmtCreated +
                ", gmtModified=" + gmtModified +
                '}';
    }
}
