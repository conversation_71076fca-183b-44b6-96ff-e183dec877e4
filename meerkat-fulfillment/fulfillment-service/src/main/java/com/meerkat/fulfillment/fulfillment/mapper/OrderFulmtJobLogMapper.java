package com.meerkat.fulfillment.fulfillment.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meerkat.fulfillment.fulfillment.mapper.dataobj.OrderFulmtJobLogDo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 履约单流程信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-24
 */
@Mapper
public interface OrderFulmtJobLogMapper extends BaseMapper<OrderFulmtJobLogDo> {

    /**
     * 查询日志列表 按时间排序
     * 
     * @param   
     * @return java.util.List<com.meerkat.fulfillment.fulfillment.mapper.dataobj.OrderFulmtJobLogDo>
     * <AUTHOR>
     * @date 2022/5/24 15:21
     */
    List<OrderFulmtJobLogDo> infoList();

}
