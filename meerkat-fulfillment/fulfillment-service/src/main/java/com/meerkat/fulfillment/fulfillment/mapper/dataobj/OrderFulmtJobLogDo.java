package com.meerkat.fulfillment.fulfillment.mapper.dataobj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 履约单流程记录
 * @date 2022/5/24 14:35
 */
@TableName("tb_order_fulmt_job_log")
public class OrderFulmtJobLogDo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 主键id
     */
    private Long id;


    /**
     * 0 进行中 1完成 2失败
     */
    private int state;


    /**
     * 订单编码
     */
    private String orderNum;


    /**
     * 履约商品id
     */
    private Long fulmtGoodsId;


    /**
     * 错误消息
     */
    private String message;


    /**
     * 数据体 方便重新构建
     */
    private String dataInfo;


    /**
     * 文件名称
     */
    private String fileName;


    /**
     * 删除状态：0->未删除；1->已删除
     */
    private int isDeleted;

    /**
     * 提交时间
     */
    private Date gmtCreated;


    /**
     * 更新时间
     */
    private Date gmtModified;


    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public int getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(int isDeleted) {
        this.isDeleted = isDeleted;
    }
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    public String getDataInfo() {
        return dataInfo;
    }

    public void setDataInfo(String dataInfo) {
        this.dataInfo = dataInfo;
    }
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
    public Long getFulmtGoodsId() {
        return fulmtGoodsId;
    }

    public void setFulmtGoodsId(Long fulmtGoodsId) {
        this.fulmtGoodsId = fulmtGoodsId;
    }
    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }
    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }
}
