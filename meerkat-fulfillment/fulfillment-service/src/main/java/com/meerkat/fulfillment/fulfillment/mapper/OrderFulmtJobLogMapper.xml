<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.fulfillment.fulfillment.mapper.OrderFulmtJobLogMapper">
    <sql id="Base_Column_List">
        id,
                state,
                order_num,
                fulmt_goods_id,
                message,
                data_info,
                file_name,
                is_deleted,
                gmt_created,
                gmt_modified
    </sql>
    <select id="infoList" resultType="com.meerkat.fulfillment.fulfillment.mapper.dataobj.OrderFulmtJobLogDo">
        SELECT <include refid="Base_Column_List" />
        FROM tb_order_fulmt_job_log
        order by gmt_created desc limit 10
    </select>

</mapper>
