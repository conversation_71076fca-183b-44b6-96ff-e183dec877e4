package com.meerkat.fulfillment.fulfillment.listener;

import com.meerkat.notify.consumer.RabbitConsumerProConfig;
import com.meerkat.notify.listener.AbstractMessageListener;
import com.meerkat.order.enums.MqOrderImportConstants;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @createTime 2022/5/23 10:08
 */
@Configuration
public class OpsOrderRefundConsumerProConfig extends RabbitConsumerProConfig {

    private final String QUEUE_NAME = "middlePlatform.order.import.queue";
    @Autowired
    private OpsOrderRefundMessageListener opsOrderRefundMessageListener;

    @Override
    public String queueName() {
        return QUEUE_NAME;
    }

    @Override
    public AbstractMessageListener messageListener() {
        return opsOrderRefundMessageListener;
    }

    @Override
    public String topicExchangeName() {
        return MqOrderImportConstants.EXCHANGE;
    }

    @Override
    public List<String> routingKeys() {
        return List.of(
                MqOrderImportConstants.RoutingKey.REFUND_REFUNDING,
                MqOrderImportConstants.RoutingKey.REFUND_REFUNDED,
                MqOrderImportConstants.RoutingKey.REFUND_REFUSE,
                MqOrderImportConstants.RoutingKey.REFUND_CANCEL);
    }

    @Override
    public Exchange exchange() {
         return new TopicExchange(topicExchangeName());
    }

}
