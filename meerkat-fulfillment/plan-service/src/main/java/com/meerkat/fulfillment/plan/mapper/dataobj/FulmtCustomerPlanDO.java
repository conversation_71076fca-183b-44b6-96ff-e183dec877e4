package com.meerkat.fulfillment.plan.mapper.dataobj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 客户计划任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
@TableName("tb_fulmt_customer_plan")
public class FulmtCustomerPlanDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户组客户计划id
     */
    private Long customerTemplatePlanId;

    /**
     * 客户套餐id
     */
    private Long customerPkgPlanId;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 类型 1患者分组 2客户分组
     */
    private Integer customerType;

    /**
     * 客户其他信息
     */
    private String customerInfo;

    /**
     * 客户组id
     */
    private String groupId;

    /**
     * 执行时间
     */
    private Date executeTime;

    /**
     * 执行时间int
     */
    private Long executeTimeInt;

    /**
     * 计划名称
     */
    private String name;

    /**
     * 介绍
     */
    private String introduce;

    /**
     * 触达方式 -1 人工 0 企业微信
     */
    private Integer reachWay;

    /**
     * 触达类型 0 文本 1 链接 2 图片
     */
    private Integer reachType;

    /**
     * 触达内容
     */
    private String reachContent;

    /**
     * 干预人
     */
    private Long interveneId;

    /**
     * 干预负责人
     */
    private Long interveneHeadId;

    /**
     * 是否人工干预 0 自动 1 人工
     */
    private Integer isHumanIntervention;

    /**
     * 执行状态 0未执行 1执行中 2执行成功 3执行失败
     */
    private Integer executeStatus;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 1启用2停用
     */
    private Integer status;

    /**
     * 审核状态 0待审核 1审核成功 2审核失败
     */
    private Integer checkStatus;

    private Date gmtCreated;

    private Date gmtModified;

    /**
     * 是否删除 1是0否
     */
    private Integer isDeleted;

    /**
     * 停用时间
     */
    private Date stopDate;

    /**
     * 是否修改 1是0否
     */
    private Integer isUpdated;

    /**
     * 机构id
     */
    private Long organizationId;

    /**
     * 备注
     */
    private String remark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCustomerTemplatePlanId() {
        return customerTemplatePlanId;
    }

    public void setCustomerTemplatePlanId(Long customerTemplatePlanId) {
        this.customerTemplatePlanId = customerTemplatePlanId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getCustomerInfo() {
        return customerInfo;
    }

    public void setCustomerInfo(String customerInfo) {
        this.customerInfo = customerInfo;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public Date getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Date executeTime) {
        this.executeTime = executeTime;
    }

    public Long getExecuteTimeInt() {
        return executeTimeInt;
    }

    public void setExecuteTimeInt(Long executeTimeInt) {
        this.executeTimeInt = executeTimeInt;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIntroduce() {
        return introduce;
    }

    public void setIntroduce(String introduce) {
        this.introduce = introduce;
    }

    public Integer getReachWay() {
        return reachWay;
    }

    public void setReachWay(Integer reachWay) {
        this.reachWay = reachWay;
    }

    public Integer getReachType() {
        return reachType;
    }

    public void setReachType(Integer reachType) {
        this.reachType = reachType;
    }

    public String getReachContent() {
        return reachContent;
    }

    public void setReachContent(String reachContent) {
        this.reachContent = reachContent;
    }

    public Long getInterveneId() {
        return interveneId;
    }

    public void setInterveneId(Long interveneId) {
        this.interveneId = interveneId;
    }

    public Long getInterveneHeadId() {
        return interveneHeadId;
    }

    public void setInterveneHeadId(Long interveneHeadId) {
        this.interveneHeadId = interveneHeadId;
    }

    public Integer getExecuteStatus() {
        return executeStatus;
    }

    public void setExecuteStatus(Integer executeStatus) {
        this.executeStatus = executeStatus;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getStopDate() {
        return stopDate;
    }

    public void setStopDate(Date stopDate) {
        this.stopDate = stopDate;
    }

    public Integer getIsUpdated() {
        return isUpdated;
    }

    public void setIsUpdated(Integer isUpdated) {
        this.isUpdated = isUpdated;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public Integer getIsHumanIntervention() {
        return isHumanIntervention;
    }

    public void setIsHumanIntervention(Integer isHumanIntervention) {
        this.isHumanIntervention = isHumanIntervention;
    }

    public Long getCustomerPkgPlanId() {
        return customerPkgPlanId;
    }

    public void setCustomerPkgPlanId(Long customerPkgPlanId) {
        this.customerPkgPlanId = customerPkgPlanId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
