package com.meerkat.hm.enums;

/**
 * @program meerkat-origin
 * @description: 健管权益类型枚举
 * @author: fan<PERSON><PERSON>
 * @create: 2022/08/31 16:31
 */
public enum HMPrivilegeServiceTypeEnum {
    ONE_TO_ONE_SERVICE(1, "1对1服务"),
    COMMUNITY_SERVICE(2, "社群服务"),

    ;

    private Integer code;

    private String name;

    HMPrivilegeServiceTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
