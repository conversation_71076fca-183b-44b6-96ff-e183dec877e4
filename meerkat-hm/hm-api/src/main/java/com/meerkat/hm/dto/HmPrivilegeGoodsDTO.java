package com.meerkat.hm.dto;

import com.meerkat.hm.model.HmPrivilege;

import java.io.Serializable;
import java.util.List;

/**
 * @program meerkat-origin
 * @description: 权益商品dto对象
 * @author: fandongdong
 * @create: 2022/08/31 15:22
 */

public class HmPrivilegeGoodsDTO implements Serializable {

    private static final long serialVersionUID = -8368658739436248046L;

    /**
     * 权益信息
     */
    private HmPrivilege hmPrivilege;
    /**
     * 权益关联商品信息
     */
    private List<HmPrivilegeGoodsRelationDTO> hmPrivilegeGoodsRelationDTOList;

    public HmPrivilege getHmPrivilege() {
        return hmPrivilege;
    }

    public void setHmPrivilege(HmPrivilege hmPrivilege) {
        this.hmPrivilege = hmPrivilege;
    }

    public List<HmPrivilegeGoodsRelationDTO> getHmPrivilegeGoodsRelationDTOList() {
        return hmPrivilegeGoodsRelationDTOList;
    }

    public void setHmPrivilegeGoodsRelationDTOList(List<HmPrivilegeGoodsRelationDTO> hmPrivilegeGoodsRelationDTOList) {
        this.hmPrivilegeGoodsRelationDTOList = hmPrivilegeGoodsRelationDTOList;
    }
}
