package com.meerkat.hm.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @program meerkat-origin
 * @description: 用户权益dto对象
 * @author: fandongdong
 * @create: 2022/08/31 15:22
 */
public class HmUserPrivilegeDTO implements Serializable {

    private static final long serialVersionUID = -1884166170815826317L;

    private Long id;
    /**
     * 用户id（主账号）
     */
    private Long userId;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 来源
     *
     * @see com.meerkat.hm.enums.HMPrivilegeTypeEnum
     */
    private Integer source;
    /**
     * 对应来源id
     */
    private String objId;

    /**
     * 权益商品关系表
     */
    private List<HmPrivilegeGoodsDTO> hmPrivilegeGoodsDTOList;

    /**
     * 用户权益状态
     *
     * @see com.meerkat.hm.enums.HMUserPrivilegeStatusEnum
     */
    private Integer status;

    private Date startTime;

    private Date endTime;

    public List<HmPrivilegeGoodsDTO> getHmPrivilegeGoodsDTOList() {
        return hmPrivilegeGoodsDTOList;
    }

    public void setHmPrivilegeGoodsDTOList(List<HmPrivilegeGoodsDTO> hmPrivilegeGoodsDTOList) {
        this.hmPrivilegeGoodsDTOList = hmPrivilegeGoodsDTOList;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getObjId() {
        return objId;
    }

    public void setObjId(String objId) {
        this.objId = objId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
}