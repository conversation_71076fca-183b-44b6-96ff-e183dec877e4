package com.meerkat.hm.param;

import java.io.Serializable;

/**
 * @program meerkat-origin
 * @description:
 * @author: fandongdong
 * @create: 2022/09/02 17:39
 */
public class HmFileRecordEdit implements Serializable {
    private Long userId;
    private String name;
    private String url;

    private String userMobile;
    private String userNickName;

    public String getUserMobile() {
        return userMobile;
    }

    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
    }

    public String getUserNickName() {
        return userNickName;
    }

    public void setUserNickName(String userNickName) {
        this.userNickName = userNickName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}

