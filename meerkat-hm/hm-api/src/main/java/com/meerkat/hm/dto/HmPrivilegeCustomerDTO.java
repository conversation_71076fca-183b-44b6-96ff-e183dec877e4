package com.meerkat.hm.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @program meerkat-origin
 * @description: 用户权益传输对象
 * @author: fandongdong
 * @create: 2022/08/31 15:22
 */

public class HmPrivilegeCustomerDTO implements Serializable {
    private static final long serialVersionUID = -6298933918998392506L;

    /**
     * 待领取权益
     */
    private List<HmUserPrivilegeDTO> pendingList;
    /**
     * 进行中权益
     */
    private List<HmUserPrivilegeDTO> processingList;
    /**
     * 已完成权益
     */
    private List<HmUserPrivilegeDTO> finishedList;

    public List<HmUserPrivilegeDTO> getPendingList() {
        return pendingList;
    }

    public void setPendingList(List<HmUserPrivilegeDTO> pendingList) {
        this.pendingList = pendingList;
    }

    public List<HmUserPrivilegeDTO> getProcessingList() {
        return processingList;
    }

    public void setProcessingList(List<HmUserPrivilegeDTO> processingList) {
        this.processingList = processingList;
    }

    public List<HmUserPrivilegeDTO> getFinishedList() {
        return finishedList;
    }

    public void setFinishedList(List<HmUserPrivilegeDTO> finishedList) {
        this.finishedList = finishedList;
    }
}
