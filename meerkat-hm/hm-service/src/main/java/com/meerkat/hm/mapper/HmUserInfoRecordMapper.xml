<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meerkat.hm.mapper.HmUserInfoRecordMapper">
  <resultMap id="BaseResultMap" type="com.meerkat.hm.model.HmUserInfoRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="val" jdbcType="VARCHAR" property="val" />
    <result column="create_id" jdbcType="BIGINT" property="createId" />
    <result column="modified_id" jdbcType="BIGINT" property="modifiedId" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, type, val, create_id, modified_id, is_deleted, gmt_created, gmt_modified
  </sql>
  <select id="selectByExample" parameterType="com.meerkat.hm.model.HmUserInfoRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_hm_user_info_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_hm_user_info_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tb_hm_user_info_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meerkat.hm.model.HmUserInfoRecordExample">
    delete from tb_hm_user_info_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meerkat.hm.model.HmUserInfoRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tb_hm_user_info_record (user_id, type, val, 
      create_id, modified_id, is_deleted, 
      gmt_created, gmt_modified)
    values (#{userId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, #{val,jdbcType=VARCHAR}, 
      #{createId,jdbcType=BIGINT}, #{modifiedId,jdbcType=BIGINT}, #{isDeleted,jdbcType=BIT}, 
      #{gmtCreated,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.meerkat.hm.model.HmUserInfoRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tb_hm_user_info_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="val != null">
        val,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="modifiedId != null">
        modified_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="gmtCreated != null">
        gmt_created,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="val != null">
        #{val,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=BIGINT},
      </if>
      <if test="modifiedId != null">
        #{modifiedId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="gmtCreated != null">
        #{gmtCreated,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meerkat.hm.model.HmUserInfoRecordExample" resultType="java.lang.Long">
    select count(*) from tb_hm_user_info_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tb_hm_user_info_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.val != null">
        val = #{record.val,jdbcType=VARCHAR},
      </if>
      <if test="record.createId != null">
        create_id = #{record.createId,jdbcType=BIGINT},
      </if>
      <if test="record.modifiedId != null">
        modified_id = #{record.modifiedId,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.gmtCreated != null">
        gmt_created = #{record.gmtCreated,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tb_hm_user_info_record
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      type = #{record.type,jdbcType=INTEGER},
      val = #{record.val,jdbcType=VARCHAR},
      create_id = #{record.createId,jdbcType=BIGINT},
      modified_id = #{record.modifiedId,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      gmt_created = #{record.gmtCreated,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meerkat.hm.model.HmUserInfoRecord">
    update tb_hm_user_info_record
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="val != null">
        val = #{val,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=BIGINT},
      </if>
      <if test="modifiedId != null">
        modified_id = #{modifiedId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="gmtCreated != null">
        gmt_created = #{gmtCreated,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meerkat.hm.model.HmUserInfoRecord">
    update tb_hm_user_info_record
    set user_id = #{userId,jdbcType=BIGINT},
      type = #{type,jdbcType=INTEGER},
      val = #{val,jdbcType=VARCHAR},
      create_id = #{createId,jdbcType=BIGINT},
      modified_id = #{modifiedId,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=BIT},
      gmt_created = #{gmtCreated,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>