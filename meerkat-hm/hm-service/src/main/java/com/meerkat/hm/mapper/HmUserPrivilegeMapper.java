package com.meerkat.hm.mapper;

import com.meerkat.hm.dao.HmUserPrivilegeDO;
import com.meerkat.hm.param.HmUserPrivilegeDOQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HmUserPrivilegeMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(HmUserPrivilegeDO record);

    HmUserPrivilegeDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(HmUserPrivilegeDO record);

    List<HmUserPrivilegeDO> selectByQuery(@Param("query") HmUserPrivilegeDOQuery query);

    int batchCloseHmUserPrivilege(@Param("ids") List<Long> ids);


}