package com.meerkat.hm.mapper;

import com.meerkat.hm.model.HmFileRecord;
import com.meerkat.hm.model.HmFileRecordExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HmFileRecordMapper {
    long countByExample(HmFileRecordExample example);

    int deleteByExample(HmFileRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(HmFileRecord record);

    int insertSelective(HmFileRecord record);

    List<HmFileRecord> selectByExample(HmFileRecordExample example);

    HmFileRecord selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") HmFileRecord record, @Param("example") HmFileRecordExample example);

    int updateByExample(@Param("record") HmFileRecord record, @Param("example") HmFileRecordExample example);

    int updateByPrimaryKeySelective(HmFileRecord record);

    int updateByPrimaryKey(HmFileRecord record);
}